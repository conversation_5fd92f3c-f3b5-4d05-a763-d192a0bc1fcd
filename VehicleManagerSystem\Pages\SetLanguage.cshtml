﻿@page 
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.SetLanguageModel
@{
    bool is_switch = false;
    string originLanguage="", newLanguage = "";
    if (!string.IsNullOrWhiteSpace(Model.Language))
    {
          originLanguage = language["LANGUAGE_NAME"];
          Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(Model.Language);
          newLanguage= language["LANGUAGE_NAME"];
        is_switch=true;
    } 
   
}
@section Scripts{
    @if (is_switch) {
        <script>
            ui.loading(true,"@Html.Raw(originLanguage) -> @Html.Raw(newLanguage)");
        </script>
        if (!string.IsNullOrWhiteSpace(Model.Source)){
            <script>
                    setTimeout(function(){
                    location.href="@Model.Source";
                },500)
            </script>
        }
    }else if(!string.IsNullOrWhiteSpace(Model.Source)){
        
        <script>
            location.href = "@Model.Source";
        </script>
    }
}