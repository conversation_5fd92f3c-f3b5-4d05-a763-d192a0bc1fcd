plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.scvdos.vds.ground.module.tcp'
    compileSdk 34

    defaultConfig {
        applicationId "com.scvdos.vds.ground.module.tcp"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.mqtt

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    compileOnly files('E:/Code-android/libs/groundapi-release.aar' )
    compileOnly files('E:/Code-android/libs/messages-release.aar' )
}