﻿@keyframes vui-aim-arc {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes vui-aim-dashed {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.95);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes vui-aim-circular-min {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.8);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes vui-aim-point {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.6);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes vui-aim-cross {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

.vui-aim {
    height: 400px;
    width: 400px;
    position: relative;
    box-sizing: border-box;
    opacity: 0.8;
}

.vui-aim > .vui-aim-arc {
    box-sizing: border-box;
    position: absolute;
    height: 100%;
    width: 100%;
    border: 2px solid #EEE;
    left: 0;
    top: 0;
    border-radius: 50%;
}

.vui-aim > .vui-aim-circular {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}

.vui-aim > .vui-aim-circular > .vui-aim-circular-in {
    animation-name: vui-aim-arc;
    animation-duration: 4s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    height: 90%;
    width: 90%;
    border: 6px solid #eee;
    border-radius: 50%;
    box-sizing: border-box;
    clip-path: polygon(0 0,50% 0,50% 50%,100% 50%,100% 100%,50% 100%,50% 50%,0 50%);
}

.vui-aim > .vui-aim-dashed {
    box-sizing: border-box;
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.vui-aim > .vui-aim-dashed > .vui-aim-dashed-in {
    animation-name: vui-aim-dashed;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    height: 80%;
    width: 80%;
    border: 2px dotted #eee;
    border-radius: 50%;
    box-sizing: border-box;
}

.vui-aim > .vui-aim-circular-min {
    box-sizing: border-box;
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.vui-aim > .vui-aim-circular-min > .vui-aim-circular-min-in {
    animation-name: vui-aim-circular-min;
    animation-duration: 1.8s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    height: 50%;
    width: 50%;
    border: 2px solid #eee;
    border-radius: 50%;
    box-sizing: border-box;
}

.vui-aim > .vui-aim-point {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}

.vui-aim > .vui-aim-point > .vui-aim-point-in {
    height: 15%;
    width: 15%;
    border-radius: 50%;
    box-sizing: border-box;
    background: #fff;
    position: absolute;
    animation-name: vui-aim-point;
    animation-duration: 1.4s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.vui-aim > .vui-aim-cross {
    height: 100%;
    width: 100%;
    animation-name: vui-aim-cross;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.vui-aim > .vui-aim-cross > .vui-aim-cross-v {
    position: absolute;
    border-right: 2px dashed #eee;
    height: 100%;
    width: 50%;
    top: 0;
    left: 0;
    clip-path: polygon(90% 0,90% 100%,110% 100%,110% 60%,95% 60%,95% 40%,110% 40%,110% 0);
}

.vui-aim > .vui-aim-cross > .vui-aim-cross-h {
    position: absolute;
    border-bottom: 2px dashed #eee;
    height: 50%;
    width: 100%;
    top: 0;
    left: 0;
    clip-path: polygon(0 90%,0 110%,40% 110%,40% 95%,60% 95%,60% 110%,100% 110%,100% 90%);
}

.aim-show {
    animation: aim-init 0.4s linear;
}

.aim-show > .vui-aim-dashed > .vui-aim-dashed-in {
    animation: aim-accle-dot 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.aim-show > .vui-aim-circular > .vui-aim-circular-in {
    animation: aim-accle 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
.hideani {
    animation: aim-remove 0.6s linear;
}
@keyframes aim-remove {
    0% {
        transform: scale(1);
        opacity: 0.8;
    } 
    100% {
        transform: scale(20);
        opacity: 0;
    }
}
@keyframes aim-init {
    0% {
        transform: scale(10);
        opacity: 0.2;
    }

    50% {
        transform: scale(5);
        opacity: 0.6;
    }

    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

@keyframes aim-accle {
    0% {
        transform: scale(10);
    }

    60% {
        transform: scale(0.8);
    }


    100% {
        transform: scale(1);
    }
}

@keyframes aim-accle-dot {
    0% {
        transform: scale(10);
    }

    60% {
        transform: scale(0.8);
    }

    100% {
        transform: scale(1);
    }
}
