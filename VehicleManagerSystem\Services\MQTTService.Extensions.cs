﻿using System.Reflection;

namespace VehicleManagerSystem.Services
{
    public partial class MQTTService
    {
        private async Task<string> JsonAsync(object obj)
        {
            return await Task.Run(() => Newtonsoft.Json.JsonConvert.SerializeObject(obj));
        }
        private string Json(object obj)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(obj);
        }
    }
}
