﻿$(function (api) {
    api.bridge = function (url, progress, success,error,loadingText){
        var _loading = ui.loading(true,loadingText);
        $.ajax({
            url: url, 
            type: "get",
            dataType: "html",
            xhr: function () {
                let xhr = new window.XMLHttpRequest();
                xhr.onprogress = function (e) {
                    if (e.lengthComputable) {
                        progress(Math.round(progress * 100));
                    }
                };
                return xhr;
            },  
            headers: {
                "token": localStorage.getItem("token"),
                "uid": localStorage.getItem("uid"),
            },
            success: function (html) {
                _loading.close();
                success(html);
            },
            error: function (e) {
                _loading.close();
                error(e)
            }
        }); 
    }
    api.post = function (url, data, callback, showloading, showdialog) {
        var _loading = null;
        if (showloading) {
            _loading = ui.loading();
        }
        console.log(data);
       
        $.ajax({
            url: url,
            data: JSON.stringify(data),
            type: "post",
            dataType: "json",
            contentType: "application/json",
            headers: {
                "token": localStorage.getItem("token"),
                "uid": localStorage.getItem("uid"),
            },
            success: function (res) {
                if (_loading != null) _loading.close();
                if (!res.success) {
                    if (showdialog) {
                        ui.alert(res.message);
                    }
                }
                callback(res);
            },
            error: function () { 
                if (_loading != null) {
                     _loading.close();
                }
                if (showdialog) {
                    ui.alert("网络错误");
                }
            }
        });
    };
    api.get = function (url, query, callback, showloading, showdialog) {
        var _loading = null;
        if (showloading) {
            _loading = ui.loading();
        }
        console.log(_loading);
        $.ajax({
            url: url,
            data: query,
            type: "get",
            success: function (res) {
                if (_loading != null) _loading.remove();
                if (!res.success) {
                    if (showdialog) {
                        ui.alert(res.message);
                    }
                }
                callback(res);
            },
            error: function () {
                console.log(_loading);
                if (_loading != null) _loading.remove();
                if (showdialog) {
                    ui.alert("网络错误");
                }
            }
        });
    }
    api.login = function (username, password, callback, showloading, showdialog) {
        api.post("/api/account/login", { username: username, password: password }, callback, showloading, showdialog);
    }
    api.addVehicle = function (serial, displayName, callback) {
        api.post("/api/vehicle/add", { serial: serial, name: displayName }, callback, true, true);
    }
    window.api = api;
}(window.api || {}));