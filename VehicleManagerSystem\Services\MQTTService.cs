﻿using System.Reflection;
using System.Text;
using Azure;
using Microsoft.AspNetCore.SignalR;
using MQTTnet;
using MQTTnet.Extensions.TopicTemplate;
using VehicleManagerSystem.Tables;
using static VehicleManagerSystem.Services.MQTTService;

namespace VehicleManagerSystem.Services
{
    public partial class MQTTService
    {
        ILogger<MQTTService> logger;
        private MqttClientFactory mqtt_factory = new();
        private IMqttClient mqtt_client = null!;
        private volatile bool isRunning = false;
        private DataBaseService database;
        private static Dictionary<string, ICommandExecuter> mSubscribe = [];
        private ICommandExecuter vehicleResult = null!;
        private IHubContext<VehicleRealTime> realTimeService;
        public MQTTService(DataBaseService _database,IHubContext<VehicleRealTime> hubContext,ILogger<MQTTService> _logger)
        {
            logger = _logger;
            realTimeService = hubContext;
            database = _database;
            vehicleResult = TOPIC.TOPIC_VEHICLE_RESULT.GetCommand<ResultCommand>(OnVehicleResult);
            mSubscribe.Add(TOPIC.TOPIC_VEHICLE_HEARTBEAT.Topic,TOPIC.TOPIC_VEHICLE_HEARTBEAT.GetCommand<HeartbeatCommand>(OnHeartbeat));
        } 
        public async Task StartAsync(CancellationToken token)
        {
            if (isRunning) return;
            isRunning = true;
            await LoopConnectAsync();
        }
        public async Task StopAsync(CancellationToken token)
        {
            isRunning = false;
            if (mqtt_client != null)
            {
                await mqtt_client.TryDisconnectAsync();
            }
        }
        public async void Subscribe(TableVehicle vehicle)
        {
            foreach (var topic in mSubscribe) { 
               await mqtt_client.SubscribeAsync(mqtt_factory.CreateSubscribeOptionsBuilder()
                            .WithTopicTemplate(topic.Value.WithParameter("vehicle_sn", vehicle.VehicleSerial))
                            .Build());
            }
         
        }
         public async void Subscribe(string vehicleId,string clientId)
        {

            var topic = vehicleResult.WithParameter("vehicle_sn", vehicleId)
                                                            .WithParameter("client_sn", clientId).TopicFilter;
               await mqtt_client.SubscribeAsync(mqtt_factory.CreateSubscribeOptionsBuilder()
                            .WithTopicTemplate(vehicleResult.WithParameter("vehicle_sn", vehicleId)
                                                            .WithParameter("client_sn",clientId))
                            .Build());
          
         
        }
        public async void UnSubscribe(string vehicleId,string clientId)
        {
         
               await mqtt_client.UnsubscribeAsync(
                   vehicleResult.WithParameter("vehicle_sn", vehicleId)
                                .WithParameter("client_sn",clientId).TopicFilter);
          
         
        }
        public async void UnSubscribe(TableVehicle vehicle)
        {
            foreach (var topic in mSubscribe) {
                await mqtt_client.UnsubscribeAsync(topic.Value.WithParameter("vehicle_sn", vehicle.VehicleSerial).TopicFilter);
            }
        }
        private async Task LoopConnectAsync()
        {
            try
            {
                mqtt_client = mqtt_factory.CreateMqttClient();
                mqtt_client.DisconnectedAsync += OnDisconnectedAsync;
                mqtt_client.ConnectedAsync += OnConnectedAsync;
                mqtt_client.ApplicationMessageReceivedAsync += ApplicationMessageReceivedAsync;
                await mqtt_client.ConnectAsync(new MqttClientOptionsBuilder()
                    .WithTcpServer("127.0.0.1", 11883)
                    .WithCredentials("#LOCAL", "#PASSWORD")
                    .Build(), CancellationToken.None);
            }
            catch(Exception e) 
            {

            }
        }
        private async Task OnConnectedAsync(MqttClientConnectedEventArgs arg)
        {
            Console.WriteLine("MQTT连接成功");
            if (arg.ConnectResult.ResultCode == MqttClientConnectResultCode.Success)
            {
                var vehicleList = await database.SqlExecuteAsync(async sql =>
                {
                    return await sql.Queryable<TableVehicle>().ToListAsync();

                });
                if (vehicleList != null)
                {
                    foreach (var vehicle in vehicleList)
                    {
                        Subscribe(vehicle);
                    }
                }
            }
        }
        private async Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs arg)
        {

            mqtt_client.DisconnectedAsync -= OnDisconnectedAsync;
            mqtt_client.ConnectedAsync -= OnConnectedAsync;
            mqtt_client.ApplicationMessageReceivedAsync -= ApplicationMessageReceivedAsync;
            await mqtt_client.DisconnectAsync();
            if (isRunning)
            {
                _ = LoopConnectAsync();
            }
        } 
        private async Task ApplicationMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs arg)
        {
            logger.LogError(arg.ApplicationMessage.Topic);
            if (!string.IsNullOrWhiteSpace(arg.ApplicationMessage.Topic) && arg.ApplicationMessage.Topic.Contains("/message/"))
            {
              
                 int last_n = arg.ApplicationMessage.Topic.IndexOf("/message/") ;
                 if (last_n == -1) return;
                 var topic = arg.ApplicationMessage.Topic[..(last_n + 9)];
                 var client_id= arg.ApplicationMessage.UserProperties.Where(it => it.Name == "clientId").FirstOrDefault();
                if (client_id != null && client_id.Value!=null)
                {
                    if (mSubscribe.TryGetValue(topic, out var subscribe))
                    {
                        subscribe.ExecuteAsync(Encoding.UTF8.GetString(arg.ApplicationMessage.Payload), client_id.Value);
                    }
                }
                ///vehicle/control/response/VDB00ZY2500TW50000AF9000001/1TmMCZZSUgi-RWD-OWXUqg
            }else if (!string.IsNullOrWhiteSpace(arg.ApplicationMessage.Topic) && arg.ApplicationMessage.Topic.Contains("/response/"))
            {///vehicle/control/response/
              
                 int last_n = arg.ApplicationMessage.Topic.IndexOf("/response/") ;
                 if (last_n == -1) return;
                 var topic = arg.ApplicationMessage.Topic[..(last_n + 10)];
                 var client_id= arg.ApplicationMessage.UserProperties.Where(it => it.Name == "clientId").FirstOrDefault();
                if (client_id != null && client_id.Value!=null)
                { 
                   vehicleResult.ExecuteAsync(Encoding.UTF8.GetString(arg.ApplicationMessage.Payload), client_id.Value);
                }///vehicle/control/response/VDB00ZY2500TW50000AF9000001/1TmMCZZSUgi-RWD-OWXUqg
            }
            await Task.CompletedTask;
        } 
    }
}
