﻿@page
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.BridgeModel
@{
	Layout = null;
	var language_list = @language.GetAllStrings(true);
}
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>@language["UI_LOADING"]</title>
	<link href="~/css/ui.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/font/weilai/weilai.css" rel="stylesheet" />
	<link rel="stylesheet" href="~/VehicleManagerSystem.styles.css" asp-append-version="true" />
</head>
<body>
	<page>
		<div id="progress">
			<div id="progress-value"></div>
		</div>
		<div class="loading"></div>
	</page>
	<script src="~/lib/jquery/dist/jquery.min.js"></script>
	<script src="~/js/ui.js" asp-append-version="true"></script>
	<script src="~/js/api.js" asp-append-version="true"></script>
	<script type="text/javascript">
		window.constants={};
		@foreach (var item in language_list)
		{
			@Html.Raw($"\t\twindow.constants.{item.Name}=\"{item.Value}\";\n") 
		}
	</script>
	@if (string.IsNullOrWhiteSpace(Model.Target))
	{
		<script type="text/javascript">
			ui.alert(window.constants.ERROR_PAGE_NOT_FOUND,function(){return true;});
			document.title = "404:"+window.constants.ERROR_PAGE_NOT_FOUND;
		</script>
	}
	else
	{
		<script type="text/javascript">
			var target_url="@Html.Raw(Model.Target)";
			var begin_load=function(){
				api.bridge(target_url,function(progress){
					$("#progress-value").width(progress + "%");
				},function(html){
						document.open();
						document.write(html);
						document.close();
						window.parent.postMessage({
						  message: 'update_title'
						}, '*');
				}, function (e) {
					$("#progress-value").width("0%");
					switch (e.status) {
						case 401:
						case 403: {
							localStorage.removeItem("token");
							ui.alert(window.constants.ERROR_PERMISSION_ERROR,function(){
								top.location.href="/login";
							});
							break;
						}
						case 404: {
							document.title = window.constants.ERROR_PAGE_NOT_FOUND;
							ui.alert(window.constants.ERROR_PAGE_NOT_FOUND ,()=>true);
							break;
						}
						case 500: {
							document.title =window.constants.ERROR_SERVER_ERROR;
							ui.alert(window.constants.ERROR_SERVER_ERROR,()=>true);
							break;
						}
						default: {
							document.title =window.constants.ERROR_UNKNOW ;
							ui.alert(window.constants.ERROR_UNKNOW,()=>true);
							break;
						}
					}

					window.parent.postMessage({
						  message: 'update_title'
					}, '*');
				},window.constants.UI_LOADING);
			} 
		 	begin_load();
		</script>
	}

</body>
</html> 