﻿using System.Reflection;
using Microsoft.Extensions.Logging;
using SqlSugar;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Services
{
    public partial class DataBaseService(ILogger<DataBaseService> logger)
    {
        public const string ServerAddress = "127.0.0.1";
        public const int ServerPort = 3306;
        public const string DataBaseName = "VehicleManagerSystem";
        public const string DataBaseUserName = "center";
        public const string DataBasePassword = "123456";
  
        public  async Task<T> SqlExecuteAsync<T>(Func<SqlSugar.SqlSugarClient, Task<T>> func)
        {
            return await func(new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = $"server={ServerAddress};port={ServerPort};database={DataBaseName};uid={DataBaseUserName};pwd={DataBasePassword};", // 连接字符串
                DbType = DbType.MySql,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
            }));
        }
        public  async Task<T> SqlExecuteNoneDataBaseAsync<T>(Func<SqlSugar.SqlSugarClient, Task<T>> func)
        {
            return await func(new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = $"server={ServerAddress};port={ServerPort};database={DataBaseName};uid={DataBaseUserName};pwd={DataBasePassword};", // 连接字符串
                DbType = DbType.MySql,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
            }));
        }
        public async Task<bool> StopAsync()
        {
            if (m_cancel_source != null)
            {
                await m_cancel_source.CancelAsync();
                m_cancel_event.Set();
            }
            return true;
        }

        private CancellationTokenSource m_cancel_source = null!;
        private ManualResetEvent m_cancel_event=new ManualResetEvent(false);
        public async Task<bool> StartAsync() {
            logger.LogInformation("{name}", "初始化数据库"); 
            if (await SqlExecuteNoneDataBaseAsync(async (sql) => await Task.FromResult(sql.DbMaintenance.CreateDatabase(DataBaseName))))
            {
                var tables=  Assembly.GetAssembly(typeof(ITable))?.GetTypes().Where(it => typeof(ITable).IsAssignableFrom(it) && !typeof(ITable).Equals(it)).ToArray();
                if (await SqlExecuteAsync(async (sql) =>
                {
                    logger.LogInformation("{name}", "正在初始化数据表");
                    sql.CodeFirst.InitTables(tables?.ToArray());
                    return await InitializeAsync(sql);
                })) {
                    //m_cancel_source=new CancellationTokenSource();
                    //ThreadPool.QueueUserWorkItem(DataBaseProcess, m_cancel_source.Token,false);
                    return true;
                }
            }
            
            return false;
        }
        private void DataBaseProcess(CancellationToken token)
        {
            while (!token.IsCancellationRequested) {

                //SqlExecuteAsync(async sql =>sql.Deleteable<TableUserToken>().Where(it=>it.LoginTime))


                try
                {
                    m_cancel_event.WaitOne(1000 * 60 * 10);
                }
                catch  { 
                
                } 
            }
        }

    }

   
}
