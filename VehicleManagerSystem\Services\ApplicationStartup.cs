﻿
namespace VehicleManagerSystem.Services
{
    public class ApplicationStartup(DataBaseService database,
        MQTTServer mqttServer,
        MQTTService mqttService,
        IHostApplicationLifetime appLifetime) : IHostedService
    {
        public async Task StartAsync(CancellationToken cancellationToken)
        {
           if(!await database.StartAsync()){
                appLifetime.StopApplication();
           }
           await mqttServer.StartAsync(cancellationToken);
           await mqttService.StartAsync(cancellationToken);
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            await mqttService.StopAsync(cancellationToken);
            await mqttServer.StopAsync(cancellationToken);
            await database.StopAsync();
        }
    }
}
