﻿using Microsoft.AspNetCore.SignalR;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Services
{
 
    public class VehicleRealTime (DataBaseService database,MQTTService mqtt): Hub
    {
        public class ControlAuthorizedInfo
        {
            public string VehicleId { get; set; }
        }
        public static System.Collections.Concurrent.ConcurrentDictionary<string, ControlAuthorizedInfo> m_authorizeds = new();
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            if(m_authorizeds.TryRemove(Context.ConnectionId, out var vehicle))
            {
                mqtt.UnSubscribe(vehicle.VehicleId, Context.ConnectionId);
            }
            await base.OnDisconnectedAsync(exception);
        }
        public override async Task OnConnectedAsync()
         { 
              
             var httpContext = Context.GetHttpContext();
            if (httpContext != null)
            {
                var channel = httpContext.Request.Query["channel"].FirstOrDefault();
                var user_id = httpContext.Request.Query["uid"].FirstOrDefault();
                var token = httpContext.Request.Query["token"].FirstOrDefault();
                var vehicle_id = httpContext.Request.Query["vehicle_id"].FirstOrDefault();
                if (string.IsNullOrWhiteSpace(vehicle_id) || string.IsNullOrWhiteSpace(user_id) || string.IsNullOrWhiteSpace(token))
                { 
                    return;
                }
                if (!int.TryParse(user_id, out var user_id_t)) return;
                var is_permission=await database.SqlExecuteAsync(async sql =>
                {
                    var user = await sql.Queryable<Tables.TableUser>().Where(it => it.Id == user_id_t).FirstAsync();
                    if (user == null)
                    {

                        return false;
                    }
                    if (user.UserStatus != UserStatus.Normal)
                    {
                        return false;
                    }
                    if (!database.UserEnabled(user.Id))
                    {
                        return false;
                    }
                    var nonce = httpContext.Request.Query["nonce"].FirstOrDefault();
                    string token_builder_str = $"{vehicle_id}-{user_id}-{nonce}-@@11XYZ";
                    if (user.Secret == null)
                    {
                        user.Secret = "102030405060708090a0b0c0d0e0f0@@";
                    }
                    using var hmacsha256 = new HMACSHA256(Encoding.ASCII.GetBytes(user.Secret));
                    byte[] hashmessage = hmacsha256.ComputeHash(Encoding.ASCII.GetBytes(token_builder_str));
                    if (string.Join("", hashmessage.Select(it => it.ToString("x2"))) != token)
                    { 
                        return false;
                    } 
                    var vehicle = await sql.Queryable<TableVehicle>().Where(it => it.VehicleSerial == vehicle_id).FirstAsync();
                    if (vehicle == null)
                    {
                        return false;
                    }
                    if (vehicle.OwnerUid != user.Id && user.UserRole != UserRole.SuperAdmin)
                    {
                        var vehicle_auth = await sql.Queryable<TableVehicleAuthorize>().Where(it => it.UserUid == user.Id).Where(it => it.VehicleId == vehicle.Id).FirstAsync();
                        if (vehicle_auth == null)
                        {
                            return false;
                        }
                    }

                   
                    m_authorizeds.TryAdd(Context.ConnectionId,new ControlAuthorizedInfo
                    {
                        VehicleId=vehicle_id
                    });
                    return true;
                });
                if (is_permission)
                {   
                    await Groups.AddToGroupAsync(Context.ConnectionId, vehicle_id);
                    mqtt.Subscribe(vehicle_id, Context.ConnectionId);
                  
                  
                }
                
            } 
        }

        public class Command
        {
            public string method { get; set; }
            public string action { get; set; }
            public object data {  get; set; }
        }
        public async Task Message(string json,string vehicle_id)
        {
            if(m_authorizeds.TryGetValue(Context.ConnectionId,out var info))
            {
                if (info.VehicleId == vehicle_id)
                {
                    try
                    {
                        var command = Newtonsoft.Json.JsonConvert.DeserializeObject<Command>(json);
                        if (command != null)
                        {
                            await mqtt.SendAsync(Context.ConnectionId,info.VehicleId,command.method, command.action, command.data);
                        }
                    }
                    catch (Exception ex) { 
                    
                    }
                }
            }
        }
    }
}
