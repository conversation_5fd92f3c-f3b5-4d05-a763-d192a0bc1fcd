// noinspection JSCheckFunctionSignatures


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion:'/modules/expansion',
}).define(['jquery','form', 'table','expansion'], function (exports) {
    const table_name = "resource-table";
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        $ = layui.jquery,
        module={
            build:function (resource,target,role){
                let columns = [[]];
                columns[0] = [];
                columns[0].push({ type: "checkbox" });
                //it.Id,
                //    it.,
                //    it.Remark,
                //    it.Status,
                //    it.RequestStatus,
                //    it.CreateTime,
                //    it.,
                //    it.,
                //    it.,
                //    it.,
                //    it.,
                //    it.,
                //    it.,
                //    it.,
                //    it.,
                //    it.UserUid,
                //    it.TodayDateOffset,
                //    TodayDateReal = today
                columns[0].push({field: "Id", title: "ID"});
                columns[0].push({ field: "ChannelCode", title: "通道编号"});
                columns[0].push({ field: "ResourceIdentity", title: "资源标识" });
                columns[0].push({ field: "UserUid", title: "用户ID" });
                columns[0].push({ field: "TotalAmountLimit",  title: "金额限制", templet: "#max-amount-limit-tpl" });
                columns[0].push({ field: "TotalNumberLimit",  title: "次数限制", templet: "#max-number-limit-tpl" });
                columns[0].push({ field: "Remark", title: "备注" });
                if (target == "using") columns[0].push({ field: "FreezeAmount", title: "冻结金额", templet: "#freeze-amount-tpl" });
                if (target == "using") columns[0].push({ field: "FreezeNumber", title: "冻结次数" });
                if (target == "using") columns[0].push({ field: "TodayCompleteAmount", title: "今日流水", templet: "#today-amount-tpl" });
                if (target == "using") columns[0].push({ field: "TodayCompleteNumber", title: "今日次数" });
                if (target == "using") columns[0].push({ field: "TotalCompleteAmount", title: "总流水", templet: "#total-amount-tpl" });
                if (target == "using") columns[0].push({ field: "TotalCompleteNumber", title: "总次数"});
                columns[0].push({field: "Description", title: "描述"});
                columns[0].push({field: "CreateTime",  title: "导入时间"});
                columns[0].push({ field: "Status", title: "状态", templet: "#res-status-tpl" });
                columns[0].push({field: "operations", title: "操作",minWidth:260, templet: "#operation-tpl"});
                return columns;
            },
            render:function (resource,target,role){
                table.render({
                    elem:"#"+table_name,
                    url: "/api/resource/"+resource+"/list/" + target,
                    height: "100%",
                    page: true,
                    defaultToolbar: ['filter'],
                    toolbar: "#toolbar",
                    limit: 30,
                    cols:module.build(resource,target,role),
                    parseData: function (res) {
                        return expansion.parse(res);
                    }, done: function () {
                        expansion.render();
                    }
                });

                let event_handler = {
                    get_channel: function(success) {
                        $.ajax({
                            url: "/api/resource/"+resource+"/channel",
                            type: "get",
                            loading: true,
                            dataType: "json",
                            success: function (res) {
                                if (res.success) {
                                    success(res.body);
                                }
                            }
                        });
                    },
                    import_resource: function (index, channel, field) {
                        $.ajax({
                            url: "/api/resource/"+resource+"/import/entity?channel_code=" + channel,
                            type: "post",
                            data: JSON.stringify(field),
                            dataType: "json",
                            loading:true,
                            success: function (res) {
                                if (res.success) {
                                    //刷新列表
                                    table.reload(table_name);
                                    layer.close(index);
                                }
                            }
                        });
                    },
                    show_import_dialog: function (channel,html) {
                        let form_filter = "import_" + (new Date()).valueOf();
                        layer.open({
                            title: "添加",
                            type: 1,
                            fixed: true,
                            maxmin: false,
                            shadeClose: false,
                            resize: true,
                            offset: ['30px'],
                            area: ['300px'],
                            btn: ['确定导入', '取消'],
                            content: html,
                            success: function (layero, index, that) {
                                layero.find("form").attr("lay-filter", form_filter);
                                layui.form.render($(layero));
                                expansion.element_render($(layero));
                            }, yes: function (index, layero, options) {
                                if (form.submit(form_filter)!=false) {
                                    event_handler.import_resource(index, channel,form.val(form_filter))
                                }

                               
                            }
                        });
                    },
                    wait_select_channel: function (list,selected) {

                        let select_key = "s_" + new Date().valueOf();
                        var select = $('<select lay-verify="required" name="' + select_key + '"></select>');
                        select.append($("<option value=''>请选择通道</option>"))
                        for (var n in list) {
                            let item = list[n];
                            select.append($("<option value='" + item.ChannelCode + "'>" + item.Description + "</option>"))
                        }
                        layer.prompt({
                            formType: 0,
                            title: '请选择通道',
                            value: '',
                            offset: ["165px"],
                            success: (layero) => {
                                $(layero).addClass("layui-form")
                                $(layero).find("input").before(select).hide();
                                layui.form.render($(layero));
                            }
                        }, function (value, index, elem) {
                            elem = elem.parent().find("select");
                            var channel_code = elem.val();
                            if (channel_code === "") {
                                layer.tips('请选择通道', elem.parent().find(".layui-form-select"), { tips: 1 });
                                return;
                            }
                            selected(channel_code);
                            layer.close(index);
                        });
                    },
                    render_resource_create: function (channel) {
                        $.ajax({
                            url: "/api/resource/"+resource+"/import/template?channel_code=" + channel,
                            type: "get",
                            loading: true,
                            dataType:"json",
                            success: function (res) {
                                if (res.success) {
                                    event_handler.show_import_dialog(channel,res.body.html);
                                }
                            }
                        })
                    },
                    resource_create: function () { 
                        event_handler.get_channel(function (lst) {
                            event_handler.wait_select_channel(lst, function (channel) {
                                event_handler.render_resource_create(channel);
                            });
                        });   
                    }
                }

                let can_action= module.can_action = {
                    can_pause: function (d) {
                        if (d.RequestStatus == enums.request_status.none && d.Status == enums.resource_status.idle) {
                            return true;
                        }
                        return false;
                    },
                    can_resume: function (d) {

                        if (d.RequestStatus == enums.request_status.pause) {

                            return true;
                        }
                        return false;
                    },
                    can_reset: function (d) {
                        if (d.Status == enums.resource_status.failed) {
                            return true;
                        }
                        return false;
                    },
                    can_refund: function (d) {
                        if (d.Status != enums.resource_status.complete && d.RequestStatus != enums.request_status.refund) {
                            return true;
                        }
                        return false;
                    },
                    can_delete: function (d) {
                        if (d.Status == enums.resource_status.failed) {
                            return true;
                        }
                        return false;
                    },
                    can_edit: function (d) {
                        return false;
                    },
                    can_query: function (d) {
                        return true;
                    }
                };
                var reset_toolbar = function () {
                    var checkStatus = table.checkStatus(table_name),
                        ds = checkStatus.data;
                    $(".layui-table-tool .layui-btn-group>button[batch]").addClass("layui-btn-disabled");
                    let pause = false;
                    let resume = false;
                    let reset = false;
                    let refund = false;
                    let del = false;
                    for (let n = 0; n < ds.length; n++) {
                        pause = pause || can_action.can_pause(ds[n]);
                        resume = resume || can_action.can_resume(ds[n]);
                        reset = reset || can_action.can_reset(ds[n]);
                        refund = refund || can_action.can_refund(ds[n]);
                        del = del || can_action.can_delete(ds[n]);
                    }
                    if (pause) {

                        $(".layui-table-tool .layui-btn-group>button[batch='pause']").removeClass("layui-btn-disabled");
                    }
                    if (resume) {
                        console.log("resume ok")
                        $(".layui-table-tool .layui-btn-group>button[batch='resume']").removeClass("layui-btn-disabled");
                    }
                    if (reset) {
                        $(".layui-table-tool .layui-btn-group>button[batch='reset']").removeClass("layui-btn-disabled");
                    }
                    if (del) {
                        $(".layui-table-tool .layui-btn-group>button[batch='delete']").removeClass("layui-btn-disabled");
                    }
                    if (refund) {
                        $(".layui-table-tool .layui-btn-group>button[batch='refund']").removeClass("layui-btn-disabled");
                    }
                }
                var module_batch = function (event, data) {
                    $.ajax({
                        url: "/api/resource/" + resource + "/batch/" + event + "/" + target,
                        type: "post",
                        data: JSON.stringify(data),
                        dataType:"json",
                        loading: true,
                        success: function (res) {
                            if (res.success) {
                                var cache = table.cache[table_name];
                                for (var n = 0; n < cache.length; n++) {
                                    if (cache[n].Id in res.body) {
                                        if (event == "delete") {
                                           delete cache[n];
                                        } else {
                                            cache[n].operations = (new Date()).valueOf();
                                            cache[n].Status = res.body[cache[n].Id].Status;
                                            cache[n].RequestStatus = res.body[cache[n].Id].RequestStatus;
                                        }
                                        
                                    }
                                }
                                table.renderData(table_name);
                                reset_toolbar();
                            }
                            //重新渲染行
                        }
                    });
                };

                table.on('checkbox(' + table_name + ')', function (obj) {
                
                    reset_toolbar();
                   
                });
                table.on('tool(' + table_name + ')', function (obj) {
                    if ($(this).hasClass("layui-btn-disabled")) return;
                    var data = obj.data;
                    var command = obj.event;
                    if (command == "edit") {
                        //编辑
                    } else if (command == "query") {
                        //查询
                    }
                    else {
                        module_batch(command, [data.Id]);
                    }
                });
                $(document).on("click", ".layui-table-tool .layui-btn-group>button[batch]", function () {
                    if ($(this).hasClass("layui-btn-disabled")) return;
                    var command = $(this).attr("batch");
                    var checkStatus = table.checkStatus(table_name),
                        d = checkStatus.data;
                    var data = [];
                    for (var n in d) {
                        data.push(d[n].Id);
                    }
                    module_batch(command, data);
                });
                $(document).on("click", '[name="resource-create"]', function () {
                    event_handler.resource_create();
                });

                var module_query_leave = function (id,code, res) {
                    $.ajax({
                        url: "/api/resource/" + res + "/query/leave/" + target+"?id="+id+"&channel_code="+code,
                        type: "get",
                        dataType: "json",
                        loading: true,
                        success: function (res) {
                            if (res.success) {
                                if (res.body.number == -1) {
                                    layer.msg("系统错误", {
                                        icon: 2,
                                        time: 2000
                                    }, function () {
                                    });
                                }
                                layer.msg("今日剩余可用:"+res.body.number, {
                                    icon: 1,
                                    time: 2000
                                }, function () {
                                });
                            } 
                        }
                    });
                }
                $(document).on("click", '[name="resource-leave"]', function () {
                    layer.prompt({
                        formType: 0,
                        title: '请输入通道编码',
                        value: '',
                        offset: ["165px"],
                    }, function (value_c, index, elem) {
                        if (value_c.trim() === "") {
                            layer.tips('请输入通道编码', this);
                            return;
                        }
                        if (role === "superadmin") {
                            layer.prompt({
                                formType: 0,
                                title: '请输入平台ID',
                                value: '',
                                offset: ["165px"],
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的平台ID', this);
                                    return;
                                }
                                module_query_leave(value, value_c, resource);
                                layer.close(index);
                            });
                        } else if (role === "platform") {
                            module_query_leave(0, resource);
                        } else {
                            layer.msg("无查询权限", {
                                icon: 2,
                                time: 2000
                            }, function () {
                            });
                        } 
                        layer.close(index);
                    });
                    
                });
            },
            submit: function (where) {
                table.reload(table_name, {
                    page: {curr: 1},
                    where: {
                        biz_context: JSON.stringify(where)
                    }
                });
            }
            //%7B%22status%22%3A%22Busy%22%2C%22channel_code%22%3A%222%22%2C%22desc%22%3A%22333%22%2C%22remark%22%3A%222%22%2C%22keyword%22%3A%221111%22%7D
            
        };
      
    form.on("submit(search)", function (data) {
        let search={};

        for (let s in data.field) {
            if ($(".grid-toolbar-item").hasClass("switch-status") || s === "keyword") {
                let t = String(data.field[s]);
                if (t !== "null" && t !== "undefined" && t !== "") {
                    search[s] = t;
                }
            }
        }
        module.submit(search);
        return false;
    });
    $(document).on("click",'.grid-toolbar-item.show-more',function (){
        $(".grid-toolbar-item").toggleClass("switch-status");
    });
  
   

  
    exports('resource', module);
});