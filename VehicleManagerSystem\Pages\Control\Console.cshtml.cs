using System.Security.Cryptography;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using VehicleManagerSystem.Services;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Pages.Console
{
    public class ConsoleModel(DataBaseService database) : PageModel
    {
        [BindProperty(SupportsGet = true)]
        public required string id { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? uid { get; set; }
        [BindProperty(SupportsGet = true)]
        public string? nonce { get; set; }
        [BindProperty(SupportsGet = true)]
        public string? token { get; set; }

        public string? ErrorMessage { get; set; }
        public bool HasErrors { get; set; }
        public string realtime_query = "";

        public string? VehicleName { get; set; }
        public int? VehicleId { get; set; }
        public async Task<IActionResult> OnGetAsync()
        {
                HasErrors = false;
                ErrorMessage = "ERROR_MISS_VEHICLE_PERMISSION";
                if(!await database.SqlExecuteAsync(async sql =>
                {
                    TableUser? user = null;
                    if (uid != null)
                    {
             
                            user= await sql.Queryable<TableUser>().Where(it => it.UserName == uid).FirstAsync();
                            if (user == null) {
                                ErrorMessage = "ERROR_USER_EXISTS";
                                return false;
                            }
                            if (user.UserStatus != UserStatus.Normal)
                            {
                                ErrorMessage = "ERROR_LOGIN_DISABLED";
                                return false;
                            }
                            if (!database.UserEnabled(user.Id))
                            {
                                     ErrorMessage = "ERROR_LOGIN_DISABLED";
                                    return false;
                            }
                            string token_builder_str = $"{id}-{uid}-{nonce}";
                            using var hmacsha256 = new HMACSHA256(Encoding.ASCII.GetBytes(user.Secret));
                            byte[] hashmessage = hmacsha256.ComputeHash(Encoding.ASCII.GetBytes(token_builder_str));
                            if (string.Join("", hashmessage.Select(it => it.ToString("x2"))) != token)
                            {
                                ErrorMessage = "ERROR_SIGNTURE";
                                return false;
                            }
                            nonce=Guid.NewGuid ().ToString();
                            var control_token = string.Join("", hmacsha256.ComputeHash(Encoding.ASCII.GetBytes($"{id}-{user.Id}-{nonce}-@@11XYZ")).Select(it => it.ToString("x2")));
                            realtime_query =$"?uid={user.Id}&token={control_token}&nonce={nonce}&vehicle_id={id}" ;
                            
                            
                    }
                    else
                    {
                        if(Request.Headers.TryGetValue("uid",out var h_uid))
                        {
                            if (Request.Headers.TryGetValue("token", out var h_token))
                            {
                                var n_uid = h_uid.FirstOrDefault();
                                var n_token = h_token.FirstOrDefault();
                                if (string.IsNullOrWhiteSpace(n_uid) || !int.TryParse(n_uid, out var uid_i))
                                {
                                    ErrorMessage = "ERROR_MISS_LOGIN";
                                    return false;
                                }
                                if (string.IsNullOrWhiteSpace(n_token)  ||n_token.Length!=32)
                                {
                                    ErrorMessage = "ERROR_MISS_LOGIN";
                                    return false;
                                }
                                user = await sql.Queryable<TableUser>().Where(it => it.Id == uid_i).FirstAsync();
                                if (user == null) {
                                    ErrorMessage = "ERROR_MISS_LOGIN";
                                    return false;
                                }
                                var hash = string.Join("", MD5.HashData(Encoding.UTF8.GetBytes(n_token + "@@112235##")).Select(it => it.ToString("x2")));
                                var userLogin = await sql.Queryable<TableUserToken>().Where(it => it.UserId == user.Id).Where(it => it.UserToken ==hash).FirstAsync();
                                if (userLogin == null)
                                { 
                                    ErrorMessage = "ERROR_MISS_LOGIN";
                                    return false;
                                }
                                if (user.Secret == null)
                                {
                                    user.Secret = "102030405060708090a0b0c0d0e0f0@@";
                                }
                                using var hmacsha256 = new HMACSHA256(Encoding.ASCII.GetBytes(user.Secret));
                                nonce=Guid.NewGuid ().ToString();
                                var control_token = string.Join("", hmacsha256.ComputeHash(Encoding.ASCII.GetBytes($"{id}-{user.Id}-{nonce}-@@11XYZ")).Select(it => it.ToString("x2")));
                                realtime_query =$"?uid={user.Id}&token={control_token}&nonce={nonce}&vehicle_id={id}" ;
                            }
                            else
                            {
                                 ErrorMessage = "ERROR_MISS_LOGIN";
                                 return false;
                            }
                        }
                        else
                        {
                            ErrorMessage = "ERROR_MISS_LOGIN";
                              return false;
                        }
                    }
                    
                    var vehicle= await sql.Queryable<TableVehicle>().Where(it => it.VehicleSerial == id).FirstAsync();
                    if (vehicle == null) {
                        ErrorMessage = "ERROR_VEHICLE_NOT_EXISTS";
                        return false;
                    }
                    VehicleName = vehicle.VehicleName;
                    VehicleId = vehicle.Id;
                     if (vehicle.OwnerUid != user.Id && user.UserRole!=UserRole.SuperAdmin)
                     {
                        var vehicle_auth=await sql.Queryable<TableVehicleAuthorize>().Where(it => it.UserUid == user.Id).Where(it => it.VehicleId == vehicle.Id).FirstAsync();
                        if (vehicle_auth == null) {  
                             ErrorMessage = "ERROR_MISS_VEHICLE_PERMISSION";
                            return false;
                        }
                   }
                    return true;

               }))
                {
                    HasErrors = true;
                    id = null!;
                    return Page();
                }
                return Page();
        }
    }
}
