﻿

layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion: '/modules/expansion',
}).define(['jquery', 'form', 'table', 'expansion', 'handle'], function (exports) {
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        handle = layui.handle;
    var $ = layui.jquery;
    var merchant_role = 0;
    var account_role = 0;
    var application_role = 0;
    var current_role = "";
    var last_childs = [];
    var last_binds = [];
    var curr_uid = 0;
    module = {
        load_binding: function () {
            $(".binding-user>*").remove();// html("");
            $(".line").remove();
            var childs = last_childs, binds = last_binds;
            for (var index in childs) {
                var user = childs[index];
                if (user.Role == merchant_role) {
                    $(".merchant").append($("<div class='user' data-uid='" + user.Id + "'><span class='role'>商户</span><span class='remark'>" + user.UserName + "</span><span class='action'>绑定</span></div>"));
                } else if (user.Role == account_role) {
                    $(".account").append($("<div class='user' data-uid='" + user.Id + "'><span class='role'>小号</span><span  class='remark'>" + user.UserName + "</span></div>"));
                } else if (user.Role == application_role) {
                    $(".application").append($("<div class='user' data-uid='" + user.Id + "'><span class='role'>应用</span><span  class='remark'>" + user.UserName + "</span></div>"));
                }
            }
            if (current_role == "merchant") {
                for (var index in binds) {
                    var bind = binds[index];
                    if (bind.P_Uid != curr_uid) {
                        if (bind.B_Type == 0) {
                            $(".account").append($("<div class='user' data-uid='" + bind.Id + "'><span class='role'>小号</span><span  class='remark'>" + bind.Remark + "</span></div>"));
                        } else if (bind.B_Type == 1) {
                            $(".application").append($("<div class='user' data-uid='" + bind.Id + "'><span class='role'>应用</span><span  class='remark'>" + bind.Remark + "</span></div>"));
                        }
                    }
                }
            } else if (current_role != "superadmin") {
                for (var index in binds) {
                    var bind = binds[index];
                    if (bind.P_Uid != curr_uid) {
                        $(".merchant").append($("<div class='user' data-uid='" + bind.Id + "'><span class='role'>商户</span><span class='remark'>" + bind.Remark + "</span><span class='action'>绑定</span></div>"));
                    }
                }
            }
            
             for (var index in binds) {
                 var bind = binds[index];
                 try {
                     if (!bind.IsDelete) {
                         var s1 = {};
                         var s2 = {};
                         if (bind.S_Uid > 0 && bind.T_Uid > 0) {
                             s1 = $("[data-uid='" + bind.T_Uid + "']");
                             s2 = $("[data-uid='" + bind.S_Uid + "']");

                         }
                         else if (bind.T_Uid > 0) {
                             s1 = $("[data-uid='" + bind.T_Uid + "']");
                             s2 = $("[data-uid='" + bind.P_Bind + "']");
                         } else if (bind.S_Uid > 0) {
                             s1 = $("[data-uid='" + bind.P_Bind + "']");
                             s2 = $("[data-uid='" + bind.S_Uid + "']");
                         }
                         var begin = s1.offset();
                         var end = s2.offset();
                         var sp_begin = {
                             X: begin.left + s1.width() / 2 + 12,
                             Y: begin.top + s1.height() / 2 + 4
                         };

                         var sp_end = {
                             X: end.left + s2.width() / 2 + 14,
                             Y: end.top + s2.height() / 2 + 4
                         };
                         if (sp_end.X > sp_begin.X) {
                             sp_begin.X = begin.left + s1.width() + 12;
                             sp_end.X = end.left - 1;
                         } else {
                             sp_begin.X = begin.left - 1;
                             sp_end.X = end.left + s2.width() + 13;
                         }
                         var rect = module.create_line();
                         var div = document.createElement("div");

                         div.classList.add("delete-binding-c")
                         rect.appendChild(div);

                         var span = document.createElement("span");
                         span.innerText = "删除";
                         span.classList.add("delete-binding")
                         div.appendChild(span);
                         $(span).attr("data-tag", bind.T_Uid);
                         $(span).attr("data-src", bind.S_Uid);
                         $(span).attr("data-type", bind.B_Type);
                         $(span).attr("data-parent", bind.P_Bind);

                         $(rect).find(".remark")[0].setAttribute("remark", bind.Remark);
                         module.reset_line(sp_begin, sp_end, rect);
                     }
                 } catch (err) { }
                }
             
          
        },
        refresh: function () {
            $.ajax({
                url: "/api/account/get/binding",
                type: "get",
                success: function (res) {
                    if (res.success) {
                        last_childs = res.body.childs;
                        last_binds = res.body.binds;
                        curr_uid = res.body.uid;
                        module.load_binding();
                    }
                }, error: function () {

                }
            })
        },
        render: function (c_role,m_role, a_role, p_role) {
            merchant_role = m_role;
            account_role = a_role;
            application_role = p_role;
            current_role = c_role;
            module.refresh();
        },
        go_binding:function (line,target_id,src,type,src_type) {
           layer.prompt({
                formType: 0,
                title: '请输入备注',
                value: '',
                btn2: function () {
                    document.body.removeChild(line);
                },
            }, function (value, index, elem) {
                if (value.trim() === "") {
                    layer.msg("请输入备注", { icon: 2, time: 2000 }, function () { });
                    return;
                }

                line.setAttribute("remark", value);
                $.ajax({
                    url: "/api/account/set/binding",
                    type: "post",
                    dataType: "json",
                    loading: true,
                    data: JSON.stringify({
                        tag: target_id,
                        src: src,
                        type: type,
                        src_type: src_type,
                        remark: value
                    }),
                    success: function (res) {
                        if (res.success) {
                            module.refresh();
                        }
                    }
                });
                layer.close(index);
            }); 
        },
        reset_line: function (s1,s2,rect) {
            var cur_x = s2.X;
            var cur_y = s2.Y;
            var is_left = false;
            if (cur_x < s1.X) {
                is_left = true;
                rect.style.left = (cur_x+2) + "px";
                rect.style.top = cur_y + "px";
            } else {
                rect.style.left = (s1.X+2) + "px";
                rect.style.top = s1.Y + "px";
            }
            var width = Math.abs(cur_x - s1.X);
            var height = cur_y - s1.Y;
            width -= 4;
            rect.style.width = width + "px";
            rect.style.height = height + "px";
            var thetaRadians = Math.atan(height / width);
            var deg = thetaRadians * (180 / Math.PI);
            if (is_left) deg *= -1;
            var x_width = width * width + height * height;
            x_width = Math.sqrt(x_width);
            rect.style.setProperty("--x-width", x_width + "px");
            rect.style.setProperty("--rotate", deg + "deg");
        },
        create_line: function (x,y) {
            var rect = document.createElement("div");
            rect.classList.add("line");
            rect.style.position = "fixed";
            rect.style.left = x + "px";
            rect.style.top = y + "px";

            var remark = document.createElement("span");
            remark.classList.add("remark");
            rect.appendChild(remark);
            document.body.appendChild(rect);
            return rect;
        },
        begin_bind : function (x, y, target_id, target_type) {
            var rect = module.create_line(x, y);
            rect.classList.add("set")
            var move = function (ex) {
                module.reset_line({ X: x, Y: y }, { X: ex.clientX, Y: ex.clientY }, rect);
            };
            document.addEventListener("mousemove", move, false);
            var on_end = function (e) {
                document.removeEventListener("mousemove", move);
                if (!$(this).parent().hasClass("merchant")) {
                    var src = $(this).attr("data-uid");
                    var type = $(this).parent().hasClass("application") ? "Application" : $(this).parent().hasClass("account") ? "Account" : "none";
                    module.go_binding(rect, target_id, src, type, target_type)
                    $(".merchant>.user>.action").css("visibility", "visible");
                } else {
                    document.body.removeChild(rect);
                }
            };
            $(document).one("click", ".user", on_end);
            $(document).one("contextmenu", function (event) {
                event.preventDefault();
                document.removeEventListener("mousemove", move);
                document.body.removeChild(rect);
                $(document).off("click", ".user,.user", on_end);
                $(".merchant>.user>.action").css("visibility", "visible");
            });
        }

    };
    exports('binding', module);
    var next_time = 0;
    var refresh_next = function () {
        if (next_time > 0) {
            next_time = (new Date()).valueOf();
            return;
        }
        next_time = (new Date()).valueOf()+500;
        var exit_time = setInterval(() => {
            if (next_time < (new Date()).valueOf()) {
                next_time = 0;
                module.load_binding();
                clearInterval(exit_time);
            }   
        }, 100);
    };
    $(window).resize(() => {
       
        refresh_next();
    });  
   
    $(document).on("click", ".merchant>.user>.action", function (e) {
        var box = $(this).parent();
        var begin = box.offset();
        var id = box.attr("data-uid");
        $(".merchant>.user>.action").css("visibility", "hidden");
        module.begin_bind(begin.left + box.width() / 2, begin.top + box.height() / 2, id, current_role=="superadmin"? "user":"bind");
    });
    $(document).on("click", ".delete-binding", function (e) {
        var tag= $(this).attr("data-tag");
        var src = $(this).attr("data-src");
        var type = $(this).attr("data-type");
        var parent = $(this).attr("data-parent");

        var p_self = $(this).parent().parent()
        $.ajax({
            url: "/api/account/disible/binding",
            type: "post",
            dataType: "json",
            loading: true,
            data: JSON.stringify({
                tag: tag,
                src: src,
                type: type,
                parent: parent,
            }),
            success: function (res) {
                if (res.success) {
                    p_self .remove();
                }
            }
        });
       
    });
    
    
   

});