package com.scvdos.vds.ground.module.tcp;


import com.scvdos.vds.ground.module.groundapi.ModuleDefine;
import com.scvdos.vds.ground.module.groundapi.ModuleInfo;
import com.scvdos.vds.ground.module.groundapi.ModuleType;

import java.util.ArrayList;
import java.util.List;

public class Define implements ModuleDefine {
    public  Define(){
        ModuleInfo module_params_manager=new ModuleInfo();
        module_params_manager.ground_module_key="501cb73b723222db";
        module_params_manager.ground_module_name="TCP转发";
        module_params_manager.ground_module_type= ModuleType.GROUND_DATA_MANAGER;
        module_params_manager.ground_module_component_id=0;
        module_params_manager.ground_module_command_id  =0;
        module_params_manager.api_version=2;
        module_params_manager.has_view=false;
        module_params_manager.ground_module_class_name= Manager.class.getName();
        modules.add(module_params_manager);
    }
    @Override
    public List<ModuleInfo> getModules() {
        return modules;
    }
    private final List<ModuleInfo> modules=new ArrayList<>();
}