﻿


layui.extend({

}).define(['jquery'], function (exports) {
    let $ = layui.$
        , admin = layui.admin
        , view = layui.view
    let ug_loading = null;
    $(window).bind('beforeunload', function () {
        const customEvent = new CustomEvent('begin_load', {
            bubbles: true,
            cancelable: false,
            detail: { message: '' }
        });
        document.dispatchEvent(customEvent);
    });
    $(document).ajaxComplete(function (event, xhr, settings) { 
        if (settings.loading_index) {
            layer.close(settings.loading_index);
        }
        if (xhr.status === 200) {
            if(settings.dataType==="json") {
                let resp = JSON.parse(xhr.responseText);
                if (resp.success === false) {
                    
                    if (!resp.message) {
                        if(is_relogin(resp.code)){
                            localStorage.removeItem("token");
                            if(top.relogin){
                                top.relogin();
                            }else{
                                top.location.href="/login";
                            }
                        }
                        (typeof (settings.alert) == 'undefined' || settings.alert) && layer.msg(get_error(resp.code), {
                            icon: 2,
                            time: 2000
                        }, function () {
                        });
                    } else {
                        
                        (typeof (settings.alert) == 'undefined' || settings.alert) && layer.msg(resp.message, {
                            icon: 2,
                            time: 2000
                        }, function () {
                        });
                    }
                }
            }
        } else { 
            if(xhr.status===403||xhr.status===401){
                if(parent.relogin){
                    parent.relogin();
                }else{
                    localStorage.removeItem("token");
                    top.location.href="/login";
                }
            }
            else if(settings.dataType==="json") {
                (typeof (settings.alert) == 'undefined' || settings.alert) && layer.msg("网络错误", {
                    icon: 2,
                    time: 2000
                }, function () {
                });
            }
        }
    });
    $.ajaxSetup({
        beforeSend: function (xhr, settings) {
            if (settings.loading) {
                settings.loading_index = layer.load(3);
            }
            xhr.setRequestHeader('token', localStorage.getItem("token"));
            if(settings.dataType==="json") {
                if (!settings.headers || settings.headers.file !== "yes") {
                    xhr.setRequestHeader('content-type', "application/json")
                } 
            } 
        }
    });
    $(document).on("click", "[lay-copy]", function () {
        let self = $(this);
        let text = self.attr("[lay-copy]");
        if (!text || text == "") text = self.text();
        navigator.clipboard.writeText(text)
            .then(() => {
                layer.tips("复制成功", self,{
                    tips: 1
                });
            })
            .catch(err => {
                layer.tips("复制失败", self, {
                    tips: 1
                });
            });
    });
    var module = {
        render: function (elem) {
            var change_ipt = $(elem).find("[lay-change]");
            change_ipt.each(function () {

                var ipt = $(this).find($(this).attr("lay-change"));
                if (!ipt.after().hasClass("save")) {
                    let save = $("<span class='save'>保存</span>");
                    ipt.after(save);
                    save.on("click", function () {
                        ipt.trigger("save", [ipt, function () {
                            ipt.parent().removeClass("change-save");

                            ipt.attr("lay-change-old-value", ipt.val());
                        }]);
                    });
                }

                ipt.attr("lay-change-old-value", ipt.val());
                ipt.off("input");
                ipt.on("input", function () {
                    if ($(this).attr("lay-change-old-value") != $(this).val()) {
                        $(this).parent().addClass("change-save");
                    } else {
                        $(this).parent().removeClass("change-save");
                    }
                });
            });

        },
        base64ToBlob: function (base64,mime){
            const binaryString = atob(base64);
            const length = binaryString.length;
            const bytes = new Uint8Array(length);
            for (let i = 0; i < length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }  
            return new Blob([bytes], { type: mime });
        },
        save_file: function (blob, filename) {
            //var blob = new Blob([content], { type: 'text/plain;charset=utf-8' }); 
            // 创建一个临时的 <a> 元素
            var link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename; 
            // 触发点击事件
            document.body.appendChild(link);
            link.click();
            // 清理工作
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }
    };
    exports('handle', module);
});
