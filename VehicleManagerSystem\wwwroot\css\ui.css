﻿html, body {
    padding: 0;
    margin: 0;
    height: 100vh;
    width: 100vw;
    font-family: weilai !important;
    background-size: cover,auto;
    background-image: url(/image/bridge.webp);
    background-repeat: no-repeat;
}
* {
    font-family: weilai;
}
shade {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    background: #333333AA;
    z-index: 99;
    flex-direction: column;
    color: #02d6fb;
}
    
    loading {
        height: 40px;
        width: 40px;
        border: 1px solid #02d6fb;
        background: rgba(2,214,251,0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20vh;
    }
    loading[no-border] {
        border: none;
        background:none;
    }
    loading:after {
        content: "";
        position: absolute;
        border: 3px solid rgba(2,214,251,0.1);
        border-radius: 50%;
        border-top: 3px solid #02d6fb;
        width: 24px;
        height: 24px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
    }

alert {
    border: 1px solid #02d6fb;
    display: flex;
    flex-direction: column;
    padding: 0px 10px;
    min-width: 200px;
    min-height: 80px;
    text-align: center;
    color: #02d6fb;
    background: rgba(2,214,251,0.2);
    animation: flash 1s ease-in-out forwards;
    margin-bottom: 20vh;
}
/* 定义闪烁两次的动画 */
@keyframes flash {
    0% {
        opacity: 1;
    }

    10% {
        opacity: 0;
    }

    20% {
        opacity: 1;
    }

    30% {
        opacity: 0;
    }

    40% {
        opacity: 1;
    }

    100% {
        opacity: 1;
    }
}
alert>title{
    height:32px;
    display:flex;
    justify-content:left;
    align-items:center;
    position:absolute;
    font-size:0.8em;
}
alert>content{
    flex:1;
    display:flex;
    justify-content:center;
    align-items:center;
}

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}