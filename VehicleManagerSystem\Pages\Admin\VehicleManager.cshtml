﻿@page
@using VehicleManagerSystem.Tables
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Admin.VehicleManagerModel
@{
	Layout = "_LayoutAdminFrame";
	ViewData["Title"] = language["INDEX_MENU_VEHICLE_MANAGER"];

}

@section Toolbar {
	<form name="toolbar-search" action="javascript:;" style='display:flex;display: flex;align-items: center;gap: 8px;'>
		<layui-select target="VehicleStatus.None" name="status" lay-verify=""></layui-select>
		<layui-select target="VehicleType.None" name="type" lay-verify=""></layui-select>
		<input  readonly readonly-fill type="text" name="keyword" lay-verify="required" placeholder="@Html.Raw(language["INPUT_TEXT_SEARCH"])" autocomplete="off" class="layui-input search-ipt" />
		<button type="submit" class='search-submit layui-btn' lay-submit lay-filter="search">@Html.Raw(language["BUTTON_TEXT_SEARCH"])</button>
		<div class='layui-icon-down search-submit layui-btn add-btn'>@Html.Raw(language["BUTTON_TEXT_ADD"])</div>
	</form>
}
<table id="table-vehicle" ></table>
<div type="text/template" id="add-template" style="display:none">
	<div vui-label="@Html.Raw(language["LABEL_VEHICLE_SERIAL"])"><input name="serial" readonly readonly-fill required type="text" placeholder="@Html.Raw(language["INPUT_TEXT_VEHICLE_SERIAL"])" title="@Html.Raw(language["INPUT_TEXT_VEHICLE_SERIAL"])" autocomplete="off" /></div>
	<div vui-label="@Html.Raw(language["LABEL_VEHICLE_SECRET"])"><input name="vehicle_secret" readonly readonly-fill required type="text" placeholder="@Html.Raw(language["INPUT_TEXT_VEHICLE_SECRET"])" title="@Html.Raw(language["INPUT_TEXT_VEHICLE_SECRET"])" autocomplete="off" /></div>
	<div vui-label="@Html.Raw(language["LABEL_VEHICLE_NAME"])"><input name="vehicle_name" readonly readonly-fill required type="text" placeholder="@Html.Raw(language["INPUT_TEXT_VEHICLE_DISPLAY_NAME"])" title="@Html.Raw(language["INPUT_TEXT_VEHICLE_DISPLAY_NAME"])" autocomplete="off"  /></div>

</div>

<!--
	1.MQTT编号,飞机SN号 -- MQTT密钥
	2.摄像头类型 --
	3.载荷类型
	//其它消息由MQTT遥控器传入 
	->转到控制界面
	->控制台
		->根据用户显示所属飞机的状态
		->WS连接后,根据用户订阅飞机状态
		VDB-周游-2025-TW50-0A(绵阳)-000001
		VDBZY25TW500A0800001     //32字节
-->

@section Scripts {


	<script id="vehicle-status-tpl" type="text/html">
		 {{# if(false){ }}
		@foreach (var val in Enum.GetValues<VehicleStatus>())
		{
			@:{{# } else if(d.vehicleStatus== @((int)val)){ }}
							<span>@Html.Raw(language[Utils.EnumsUtil.DisplayName(val)])</span>
		}
		{{# } }}

	</script>
	<script id="vehicle-type-tpl" type="text/html">
		 {{# if(false){ }}
		@foreach (var val in Enum.GetValues<VehicleType>())
		{
			@:{{# } else if(d.vehicleType== @((int)val)){ }}
							<span>@Html.Raw(language[Utils.EnumsUtil.DisplayName(val)])</span>
		}
		{{# } }}
	</script>
	<script id="vehicle-lat-tpl" type="text/html">
		<span>{{ d.latitude.toFixed(7) }}</span>
	</script>
	<script id="vehicle-rssi-tpl" type="text/html">
		<span>{{ d.rssi }}ms</span>
	</script>
	<script id="vehicle-battery-tpl" type="text/html">
		<span>{{ d.battery }}%</span>
	</script>
	<script id="vehicle-lng-tpl" type="text/html">
		<span>{{ d.longitude.toFixed(7) }}</span>
	</script>
	<script id="vehicle-alt-tpl" type="text/html">
		<span>{{ d.altitude.toFixed(2) }} @Html.Raw(language["UNIT_METER"])</span>
	</script>
	<script id="vehicle-action-tpl" type="text/html">
		<span class="vui-button">@Html.Raw(language["BUTTON_ACTION_DEL"])</span>
		<span class="vui-button normal">@Html.Raw(language["BUTTON_ACTION_SECRET"])</span>
		<span class="vui-button normal" lay-event="control"  >@Html.Raw(language["BUTTON_ACTION_CONTROL"])</span>
	</script>
	<script>
			layui.use(['layer', 'form','table'], function () {
			 var layer = layui.layer
				, form = layui.form
				,table = layui.table;
				table.render({
					 elem: '#table-vehicle'
					,height: "100%"
					,url: '/api/vehicle/list'
					,page: true
					,cols: [[
					   {field: 'vehicleSerial'			, title: '@Html.Raw(language["LABEL_VEHICLE_SERIAL"])',width:290}
					  ,{field: 'vehicleName'			, title: '@Html.Raw(language["LABEL_VEHICLE_NAME"])'}
					  ,{field: 'vehicleType'			, title: '@Html.Raw(language["LABEL_VEHICLE_TYPE"])',templet:"#vehicle-type-tpl"}
					  ,{field: 'vehicleModel'			, title: '@Html.Raw(language["LABEL_VEHICLE_MODEL"])'}
					  ,{field: 'vehicleManufacturer'	, title: '@Html.Raw(language["LABEL_VEHICLE_MANUFACTURER"])'}
					  ,{field: 'vehicleStatus'			, title: '@Html.Raw(language["LABEL_VEHICLE_STATUS"])',templet:"#vehicle-status-tpl"}
					  ,{field: 'battery'				, title: '@Html.Raw(language["LABEL_VEHICLE_BATTERY"])',width:80,templet:"#vehicle-battery-tpl"}
					  ,{field: 'rssi'					, title: '@Html.Raw(language["LABEL_VEHICLE_RSSI"])',width:120,templet:"#vehicle-rssi-tpl"}
					  ,{field: 'longitude'				, title: '@Html.Raw(language["LABEL_VEHICLE_LONGITUDE"])',templet:"#vehicle-lng-tpl"}
					  ,{field: 'latitude'				, title: '@Html.Raw(language["LABEL_VEHICLE_LATITUDE"])',templet:"#vehicle-lat-tpl"}
					  ,{field: 'altitude'				, title: '@Html.Raw(language["LABEL_VEHICLE_ALTITUDE"])',templet:"#vehicle-alt-tpl"}
					  ,{field: 'delete'					, title: '@Html.Raw(language["LABEL_ACTION"])',width:207,templet:"#vehicle-action-tpl"}
					]],
					parseData:function(res){
						if(res.success){
							return {
								"code": res.data.code, // 解析接口状态
								"msg": "", // 解析提示文本
								"count": res.data.count, // 解析数据长度
								"data": res.data.list// 解析数据列表
							};
						}else{
							return {
								"code": -1, // 解析接口状态
								"msg": res.data.message, // 解析提示文本
								"count": res.data.count, // 解析数据长度
								"data":null// 解析数据列表
							};
						}

					}

				  });
				   table.on('tool(table-vehicle)',function (obj){
					   switch(obj.event){
						   case "control":{
							   window.open("/bridge?target=/Control/Console/"+obj.data.vehicleSerial);
							   break;
						   }
					   }
					 
				});
				  $("[name='toolbar-search']").on("submit",function(){
					  table.reload('table-vehicle', {
							page: {curr: 1},
							where: {
									type:$('[name="toolbar-search"]>vui-select[name="type"]>vui-selected').attr("value"),
									status:$('[name="toolbar-search"]>vui-select[name="status"]>vui-selected').attr("value"),
									keyword:$('[name="toolbar-search"]>input[name="keyword"]').val()
							}
						});

				  });
				  vui.use(["panel"], function () {

					  $(".add-btn").on("click",function(){

							new vui.Form({
								before:function(content){
									content.append($("#add-template").html());
								},
								yes:function(field){
									api.addVehicle(field.serial,field.vehicle_name,function(res){
										if(res.success){
											//刷新页面或添加到第一条
										}
									});
								}
							}).appendTo($("body"));
					  });
				  });



		});
		
	</script>
}
