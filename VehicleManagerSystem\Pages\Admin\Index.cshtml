﻿@page
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Options
@inject IOptions<RequestLocalizationOptions> localizationOptions
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Admin.IndexModel
@{
	ViewData["Title"] = language["PAGE_TITLE_ADMIN_INDEX"];
	var supportedCultures = localizationOptions.Value.SupportedUICultures;
}

@section Styles {
	<link href="~/css/vui.css" rel="stylesheet" />
}
<page>
	<div class="content">
		<div class="frame">
		</div>
		<div class="menu-row">
			<div class="vui-main-menu">
				<div class="vui-main-menu-item g1" vui-href="/admin/vehiclemanager">
					<i class="iconfont icon-multi-4"></i>
					<div>
						@Html.Raw(language["INDEX_MENU_VEHICLE_MANAGER"])
					</div>
				</div>
				<div class="vui-main-menu-item g2" vui-href="/admin/hangar">
					<i class="iconfont icon-jiku"></i>
					<div>
						@Html.Raw(language["INDEX_MENU_HANGAR_MANAGER"])
					</div>
				</div>
				<div class="vui-main-menu-item g3" vui-href="/admin/basestation">
					<i class="iconfont icon-gjizhan"></i>
					<div>
						@Html.Raw(language["INDEX_MENU_BASE_STATION_MANAGER"])
					</div>

				</div>
				<div class="vui-main-menu-item g4" vui-href="/admin/user">
					<i class="iconfont icon-touxiang_avatar"></i>
					<div>
						@Html.Raw(language["INDEX_MENU_USER_MANAGER"])
					</div>
				</div>
				<div class="vui-main-menu-item g5" vui-href="/admin/history">
					<i class="iconfont icon-jiku"></i>
					<div>
						@Html.Raw(language["INDEX_MENU_FLIGHT_LOGGER"])
					</div>
				</div>
				<div class="vui-main-menu-item g6" vui-href="/admin/mission">
					<i class="iconfont icon-jiku"></i>
					<div>
						@Html.Raw(language["INDEX_MENU_MISSION_MANAGER"])
					</div>
				</div>
			</div>
		</div>
		
	</div>
	@if (supportedCultures != null)
	{
		<div class="top-bar">
			<a class="control"  target="_blank" href="/bridge?target=/control/console">
				@Html.Raw(language["BUTTON_CONTROL"])
			</a>
			<div class="language ">
				<div class="current">@Html.Raw(language["LANGUAGE_NAME"])</div>
				<ul class="list">
					@foreach (var culture in supportedCultures)
					{
						<li locale="@culture.Name">@culture.DisplayName</li>
					} 
				</ul>
			</div>
		</div>
	}

	
</page>

@section Scripts{
	<script>
		function getScoped($element) {
			const attrs = Array.from($element[0].getAttributeNames());
			return attrs.find(attr => attr.startsWith('b-')) || null;
		}
		var base_title=document.title;
		var scoped_symbol=getScoped($(".content"));
		var go_to_page=function(target){
			if(!$("body").hasClass("content-body")){
				$("body").addClass("content-body");
			}
			var hash=target;
			if(target){
				target="/bridge?target="+target;
			}
			var frames=$(".frame");
			var target_frame=frames.find("[vui-href='"+target+"']");
			if(target_frame.length==0){
				 target_frame=$('<iframe src="'+target+'" vui-href="'+target+'"'+scoped_symbol+' ></iframe>').appendTo(frames);
				 target_frame[0].addEventListener('load', function () {
					var width=$(".menu-row").width();
					$(".frame").css("--left-menu-width",(width - 25+12)+"px");
					if(target_frame[0].contentDocument){
					   var iframeWindow = target_frame[0].contentDocument;
					   document.title=base_title + (iframeWindow.title?( "-" + iframeWindow.title):"");
					}
					
				 });
			}
			var cur=$(".vui-main-menu-item.selected");
			if(cur.attr("vui-href")==target){
				return;
			}
			$(".vui-main-menu-item.selected").removeClass("selected");
			$(".vui-main-menu-item[vui-href='"+hash+"']").addClass("selected");
			frames.find("*").removeClass("this");
			target_frame.addClass("this");
			location.hash="#"+hash;

			var width=$(".menu-row").width();
			$(".frame").css("--left-menu-width",(width - 25+12)+"px");
			if(target_frame[0].contentDocument){
				var iframeWindow = target_frame[0].contentDocument;
				document.title=base_title + (iframeWindow.title?( "-" + iframeWindow.title):"");
			}
			
		
		}
		window.addEventListener('message', function (event) {
			if (event.data.message === 'update_title') {
				var frames=$(".frame");
				var iframes=frames.find("iframe");
				let sourceIframe = null;
				iframes.each(function(){
					 if (event.source === $(this)[0].contentWindow) {
						sourceIframe = $(this);
						return true;
					 }
				});
				if(sourceIframe && sourceIframe.hasClass("this")){
					var iframeWindow = sourceIframe[0].contentDocument;
					document.title=base_title + (iframeWindow.title?( "-" + iframeWindow.title):"");
					console.log(document.title)
				}
				 
			}
		});
		$(".vui-main-menu-item").on("click",function(){ 
			var target=$(this).attr("vui-href");
			go_to_page(target);
			
		});
		$(".language").on("click",function(e){
			$(this).toggleClass("open");
			e.stopPropagation();
			e.preventDefault();
		});
		$(".language>ul>li").on("click",function(e){
			var locale=$(this).attr("locale");
			location.href="/setLanguage?locale="+locale+"&source="+encodeURIComponent(location.href);
			e.stopPropagation();
			e.preventDefault();
		});
		$(document).on("click",function(){
			$(".language.open").removeClass("open");
		});
		if(location.hash && location.hash.indexOf("#/")==0){
			go_to_page(location.hash.substring(1,location.hash.length));
		}
	</script>
}