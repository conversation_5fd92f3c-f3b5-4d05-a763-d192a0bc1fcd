﻿using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace VehicleManagerSystem.Utils
{
    public class EnumsUtil
    {
        public static string DisplayName(Enum enum_val)
        {
            var type = enum_val.GetType();
            var field = type.GetField(enum_val.ToString());
            var display = field?.GetCustomAttribute<DisplayAttribute>();
            return display?.Name??"UNDEFINE";
        }
    }
}
