﻿using System.Security.Cryptography;
using System.Text;
using Dm.filter;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using VehicleManagerSystem.Services;
using VehicleManagerSystem.Tables;
using VehicleManagerSystem.Utils;

namespace VehicleManagerSystem.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AccountController(DataBaseService database, IStringLocalizer<LanguageResource> language) : ControllerBase
    {
        public class LoginInfo
        {
            public required string username { get; set; }
            public required string password { get; set; }
        }
        [HttpPost("login")]
        public async Task<object> Login(LoginInfo login)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                
                var userInfo =await sql.Queryable<TableUser>().Where(it => it.UserName == login.username).FirstAsync();
                if(userInfo == null)
                {
                    //用户名错误
                    return ApiResult.Failed(language["ERROR_USERNAME_OR_PASSWORD"]) ;
                }
                if(userInfo.Password!= string.Join("", MD5.HashData(Encoding.UTF8.GetBytes(login.password)).Select(it => it.ToString("x2"))))
                {
                    //密码错误
                    return ApiResult.Failed(language["ERROR_USERNAME_OR_PASSWORD"]);
                }
                if (userInfo.UserStatus != UserStatus.Normal) {
                    //当前用户状态
                    return ApiResult.Failed(language["ERROR_LOGIN_DISABLED"]);
                }
                if(!database.UserEnabled(userInfo.Id))
                {
                
                    //继承用户状态
                    return ApiResult.Failed(language["ERROR_LOGIN_DISABLED"]);
                }
                string user_token = string.Join("", MD5.HashData(Encoding.UTF8.GetBytes($"{Guid.NewGuid()}-{Environment.TickCount64}{DateTimeOffset.UtcNow.AddMilliseconds}")).Select(it => it.ToString("x2")));
                try
                {
                    if (await sql.Insertable<TableUserToken>(new TableUserToken()
                    {
                        UserId = userInfo.Id,
                        IPAddress = Request.HttpContext.Connection?.RemoteIpAddress?.MapToIPv4().ToString()!,
                        UserAgent = Request.Headers.UserAgent.FirstOrDefault()!,
                        LoginTime = DateTime.Now,
                        UserToken = string.Join("", MD5.HashData(Encoding.UTF8.GetBytes(user_token + "@@112235##")).Select(it => it.ToString("x2"))),

                    }).ExecuteCommandAsync() == 0)
                    {
                        return ApiResult.Failed(language["ERROR_UNKNOW"]);
                    }
                }
                catch
                {
                    return ApiResult.Failed(language["ERROR_UNKNOW"]);
                }

                switch (userInfo.UserRole)
                {
                    case UserRole.Monitor:
                        return ApiResult.Success(new
                        {
                            uid=userInfo.Id,
                            token=user_token,
                            url = "/Control/monitor"//飞行监控页面

                        });
                    case UserRole.Controller:
                        return ApiResult.Success(new
                        {
                            uid = userInfo.Id,
                            token = user_token,
                            url = "/Control/console"//飞行控制台
                        });
                    case UserRole.ThirdPartyAdmin:
                        return ApiResult.Success(new
                        {
                            uid = userInfo.Id,
                            token = user_token,
                            url = "/Admin/index"    //三方管理员后台
                        });
                    case UserRole.SuperAdmin:
                        return ApiResult.Success(new
                        {
                            uid = userInfo.Id,
                            token = user_token,
                            url = "/Admin/index"   //超级管理员后台
                        });
                    default:
                        break;
                }

                return ApiResult.Failed(language["ERROR_UNKNOW"]);
            });
        }

         public class UserListBizContext
        {
            public UserRole type { get; set; } = UserRole.None;
            public UserStatus status{  get; set; }=UserStatus.None;
            public string? keyword{  get; set; }=null;

        }
        [HttpGet("list")]
        public async Task<object> List(int page,int limit,[FromQuery]UserListBizContext  biz_context)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var query = sql.Queryable<TableUser>();
                if (biz_context != null)
                {
                    if (biz_context.type != UserRole.None)
                    {
                        query.Where(it => it.UserRole == biz_context.type);
                    }
                    if (biz_context.status != UserStatus.None)
                    {
                        query.Where(it => it.UserStatus == biz_context.status);
                    }
                    if (!string.IsNullOrWhiteSpace(biz_context?.keyword)){
                          query.Where(it => it.UserName.Contains(biz_context.keyword) ||
                                        it.NickName.Contains(biz_context.keyword));
                    }
                }
               
                return ApiResult.Success( new {
                      count = await query.Clone().CountAsync(),
                      list  = await query.ToPageListAsync( page,limit),
                      code = 0
                });
            });
        }

        [HttpGet("install")]
        public async Task<object> Install()
        {
            try
            {
                if (await database.SqlExecuteAsync(async sql =>
                {
                    var account = await sql.Insertable<TableUser>(new TableUser()
                    {
                        NickName = "超级管理员",
                        ParentUserId = 0,
                        Password = string.Join("", MD5.HashData(Encoding.UTF8.GetBytes("ss123456")).Select(it => it.ToString("x2"))),
                        UserRole = UserRole.SuperAdmin,
                        UserStatus = UserStatus.Normal,
                        UserName = "reuncc"
                    }).ExecuteReturnEntityAsync();
                    if (account != null)
                    {
                        database.UpdateUserEnabled(account);
                    }
                    return account != null;
                }))
                {
                    return ApiResult.Success(new
                    {
                        result = "初始化成功"
                    });
                }
                else
                {
                    return ApiResult.Success(new
                    {
                        result = "初始化失败"
                    });
                }
            }
            catch
            {
                return ApiResult.Success(new
                {
                    result = "初始化失败"
                });
            }
       }
    }
}
