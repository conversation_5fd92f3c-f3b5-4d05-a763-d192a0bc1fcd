using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using SqlSugar;
using System.Security.Cryptography;
using System.Text;
using VehicleManagerSystem.Services;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Pages.Control
{
    public class MissionModel(DataBaseService database, IStringLocalizer<LanguageResource> language) : Microsoft.AspNetCore.Mvc.RazorPages.PageModel
    {
        [BindProperty(SupportsGet = true)]
        public required string id { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? uid { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? nonce { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? token { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            return Page();
        }
    }
}
