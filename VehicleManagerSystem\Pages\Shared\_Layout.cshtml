﻿@using Microsoft.Extensions.Localization 
@inject IStringLocalizer<LanguageResource> language
@{
    var language_list = @language.GetAllStrings(true);
}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="~/css/ui.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/font/weilai/weilai.css" rel="stylesheet" />
	<link href="~/font/iconfont.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/VehicleManagerSystem.styles.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    @RenderBody()
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
	<script src="~/js/vui.js"></script>
    <script>
        window.constants={ };
        @foreach (var item in language_list){
                @Html.Raw($"window.constants.{item.Name}=\"{item.Value}\";\n");
       }  
     </script>
    <script src="~/js/ui.js" asp-append-version="true"></script>
	<script src="~/js/api.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>