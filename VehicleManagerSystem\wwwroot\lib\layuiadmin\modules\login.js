﻿


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
}).define(['jquery','form'], function (exports) {
    const layer = layui.layer, 
          form = layui.form, 
          $ = layui.jquery;
     
    let login_process=function (user, pwd,  google){
        $.ajax({
           url:"/api/account/login",
           type:"post",
           dataType: "json",
           loading:true,
           data: JSON.stringify({
                username: user,
                password: pwd,
                google: google,
           }),
           success:function (res){
               if(res.success){
                   if(!res.body.google){
                      localStorage.setItem("token", res.body.token);
                      location.href = "/bridge?target=/admin/index";
                   }else{
                       layer.prompt({
                           formType: 0,
                           title: '请输入谷歌验证码',
                           value: '',
                           offset: ["165px"],
                           success: (layero, index, that) => {
                               layui.$(layero).find('input').attr('type', 'number');
                               layui.form.render(layui.$(layero).find('input'));
                           }
                       }, function (value, index, elem) {
                           if (isNaN(value) || value.trim() === "") {
                               layer.tips('请输入正确的验证码', this);
                               return;
                           }
                           login_process(user, pwd,value);
                           layer.close(index);
                       });
                   }
               }
           } 
        });
    }
    form.on("submit(login)", function (data) {
        data=data.field;
        login_process(data.username,data.password,0);
        return false; 
    });
    
    exports('login', {});
});
 