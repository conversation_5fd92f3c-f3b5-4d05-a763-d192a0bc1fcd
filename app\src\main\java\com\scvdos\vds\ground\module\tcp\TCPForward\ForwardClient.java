package com.scvdos.vds.ground.module.tcp.TCPForward;

import android.util.Log;

import java.io.IOException;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class ForwardClient implements Runnable{
    public static final String TAG="MODULE-ForwardClient";
    private ForwardService m_service;
    private  Socket m_socket;
    private Thread m_thread=null;
    private Thread m_send_thread=null;
    private final Object m_mutex=new Object();

    public ForwardClient(ForwardService service,Socket socket){
        m_socket=socket;
        m_service=service;
    }

    public void loop(){
        Log.d(TAG, "loop: ---------client");
        synchronized (m_mutex) {
            if (m_thread == null) {
                if (m_socket == null) return;
                m_thread = new Thread(this);
                Log.d(TAG, "loop: ---------begin");
                m_thread.start();
                m_send_thread=new Thread(m_send_process);
                m_send_thread.start();
            }
        }
    }
    private final   ClientHandler m_handler= new ClientHandler() {
        @Override
        public void send(byte[] buffer, int offset, int size) {
            synchronized (m_mutex) {
                try {
                    if (m_socket != null) {
                        m_socket.getOutputStream().write(buffer, offset, size);
                    }
                } catch (IOException ignored) {

                }
            }
        }

        @Override
        public void close() {
            synchronized (m_mutex) {
                if (m_socket != null) {
                    try {
                        m_socket.close();
                    } catch (IOException ignored) {

                    }
                }
            }
        }
    };
    @Override
    public void run() {
        byte[] buffer=new byte[1024];
        while (!Thread.currentThread().isInterrupted()){
            try {
              int ready=  m_socket.getInputStream().read(buffer);
              if(ready<=0)break;
              Log.d(TAG, "run: RECV SIZE:"+ready);
              on_message(buffer,ready);
              buffer=new byte[1024];
            } catch (IOException ignored) {
                break;
            }
        }
        m_handler.close();
        m_thread=null;
        m_socket=null;
        m_send_thread.interrupt();
        m_service.remove(this);
    }
    private void on_message(int msgid,byte[]message){
        m_service.onMessage(msgid,message,this);
    }

     int          decode_step = 0;
     byte[]      message_payload=new byte[1024];
     int          message_payload_length = 0;
    private void on_message(byte[] bytes,int size){
        StringBuilder sb=new StringBuilder();
        for(int n=0;n<size;n++){
            sb.append(String.format("%02x ",bytes[n] &0xff));
        }
        Log.d(TAG, "run: RECV DATA:"+sb.toString());
        for(int n=0;n<size;n++){
            byte byt = bytes[n];
            switch (decode_step) {
                case 0: {
                    if ((byt &0xff) == 0xFF) {
                        decode_step = 1;
                        message_payload_length = 0;
                    }
                    break;
                }
                case 1: {
                    if ((byt &0xff) == 0xFF) {
                        decode_step = 0;

                        byte[] message =unpacket(message_payload, message_payload_length);
                        if (message!=null && message.length > 0) {
                            xor_checksum(message, message.length);
                            if ((message[message.length - 1] &0xff) == 0xCA) {
                                ByteBuffer buffer=ByteBuffer.wrap(message);
                                buffer.order(ByteOrder.LITTLE_ENDIAN);
                                int msgid= buffer.get() &0xff;
                                long msg_len=buffer.getInt() &0xffffffffL;
                                byte[] msg_ptr=new byte[(int)msg_len];
                                buffer.get(msg_ptr,0,(int)msg_len);
                                on_message(msgid,msg_ptr);

                            }
                        }
                    }
                    else {
                        if (message_payload_length >= 1024) {
                            message_payload_length = 0;
                            decode_step = 0;
                        }
                        else {
                            message_payload[message_payload_length++] = byt;
                        }
                    }
                    break;
                }
            }
        }
    }

    private final BlockingQueue<byte[]> send_pool=new LinkedBlockingQueue<>();
    private final Runnable m_send_process= () -> {
        while (!Thread.currentThread().isInterrupted()){
                try {
                    var buff=send_pool.take();
                    m_handler.send(buff,0,buff.length);
                } catch (InterruptedException e) {
                  Thread.currentThread().interrupt();
                }
        }
    };
    public void send(int msgid,byte[] message){
          byte[] data=new byte[message.length+1+4+1];
          ByteBuffer buffer=ByteBuffer.wrap(data);
          buffer.order(ByteOrder.LITTLE_ENDIAN);
          buffer.put((byte)msgid);
          buffer.putInt(message.length);
          buffer.put(message);
          buffer.put((byte) 0xca);
          xor_checksum(data,data.length);
          int max_size = data.length* 2 + 2;
          byte[] data_real=new byte[max_size];
          int size_total = packet(data_real, data, data.length);
          byte[] data_real_send=Arrays.copyOf(data_real,size_total);
          send_pool.add(data_real_send);

    }
    public interface ClientHandler{
        void send(byte[] buffer,int offset,int size);
        void close();
    }


    //原始包解格式化
    byte[] unpacket(byte[] src, int src_size) {
        byte[] src_bytes = new byte[src_size];
        int src_index = 0;
        for (int i = 0;i < src_size;i++) {
            if ((src[i] &0xff)== 0xFE) {
                i++;
                if (src[i] == 0x01) {
                    src_bytes[src_index++] = (byte)0xFF;
                }
                else if (src[i] == 0x02) {
                    src_bytes[src_index++] =  (byte)0xFE;
                }
            }
            else {
                src_bytes[src_index++] = src[i];
            }
        }
        if (src_index == 0)return null;
        return   Arrays.copyOf(src_bytes,src_index);
    }
    //最终包格式化
    int packet(  byte[] dest,byte[] src, int src_len) {
        int dest_index = 0;
        dest[dest_index++] =(byte) 0xFF;
        for (int n = 0;n < src_len;n++) {
            byte src_byt = src[n];
            if ( (src_byt&0xFF) == 0xFF) {
                dest[dest_index++] = (byte)0xFE;
                dest[dest_index++] = 0x01;

            }
            else if ((src_byt&0xFF)  == 0xFE) {
                dest[dest_index++] = (byte)0xFE;
                dest[dest_index++] = 0x02;
            }
            else {
                dest[dest_index++] = src_byt;
            }
        }
        dest[dest_index++] =(byte) 0xFF;
        return dest_index;
    }
    //异或校验
    void xor_checksum(byte[] data, int size) {
        byte xor_t=data[size - 1];
        for (int n = 0;n < size-1;n++) {
            xor_t ^= data[n];
        }
        data[size - 1]=xor_t;
    }
    //发送示例

//接收示例
}
