package com.scvdos.vds.ground.module.tcp.MQTTForward;

import static com.scvdos.vds.ground.module.tcp.TCPForward.ForwardService.buildPayloadCommand;

import android.os.AsyncTask;
import android.os.Build;
import android.util.Log;

import com.scvdos.vds.ground.api.messages.vdoslink.constants.MAV_FRAME;
import com.scvdos.vds.ground.module.tcp.Manager;

import org.eclipse.paho.mqttv5.client.IMqttToken;
import org.eclipse.paho.mqttv5.client.MqttActionListener;
import org.eclipse.paho.mqttv5.client.MqttAsyncClient;
import org.eclipse.paho.mqttv5.client.MqttCallback;
import org.eclipse.paho.mqttv5.client.MqttClientPersistence;
import org.eclipse.paho.mqttv5.client.MqttConnectionOptions;
import org.eclipse.paho.mqttv5.client.MqttDisconnectResponse;
import org.eclipse.paho.mqttv5.client.persist.MemoryPersistence;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.eclipse.paho.mqttv5.common.packet.MqttProperties;
import org.eclipse.paho.mqttv5.common.packet.UserProperty;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * MQTT客户端类 - 负责处理与MQTT服务器的通信
 * 实现了MqttCallback和Runnable接口，用于异步处理MQTT消息和连接管理
 *
 * 主要功能：
 * 1. 连接MQTT服务器并维持连接
 * 2. 订阅飞行器控制相关主题
 * 3. 处理远程控制命令并转发给飞行器
 * 4. 发布飞行器状态信息和命令执行结果
 *
 * <AUTHOR> Ground Module
 * @version 1.0
 */
public class MQTTClient implements MqttCallback,Runnable {
    /** 日志标签 */
    public final static String TAG="MQTTClient";

    /** MQTT异步客户端实例 */
    private   MqttAsyncClient m_client ;

    /** MQTT服务器主机地址 */
    private final String m_host;

    /** MQTT服务器端口号 */
    private final int m_port;

    /** 设备唯一标识ID */
    private final String m_device_id;

    /** MQTT连接用户名 */
    private final String m_username;

    /** MQTT连接密码 */
    private final String m_password;

    /** 管理器实例，用于执行飞行器控制命令 */
    private final Manager m_service;

    /**
     * MQTT客户端构造函数
     *
     * @param manager 管理器实例，用于执行飞行器控制命令
     * @param device_id 设备唯一标识ID，用于MQTT主题订阅和消息发布
     * @param host MQTT服务器主机地址
     * @param port MQTT服务器端口号
     * @param username MQTT连接用户名
     * @param password MQTT连接密码
     */
    public MQTTClient(Manager manager, String device_id, String host, int port, String username, String password){
        m_host=host;
        m_port=port;
        m_device_id=device_id;
        m_username=username;
        m_password=password;
        m_service=manager;
    }

    /** 运行状态标志 */
    private boolean m_running=false;

    /** 连接线程 */
    private Thread m_thread=null;

    /**
     * 启动MQTT客户端
     * 创建并启动连接线程，开始尝试连接MQTT服务器
     */
    public void start(){
        if(m_running)return;
        m_running=true;
        m_thread=new Thread(this);
        m_thread.start();
    }
    /** 连接同步锁对象 */
    private final Object m_connect_mutex=new Object();

    /** 是否清理会话标志 */
    private final boolean m_clean_start=true;

    /**
     * 连接MQTT服务器
     * 配置连接选项并尝试建立与MQTT服务器的连接
     *
     * @return true-连接成功，false-连接失败
     */
    private boolean connect_mqtt(){
        Log.d(TAG, "connect_mqtt: connect begin");

        // 配置MQTT连接选项
        MqttConnectionOptions options = new MqttConnectionOptions();
        options.setCleanStart(m_clean_start);           // 清理会话
        options.setSessionExpiryInterval(3600L);        // 会话过期时间：1小时
        options.setConnectionTimeout(5000);             // 连接超时：5秒
        options.setUserName(m_username);                // 设置用户名
        options.setPassword(m_password.getBytes(StandardCharsets.US_ASCII)); // 设置密码
        options.setAutomaticReconnect(true);            // 启用自动重连
        options.setAutomaticReconnectDelay(1000,2000);  // 重连延迟：1-2秒

        try {
            // 创建内存持久化存储
            MqttClientPersistence persistence = new MemoryPersistence();

            // 创建MQTT异步客户端
            m_client=new MqttAsyncClient("tcp://"+m_host+":"+m_port, m_device_id,persistence);
            m_client.setCallback(this);

            // 执行连接并等待完成
            IMqttToken conToken =  m_client.connect(options);
            conToken.waitForCompletion();
            return true;
        } catch (MqttException ignored) {
            ignored.printStackTrace();
            Log.d(TAG, "connect_mqtt: connect failed"+m_host+":"+m_port+","+m_device_id);

            // 连接失败时等待2秒后重试
            if(m_running){
                synchronized (m_connect_mutex){
                    try {
                        m_connect_mutex.wait(2000);
                    } catch (InterruptedException ignored1) {
                        // 忽略中断异常
                    }
                }
            }
        }
        return false;
    }
    /**
     * MQTT连接断开回调
     * 当MQTT连接意外断开时被调用
     *
     * @param disconnectResponse 断开连接响应信息
     */
    @Override
    public void disconnected(MqttDisconnectResponse disconnectResponse) {
        Log.d(TAG, "connect_mqtt: disconnected");
        // 注释掉的代码：自动重连逻辑已在连接选项中配置
        // 这里可以添加额外的断开连接处理逻辑
    }

    /**
     * MQTT错误发生回调
     * 当MQTT通信过程中发生错误时被调用
     *
     * @param exception MQTT异常信息
     */
    @Override
    public void mqttErrorOccurred(MqttException exception) {
        Log.d(TAG, "mqttErrorOccurred: ");
    }

    /*
     * ========== MQTT主题定义和远程控制功能说明 ==========
     *
     * 远程控制系统架构：
     * 1. 控制鉴权 - 有UI的请求权限时，界面弹出权限请求窗口，确定后界面显示正在远程受控
     *
     * 用户界面页面：
     * - 页面1: 视频控制UI界面
     * - 页面2: 载荷控制UI界面
     * - 页面3: 视频显示UI界面
     *
     * 载荷控制功能：
     * - 获取载荷列表（型号 - 需要兼容远程控制）
     * - 控制模板（网页地址，通过POSTMESSAGE与父窗口iframe通信）
     *
     * 摄像头控制功能：
     * - 获取摄像头列表（型号 - 需要兼容远程控制）
     * - 控制模板（网页地址，通过POSTMESSAGE与父窗口iframe通信）
     * - 视频模板
     * - 基础控制：上下左右、拍照录像、SD卡操作
     * - 高级功能：一键上下、红外广角变焦、测距测温度、放大缩小还原
     * - 状态信息：yaw-pitch-roll-录像状态
     * - 请求摄像头推流
     */

    // ========== MQTT主题常量定义 ==========

    /** 飞行指令控制主题前缀 */
    private final static String topic_vehicle_flight_command_control="/vehicle/control/flight/command/";

    /** 引导飞行位置控制主题前缀 */
    private final static String topic_vehicle_flight_position_control="/vehicle/control/flight/position/";

    /** 飞机载荷控制主题前缀 */
    private final static String topic_vehicle_payload_control="/vehicle/control/payload/";

    /** 飞机摄像头控制主题前缀 */
    private final static String topic_vehicle_camera_control="/vehicle/control/camera/";

    /** 飞机任务控制主题前缀 */
    private final static String topic_vehicle_mission_control="/vehicle/control/mission/";

    /** 控制指令回调主题格式 - 参数：发送响应的设备ID/接收响应的设备ID */
    private final static String topic_vehicle_control_response="/vehicle/control/response/%s/%s";

    /**
     * 发送飞行器控制命令响应（无数据）
     *
     * @param method 控制方法类型（如：flight, mission, payload等）
     * @param source 命令来源设备ID
     * @param success 命令执行是否成功
     * @param error_code 错误代码（成功时为0）
     * @param command 具体的命令名称
     */
    public void vehicle_response(String method,String source,boolean success,int error_code,String command){
        vehicle_response(method,source,success,error_code,command,null);
    }

    /**
     * 发送飞行器控制命令响应（带数据）
     * 构建JSON响应消息并发布到MQTT响应主题
     *
     * @param method 控制方法类型（如：flight, mission, payload等）
     * @param source 命令来源设备ID
     * @param success 命令执行是否成功
     * @param error_code 错误代码（成功时为0）
     * @param command 具体的命令名称
     * @param data 响应数据对象（可为null）
     */
    public void vehicle_response(String method,String source,boolean success,int error_code,String command,Object data){
        try{
            // 构建JSON响应消息
            JSONObject json=new JSONObject();
            json.put("method",method);      // 控制方法
            json.put("action",command);     // 执行的动作
            json.put("status",success);     // 执行状态
            json.put("code",error_code);    // 错误代码
            json.put("data",data);          // 响应数据

            // 创建MQTT消息
            MqttMessage message = new MqttMessage(json.toString().getBytes(StandardCharsets.UTF_8));

            // 设置消息属性，包含客户端ID
            var prop=new MqttProperties();
            List<UserProperty> list=new ArrayList<>();
            list.add(new UserProperty("clientId",m_device_id));
            prop.setUserProperties(list);
            message.setProperties(prop);
            message.setQos(1);  // 设置QoS为1，确保消息至少传递一次

            // 发布响应消息到指定主题
            m_client.publish(String.format(topic_vehicle_control_response,m_device_id,source), message);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }
    }

    /**
     * MQTT消息到达回调
     * 当订阅的主题收到消息时被调用，负责解析和处理各种控制命令
     *
     * @param topic 消息主题
     * @param message MQTT消息对象
     * @throws Exception 处理异常
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {

        try {
            // 提取消息发布者的客户端ID
            List<UserProperty> userProperties = message.getProperties().getUserProperties();
            String publisherClientId = userProperties.stream()
                .filter(prop -> "clientId".equals(prop.getKey()))
                .findFirst()
                .map(UserProperty::getValue)
                .orElse(null);

            // 获取消息载荷并转换为JSON
            var payload = message.getPayload();
            String json = new String(payload, StandardCharsets.UTF_8);
            Log.d(TAG, "messageArrived[" + publisherClientId + "]: " + json +"{"+topic+"}");
            JSONObject jsonObject = new JSONObject(json);

            // 忽略重复的QoS=1消息
            if (message.getQos() == 1 && message.isDuplicate()) {
                return;
            }

            // ========== 处理飞行指令控制消息 ==========
            if (topic.startsWith(topic_vehicle_flight_command_control)) {
                Log.d(TAG, "topic_vehicle_flight_command_control");
                var command = jsonObject.getString("action");

                switch (command) {
                    case "takeoff": {
                        // 起飞命令 - 需要指定起飞高度
                        var data = jsonObject.getJSONObject("data");
                        var alt = data.getInt("alt");
                        if (!m_service.CommandTakeOff(alt, (b, i) -> {
                            vehicle_response("flight",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("flight",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "auto": {
                        // 自动模式命令 - 开始执行任务
                        if (!m_service.CommandMissionStart((b, i) -> {
                            vehicle_response("flight",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("flight",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "land": {
                        // 降落命令
                        if (!m_service.CommandLand((b, i) -> {
                            vehicle_response("flight",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("flight",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "rtl": {
                        // 返航命令 - Return To Launch
                        if (!m_service.CommandRTL((b, i) -> {
                            vehicle_response("flight",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("flight",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "pause": {
                        // 暂停任务命令
                        if (!m_service.CommandMissionPause((b, i) -> {
                            vehicle_response("flight",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("flight",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "resume": {
                        // 恢复任务命令
                        if (!m_service.CommandMissionResume((b, i) -> {
                            vehicle_response("flight",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("flight",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                }
            }
            // ========== 处理任务控制消息 ==========
            else if (topic.startsWith(topic_vehicle_mission_control)) {
                var command = jsonObject.getString("action");
                switch (command) {
                    case "request_list":{
                        // 请求任务列表
                        if (m_service.CommandRequestMission((b, i,data) -> {
                            vehicle_response("mission",publisherClientId, b == 1, i, command,data);
                        })) {
                            vehicle_response("mission",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "update_list":{
                        // 更新任务列表

                        if (!m_service.CommandAutoMission(payload, (b, i) -> {
                            vehicle_response("mission",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("mission",publisherClientId, false, 0, command);
                        }
                        break;
                    }
                    case "upload": {
                        // 上传任务
                        String  data =  jsonObject.getString("data");
                        byte[] bytes = DataChangeUitl.parseByByteArray(data.toString());
                        if (!m_service.CommandAutoMission(bytes, (b, i) -> {
                            vehicle_response("mission",publisherClientId, b == 1, i, command);
                        })) {
                            vehicle_response("mission",publisherClientId, false, 0, command);
                        }
                        break;
                    }

                }
            }
            // ========== 处理载荷控制消息 ==========
            else if (topic.startsWith(topic_vehicle_payload_control)) {
                // 载荷控制命令 - 通过MAVLink命令长消息发送
                var action = jsonObject.getString("action");
                ByteBuffer msg = ByteBuffer.wrap(payload);
                msg.order(ByteOrder.LITTLE_ENDIAN);
                var command = buildPayloadCommand(msg);
                if (!m_service.CommandCommandLong(command, (b, i) -> {
                    vehicle_response("payload",publisherClientId, b == 1, i, action);
                })) {
                    vehicle_response("payload",publisherClientId, false, 0, action);
                }
            }
            // ========== 处理飞行位置控制消息 ==========
            else if (topic.startsWith(topic_vehicle_flight_position_control)) {
                // 引导模式位置控制 - 支持前进、后退、左移、右移
                var action = jsonObject.getString("action");
                var data=jsonObject.getJSONObject("data");
                var distance=(float)data.getDouble("distance");  // 移动距离

                // 初始化位置和速度参数（NaN表示不使用该参数）
                var x =Float.NaN;       // 前后方向位移
                var y =Float.NaN;       // 左右方向位移
                var z = Float.NaN;      // 上下方向位移
                var vx =Float.NaN;      // 前后方向速度
                var vy = Float.NaN;     // 左右方向速度
                var vz =Float.NaN;      // 上下方向速度
                var yaw =0;             // 偏航角
                var yaw_rate =Float.NaN; // 偏航角速度

                // 根据动作类型设置对应的位移参数
                switch (action){
                    case "forward":{
                        // 前进 - X轴正方向
                        x=distance;
                        y=0;
                        z=0;
                        break;
                    }
                    case "retreat":{
                        // 后退 - X轴负方向
                        x=-distance;
                        y=0;
                        z=0;
                        break;
                    }
                    case "left":{
                        // 左移 - Y轴负方向
                        x=0;
                        y=-distance;
                        z=0;
                        break;
                    }
                    case "right":{
                        // 右移 - Y轴正方向
                        x=0;
                        y=distance;
                        z=0;
                        break;
                    }
                }

                // 发送引导控制命令（使用机体坐标系NED）
                if (!m_service.CommandGuidedControl(MAV_FRAME.MAV_FRAME_BODY_OFFSET_NED, x, y, z, vx, vy, vz, yaw, yaw_rate, (b, i) -> {
                    vehicle_response("position",publisherClientId, false, 0, action);
                })) {
                    vehicle_response("position",publisherClientId, false, 0, action);
                }
            }
            // ========== 处理摄像头控制消息 ==========
            else if (topic.startsWith(topic_vehicle_camera_control)) {
                // TODO: 实现摄像头控制逻辑
                // 包括：云台控制、拍照录像、变焦、红外切换等功能
            }
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    /**
     * 消息发布完成回调
     * 当MQTT消息成功发布到服务器时被调用
     *
     * @param token MQTT令牌，包含发布操作的相关信息
     */
    @Override
    public void deliveryComplete(IMqttToken token) {
        // 消息发布完成 - 可在此处添加发布成功的处理逻辑
    }

    /**
     * MQTT连接完成回调
     * 当成功连接到MQTT服务器时被调用，负责订阅相关主题
     *
     * @param reconnect 是否为重连（true-重连，false-首次连接）
     * @param serverURI 服务器URI
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        // 首次连接或清理会话的重连时才订阅主题
        if(!reconnect || m_clean_start){
            Log.d(TAG, "connectComplete: --------connected-"+m_device_id);
            try {
                // 订阅所有控制相关主题，QoS设置为1
                m_client.subscribe(topic_vehicle_flight_command_control+m_device_id,1);    // 飞行命令控制
                m_client.subscribe(topic_vehicle_flight_position_control+m_device_id,1);   // 飞行位置控制
                m_client.subscribe(topic_vehicle_payload_control+m_device_id,1);           // 载荷控制
                m_client.subscribe(topic_vehicle_camera_control+m_device_id,1);            // 摄像头控制
                m_client.subscribe(topic_vehicle_mission_control+m_device_id,1);           // 任务控制
            } catch (MqttException ignored) {
                Log.d(TAG, "connectComplete: --------error");
                ignored.printStackTrace();
            }
        }else{
            Log.d(TAG, "connectComplete: --------cancel");
        }
    }

    /**
     * 认证数据包到达回调
     * 当收到MQTT认证相关数据包时被调用
     *
     * @param reasonCode 认证原因代码
     * @param properties MQTT属性
     */
    @Override
    public void authPacketArrived(int reasonCode, MqttProperties properties) {
        // 认证处理 - 可在此处添加认证相关逻辑
        Log.d(TAG, "authPacketArrived: ");
    }

    /**
     * 线程运行方法
     * 持续尝试连接MQTT服务器，直到连接成功或线程被中断
     * 实现Runnable接口的run方法
     */
    @Override
    public void run() {
        while (!Thread.currentThread().isInterrupted()){
           // 尝试连接MQTT服务器，连接成功则退出循环
           if(connect_mqtt())return;
        }
    }

    /** 飞行器状态消息发布主题格式 - 参数：设备ID */
    private final static String topic_vehicle_status_message="/vehicle/status/message/%s";

    /**
     * 发布飞行器心跳状态消息
     * 将飞行器的实时状态信息发布到MQTT主题，供远程监控使用
     *
     * @param json 包含飞行器状态信息的JSON字符串
     *             包括：位置、姿态、飞行模式、任务状态、电池电量等信息
     */
    public void publish_heartbeat(String json){
        // 创建MQTT消息
        MqttMessage message = new MqttMessage(json.getBytes(StandardCharsets.UTF_8));

        // 设置消息属性，包含发布者客户端ID
        var prop=new MqttProperties();
        List<UserProperty> list=new ArrayList<>();
        list.add(new UserProperty("clientId",m_device_id));
        prop.setUserProperties(list);
        message.setProperties(prop);
        message.setQos(1);  // 设置QoS为1，确保消息至少传递一次

        try {
            // 发布心跳消息到状态主题
            m_client.publish(String.format(topic_vehicle_status_message,m_device_id), message);
        } catch (MqttException ignored) {
            ignored.printStackTrace();
        }
    }
    public static class DataChangeUitl{
        // 解析JSON数据到对象
        public static MissionData aparse(String jsonData) throws JSONException {
            MissionData missionData = new MissionData();

            JSONObject json = new JSONObject(jsonData);
            missionData.Id = json.getInt("Id");
            missionData.MissionName = json.getString("MissionName");
            missionData.VehicleId = json.getInt("VehicleId");
            missionData.MissionDescription = json.getString("MissionDescription");

            missionData.Speed = json.getInt("Speed");
            missionData.Height = json.getInt("Height");
            missionData.CreateTime = json.getString("CreateTime");

            // 解析航点数据
            org.json.JSONArray pointArray = json.getJSONArray("PointData");
            for (int i = 0; i < pointArray.length(); i++) {
                JSONObject pointObj = pointArray.getJSONObject(i);
                PointData point = new PointData();
                point.Id = pointObj.getInt("Id");
                point.Longitude = pointObj.getDouble("Longitude");
                point.Latitude = pointObj.getDouble("Latitude");
                point.Altitude = pointObj.getDouble("Altitude");
                missionData.PointData.add(point);
            }
            return missionData;
        }
        //将json转为16进制byte数组
        public static byte[] parseByByteArray(String json) throws IOException, JSONException{
            JSONObject jsonObj = new JSONObject(json);
            ByteBuffer buffer = ByteBuffer.allocate(1024); // 初始容量设为1024，可根据实际情况调整
            buffer.order(ByteOrder.LITTLE_ENDIAN);

            // 航线全局速度 (uint8)
            buffer.put( (byte) jsonObj.getInt("Speed"));

            // 航线全局高度 (uint16)
            buffer.putShort((short) jsonObj.getInt("Height"));

            // 上传动作 (uint8)
            buffer.put((byte) jsonObj.getInt("UploadStatus") ); // 默认值为0，仅上传航线

            // 航点数量 (uint8)
            org.json.JSONArray pointArray = jsonObj.getJSONArray("PointData");
            buffer.put((byte) pointArray.length());

            // 遍历每个航点并添加到缓冲区
            for (int i = 0; i < pointArray.length(); i++) {
                JSONObject pointObj = pointArray.getJSONObject(i);

                // 纬度 (int32)
                long lat = (long) (new BigDecimal(pointObj.getDouble("Latitude")+ 90).setScale(7, RoundingMode.HALF_UP).doubleValue()  * 1e7);
                buffer.putInt((int) lat);
                // 经度 (int32)

                long lng = (long) (new BigDecimal(pointObj.getDouble("Longitude")+ 180).setScale(7, RoundingMode.HALF_UP).doubleValue()* 1e7);
                buffer.putInt((int) lng);

                // 相对高度 (int16)
                short height = (short) (new BigDecimal(pointObj.getDouble("Altitude")).setScale(2, RoundingMode.HALF_UP).doubleValue());
                buffer.putShort(height);
                // 航向 (int16)
                short heading = (short) (0XFFFF);
                buffer.putShort(heading);

                // 任务执行时间 (uint8)
                buffer.put((byte) 0X00);
            }

            // 获取实际使用的字节数组
            byte[] result = new byte[buffer.position()];
            System.arraycopy(buffer.array(), 0, result, 0, result.length);
            Log.d(TAG, "toByteArray: "+bytesToHex(result) );
            //parseFromByteArray(result);
            return result;
        }
        // 将对象转为16进制 byte数组
        public static byte[] toByteArray(MissionData missionData) throws IOException {
            ByteBuffer buffer = ByteBuffer.allocate(1024); // 初始容量设为1024，可根据实际情况调整
            buffer.order(ByteOrder.BIG_ENDIAN);
            // 航线全局速度 (uint8)
            buffer.put( (byte) missionData.Speed);

            // 航线全局高度 (uint16)
            buffer.putShort((short) missionData.Height);

            // 上传动作 (uint8)
            buffer.put((byte) 0X00); // 默认值为0，仅上传航线

            // 航点数量 (uint8)
            buffer.put((byte) missionData.PointData.size());

            // 遍历每个航点并添加到缓冲区
            for (PointData point : missionData.PointData) {
                // 纬度 (int32)

                long lat = (long) (new BigDecimal(point.Latitude+ 90).setScale(7, RoundingMode.HALF_UP).doubleValue()  * 1e7);
                buffer.putInt((int) lat);
                // 经度 (int32)

                long lng = (long) (new BigDecimal(point.Longitude+ 180).setScale(7, RoundingMode.HALF_UP).doubleValue()* 1e7);
                buffer.putInt((int) lng);

                // 相对高度 (int16)
                short height = (short) (new BigDecimal(point.Altitude).setScale(2, RoundingMode.HALF_UP).doubleValue());
                buffer.putShort(height);
                // 航向 (int16)
                short heading = (short) (0XFFFF);
                buffer.putShort(heading);

                // 任务执行时间 (uint8)
                buffer.put((byte) 0X00);
            }

            // 获取实际使用的字节数组
            byte[] result = new byte[buffer.position()];
            System.arraycopy(buffer.array(), 0, result, 0, result.length);
            Log.d(TAG, "toByteArray: "+bytesToHex(result) );
             //parseFromByteArray(result);
            return result;
        }

        // 字节数组转16进制字符串
        private static String bytesToHex(byte[] bytes) {
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02X ", b));
            }
            return sb.toString().trim();
        }

        // 解析字节数组 检验
        public static MissionData parseFromByteArray(byte[] bytes) throws IOException {
            ByteBuffer buffer = ByteBuffer.wrap(bytes);
            buffer.order(ByteOrder.BIG_ENDIAN); // 确保与 toByteArray 保持一致

            MissionData missionData = new MissionData();

            missionData.Speed = buffer.get() & 0xFF;
            missionData.Height = buffer.getShort() & 0xFFFF;
            int uploadAction = buffer.get() & 0xFF;
            int waypointCount = buffer.get() & 0xFF;

            missionData.PointData = new ArrayList<>();
            for (int i = 0; i < waypointCount; i++) {
                PointData point = new PointData();

                int lat = buffer.getInt();
                point.Latitude = Integer.toUnsignedLong(lat) / 1e7 - 90;
                Log.d(TAG, String.format("PointData-Latitude %d: %d: %.6f", i, Integer.toUnsignedLong(lat), point.Latitude));
                int lng = buffer.getInt();
                point.Longitude = Integer.toUnsignedLong( lng) / 1e7 - 180;
                Log.d(TAG, String.format("PointData-Longitude %d: %d: %.6f", i, Integer.toUnsignedLong( lng), point.Longitude));

                short heightRaw = buffer.getShort();
                point.Altitude = Short.toUnsignedInt(heightRaw) & 0xFFFF; // 转换为无符号整数（0~65535）

                short heading = buffer.getShort(); // 示例：可保留原始值
                int executionTime = buffer.get() & 0xFF;

                missionData.PointData.add(point);

                Log.d(TAG, String.format("PointData-height %d: %.1f", i, point.Altitude));
            }

            return missionData;
        }

        // 航点数据类
        public static class PointData {
            public int Id;
            public double Longitude;
            public double Latitude;
            public double Altitude;
        }

        public static class MissionData {
            public int Id;
            public String MissionName;
            public int VehicleId;
            public String MissionDescription;
            public List<PointData> PointData = new ArrayList<>();
            public double Speed;
            public double Height;
            public String CreateTime;
        }

    }
}
