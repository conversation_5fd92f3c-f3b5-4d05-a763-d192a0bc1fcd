﻿using Dm;
using Dm.util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using SqlSugar;
using System.Security.Cryptography;
using System.Text;
using VehicleManagerSystem.Services;
using VehicleManagerSystem.Tables;
using VehicleManagerSystem.Utils;
using static VehicleManagerSystem.Controllers.AccountController;

namespace VehicleManagerSystem.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MissionController(DataBaseService database, IStringLocalizer<LanguageResource> language, MQTTService mqtt) : ControllerBase
    {
        public class Bizcontext
        {
            //VehicleID
            public int? VehicleId { get; set; } = null;
        }

        /// <summary>
        /// 权限验证结果
        /// </summary>
        public class AuthResult
        {
            public bool IsSuccess { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
            public TableUser? User { get; set; }
            public TableVehicle? Vehicle { get; set; }
        }

        /// <summary>
        /// 验证用户身份和飞行器权限
        /// </summary>
        /// <param name="vehicleId">飞行器ID</param>
        /// <param name="requireVehicleAccess">是否需要飞行器访问权限</param>
        /// <returns>权限验证结果</returns>
        private async Task<AuthResult> ValidateUserPermissionAsync(int? vehicleId = null, bool requireVehicleAccess = false)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var result = new AuthResult();

                // 1. 验证用户身份
                if (!Request.Headers.TryGetValue("uid", out var h_uid) ||
                    !Request.Headers.TryGetValue("token", out var h_token))
                {
                    result.ErrorMessage = "用户未登录";
                    return result;
                }

                var n_uid = h_uid.FirstOrDefault();
                var n_token = h_token.FirstOrDefault();

                if (string.IsNullOrWhiteSpace(n_uid) || !int.TryParse(n_uid, out var uid_i))
                {
                    result.ErrorMessage = "用户身份无效";
                    return result;
                }

                if (string.IsNullOrWhiteSpace(n_token) || n_token.Length != 32)
                {
                    result.ErrorMessage = "用户令牌无效";
                    return result;
                }

                // 2. 验证用户存在性和状态
                var user = await sql.Queryable<TableUser>().Where(it => it.Id == uid_i).FirstAsync();
                if (user == null)
                {
                    result.ErrorMessage = "用户不存在";
                    return result;
                }

                if (user.UserStatus != UserStatus.Normal)
                {
                    result.ErrorMessage = "用户已被禁用";
                    return result;
                }

                if (!database.UserEnabled(user.Id))
                {
                    result.ErrorMessage = "用户已被禁用";
                    return result;
                }

                // 3. 验证用户令牌
                var hash = string.Join("", MD5.HashData(Encoding.UTF8.GetBytes(n_token + "@@112235##")).Select(it => it.ToString("x2")));
                var userLogin = await sql.Queryable<TableUserToken>()
                    .Where(it => it.UserId == user.Id)
                    .Where(it => it.UserToken == hash)
                    .FirstAsync();

                if (userLogin == null)
                {
                    result.ErrorMessage = "用户令牌已过期";
                    return result;
                }

                result.User = user;

                // 4. 如果需要验证飞行器权限
                if (requireVehicleAccess && vehicleId.HasValue)
                {
                    var vehicle = await sql.Queryable<TableVehicle>().Where(it => it.Id == vehicleId.Value).FirstAsync();
                    if (vehicle == null)
                    {
                        result.ErrorMessage = "飞行器不存在";
                        return result;
                    }

                    // 检查用户是否有权限访问该飞行器
                    if (vehicle.OwnerUid != user.Id && user.UserRole != UserRole.SuperAdmin)
                    {
                        var vehicle_auth = await sql.Queryable<TableVehicleAuthorize>()
                            .Where(it => it.UserUid == user.Id)
                            .Where(it => it.VehicleId == vehicle.Id)
                            .FirstAsync();

                        if (vehicle_auth == null)
                        {
                            result.ErrorMessage = "无权限访问该飞行器";
                            return result;
                        }
                    }

                    result.Vehicle = vehicle;
                }

                result.IsSuccess = true;
                return result;
            });
        }

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="limit">每页数量</param>
        /// <param name="biz_context">业务上下文</param>
        /// <returns>任务列表</returns>
        [HttpGet("list")]
        public async Task<object> GetMissionList(int page, int limit, [FromQuery] Bizcontext biz_context)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                
                var query = sql.Queryable<TableMission>();
                query = query.Where(it => it.VehicleId == biz_context.VehicleId);
                var count = await query.Clone().CountAsync();
                var list = await query.OrderByDescending(it => it.CreateTime).ToPageListAsync(page, limit);

                return ApiResult.Success(new
                {
                    count = count,
                    list = list,
                    code = 0
                });
            });
        }

        /// <summary>
        /// 添加新任务
        /// </summary>
        /// <param name="mission">任务信息</param>
        /// <returns>添加结果</returns>
        [HttpPost("add")]
        public async Task<object> AddMission([FromBody] TableMission mission)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.MissionName == mission.MissionName && it.VehicleId == mission.VehicleId)
                    .FirstAsync();

                if (existingMission != null)
                {
                    return ApiResult.Failed("任务名称已存在");
                }

                mission.CreateTime = DateTime.Now;

                var result = await sql.Insertable(mission).ExecuteCommandAsync();

                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务添加成功",
                });
            });
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="mission">任务信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("update")]
        public async Task<object> UpdateMission([FromBody] TableMission mission)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.Id == mission.Id)
                    .FirstAsync();

                if (existingMission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }

                var duplicateMission = await sql.Queryable<TableMission>()
                    .Where(it => it.MissionName == mission.MissionName &&
                                it.VehicleId == mission.VehicleId &&
                                it.Id != mission.Id)
                    .FirstAsync();

                if (duplicateMission != null)
                {
                    return ApiResult.Failed("任务名称已存在");
                }

                var updateMission = new TableMission
                {
                    Id = mission.Id,
                    MissionName = mission.MissionName,
                    VehicleId = mission.VehicleId,
                    //MissionDescription = mission.MissionDescription,
                    PointData = mission.PointData, // 这会自动设置PointDataJson
                    Speed = mission.Speed,
                    Height = mission.Height,
                    CreateTime = existingMission.CreateTime // 保持原始创建时间
                };

                var result = await sql.Updateable(updateMission).ExecuteCommandAsync();

                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务更新成功",
                });
            });
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务详情</returns>
        [HttpGet("detail")]
        public async Task<object> GetMissionDetail(int id)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                // 1. 获取任务信息
                var mission = await sql.Queryable<TableMission>().InSingleAsync(id);

                if (mission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }

                return ApiResult.Success(mission);
            });
        }

        /// <summary>
        /// 推送任务到地面站
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="type">推送类型</param>
        /// <returns>推送结果</returns>
        [HttpGet("upload")]
        public async Task<object> UploadMission(int id,int type)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                // 1. 获取任务信息
                var mission = await sql.Queryable<TableMission>().InSingleAsync(id);
                if (mission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }

                // 2. 验证用户权限（需要对该飞行器有访问权限）
                var authResult = await ValidateUserPermissionAsync(mission.VehicleId, true);
                if (!authResult.IsSuccess)
                {
                    return ApiResult.Failed(authResult.ErrorMessage);
                }

                // 3. 验证用户角色权限（只有控制员和管理员可以推送任务）
                if (authResult.User!.UserRole != UserRole.Controller &&
                    authResult.User.UserRole != UserRole.SuperAdmin &&
                    authResult.User.UserRole != UserRole.ThirdPartyAdmin)
                {
                    return ApiResult.Failed("当前用户角色无权限推送任务");
                }

                // 4. 获取飞行器序列号
                //var VehicleSerial = sql.Queryable<TableVehicle>().InSingleAsync(mission.VehicleId).Result.VehicleSerial;
                //string VehicleSerial = authResult.Vehicle!.VehicleSerial;

                
                if (type > 1) 
                    mission.UploadStatus = UploadStatus.None;
                else
                    mission.UploadStatus = (UploadStatus)type;

                mission.PointDataJson = "";
                try
                {
                    // 6. 推送任务到MQTT
                    await mqtt.SendAsync(authResult.Vehicle.VehicleSerial, authResult.Vehicle.VehicleSerial, "mission", "upload", mission);

                    return ApiResult.Success(new
                    {
                        message = "任务推送成功",
                        missionId = id,
                        missionName = mission.MissionName,
                        vehicleName = authResult.Vehicle?.VehicleName,
                        operatorName = authResult.User.UserName
                    });
                }
                catch (Exception ex)
                {
                    return ApiResult.Failed($"任务推送失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("delete")]
        public async Task<object> DeleteMission([FromBody] int id)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.Id == id)
                    .FirstAsync();

                if (existingMission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }

                var result = await sql.Deleteable<TableMission>(n => n.Id == id).ExecuteCommandAsync();
                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务删除成功",
                });
            });
        }

        /// <summary>
        /// 批量删除任务
        /// </summary>
        /// <param name="ids">任务ID列表</param>
        /// <returns>删除结果</returns>
        [HttpDelete("batch-delete")]
        public async Task<object> BatchDeleteMissions([FromBody] List<int> ids)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                if (ids == null || ids.Count == 0)
                {
                    return ApiResult.Failed("请选择要删除的任务");
                }

                var missionsToDelete = await sql.Queryable<TableMission>()
                    .Where(it => ids.Contains(it.Id))
                    .ToListAsync();

                if (!missionsToDelete.Any())
                {
                    return ApiResult.Failed("未找到要删除的任务");
                }


                var result = await sql.Deleteable<TableMission>(n => ids.Contains(n.Id)).ExecuteCommandAsync();
                return ApiResult.Success(new
                {
                    code = result,
                    message = $"成功删除 {result} 个任务",
                });
            });
        }

        /// <summary>
        /// 复制任务
        /// </summary>
        /// <param name="id">原任务ID</param>
        /// <param name="newMissionName">新任务名称</param>
        /// <returns>复制结果</returns>
        [HttpPost("copy")]
        public async Task<object> CopyMission(int id, string newMissionName)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var originalMission = await sql.Queryable<TableMission>().InSingleAsync(id);
                if (originalMission == null)
                {
                    return ApiResult.Failed("原任务不存在");
                }
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.MissionName == newMissionName && it.VehicleId == originalMission.VehicleId)
                    .FirstAsync();

                if (existingMission != null)
                {
                    return ApiResult.Failed("新任务名称已存在");
                }

                var newMission = new TableMission
                {
                    MissionName = newMissionName,
                    VehicleId = originalMission.VehicleId,
                   // MissionDescription = originalMission.MissionDescription,
                    PointData = originalMission.PointData,
                    Speed = originalMission.Speed,
                    Height = originalMission.Height,
                    CreateTime = DateTime.Now
                };

                var result = await sql.Insertable(newMission).ExecuteCommandAsync();
                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务复制成功",
                    originalMissionName = originalMission.MissionName,
                    newMissionName = newMissionName,
                });
            });
        }
    }
}
