using Dm;
using Dm.util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using SqlSugar;
using VehicleManagerSystem.Services;
using VehicleManagerSystem.Tables;
using VehicleManagerSystem.Utils;
using static VehicleManagerSystem.Controllers.AccountController;

namespace VehicleManagerSystem.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MissionController(DataBaseService database, IStringLocalizer<LanguageResource> language, MQTTService mqtt) : ControllerBase
    {
        public class Bizcontext
        {
            //VehicleID
            public int? VehicleId { get; set; } = null;
        }

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="limit">每页数量</param>
        /// <param name="biz_context">业务上下文</param>
        /// <returns>任务列表</returns>
        [HttpGet("list")]
        public async Task<object> GetMissionList(int page, int limit, [FromQuery] Bizcontext biz_context)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var query = sql.Queryable<TableMission>();
                if (biz_context?.VehicleId != null)
                {
                    query = query.Where(it => it.VehicleId == biz_context.VehicleId);
                }

                var count = await query.Clone().CountAsync();
                var list = await query.OrderByDescending(it => it.CreateTime).ToPageListAsync(page, limit);

                return ApiResult.Success(new
                {
                    count = count,
                    list = list,
                    code = 0
                });
            });
        }

        /// <summary>
        /// 添加新任务
        /// </summary>
        /// <param name="mission">任务信息</param>
        /// <returns>添加结果</returns>
        [HttpPost("add")]
        public async Task<object> AddMission([FromBody] TableMission mission)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var vehicle = await sql.Queryable<TableVehicle>().InSingleAsync(mission.VehicleId);
                if (vehicle == null)
                {
                    return ApiResult.Failed("关联的飞机不存在");
                }


                // 验证任务名称是否已存在
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.MissionName == mission.MissionName && it.VehicleId == mission.VehicleId)
                    .FirstAsync();

                if (existingMission != null)
                {
                    return ApiResult.Failed("任务名称已存在");
                }

                mission.CreateTime = DateTime.Now;
                // PointData会自动序列化为PointDataJson
                var result = await sql.Insertable(mission).ExecuteCommandAsync();

                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务添加成功"
                });
            });
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="mission">任务信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("update")]
        public async Task<object> UpdateMission([FromBody] TableMission mission)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var vehicle = await sql.Queryable<TableVehicle>().InSingleAsync(mission.VehicleId);
                if (vehicle == null)
                {
                    return ApiResult.Failed("关联的飞机不存在");
                }

                // 验证任务是否存在
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.Id == mission.Id)
                    .FirstAsync();

                if (existingMission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }

                // 验证任务名称是否与其他任务重复
                var duplicateMission = await sql.Queryable<TableMission>()
                    .Where(it => it.MissionName == mission.MissionName &&
                                it.VehicleId == mission.VehicleId &&
                                it.Id != mission.Id)
                    .FirstAsync();

                if (duplicateMission != null)
                {
                    return ApiResult.Failed("任务名称已存在");
                }

                // 确保更新时包含所有必要字段，并正确处理PointData
                var updateMission = new TableMission
                {
                    Id = mission.Id,
                    MissionName = mission.MissionName,
                    VehicleId = mission.VehicleId,
                    MissionDescription = mission.MissionDescription,
                    PointData = mission.PointData, // 这会自动设置PointDataJson
                    Speed = mission.Speed,
                    Height = mission.Height,
                    CreateTime = existingMission.CreateTime // 保持原始创建时间
                };

                var result = await sql.Updateable(updateMission).ExecuteCommandAsync();

                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务更新成功"
                });
            });
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务详情</returns>
        [HttpGet("detail")]
        public async Task<object> GetMissionDetail(int id)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var mission = await sql.Queryable<TableMission>().InSingleAsync(id);
                if (mission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }
                return ApiResult.Success(mission);
            });
        }

        /// <summary>
        /// 推送任务到地面站
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务详情</returns>
        [HttpGet("upload")]
        public async Task<object> UploadMission(int id)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var mission = await sql.Queryable<TableMission>().InSingleAsync(id);
                if (mission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }
                mission.PointDataJson = "";
                string VehicleSerial = sql.Queryable<TableVehicle>().InSingleAsync(mission.VehicleId).Result.VehicleSerial;
                

                await mqtt.SendAsync(VehicleSerial, VehicleSerial, "mission", "upload", mission);
                return ApiResult.Success();
            });
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("delete")]
        public async Task<object> DeleteMission([FromBody] int id)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                // 验证任务是否存在
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.Id == id)
                    .FirstAsync();

                if (existingMission == null)
                {
                    return ApiResult.Failed("任务不存在");
                }

                var result = await sql.Deleteable<TableMission>(n => n.Id == id).ExecuteCommandAsync();
                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务删除成功"
                });
            });
        }

        /// <summary>
        /// 批量删除任务
        /// </summary>
        /// <param name="ids">任务ID列表</param>
        /// <returns>删除结果</returns>
        [HttpDelete("batch-delete")]
        public async Task<object> BatchDeleteMissions([FromBody] List<int> ids)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                if (ids == null || ids.Count == 0)
                {
                    return ApiResult.Failed("请选择要删除的任务");
                }

                var result = await sql.Deleteable<TableMission>(n => ids.Contains(n.Id)).ExecuteCommandAsync();
                return ApiResult.Success(new
                {
                    code = result,
                    message = $"成功删除 {result} 个任务"
                });
            });
        }

        /// <summary>
        /// 复制任务
        /// </summary>
        /// <param name="id">原任务ID</param>
        /// <param name="newMissionName">新任务名称</param>
        /// <returns>复制结果</returns>
        [HttpPost("copy")]
        public async Task<object> CopyMission(int id, string newMissionName)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var originalMission = await sql.Queryable<TableMission>().InSingleAsync(id);
                if (originalMission == null)
                {
                    return ApiResult.Failed("原任务不存在");
                }

                // 验证新任务名称是否已存在
                var existingMission = await sql.Queryable<TableMission>()
                    .Where(it => it.MissionName == newMissionName && it.VehicleId == originalMission.VehicleId)
                    .FirstAsync();

                if (existingMission != null)
                {
                    return ApiResult.Failed("新任务名称已存在");
                }

                var newMission = new TableMission
                {
                    MissionName = newMissionName,
                    VehicleId = originalMission.VehicleId,
                    MissionDescription = originalMission.MissionDescription,
                    PointData = originalMission.PointData,
                    Speed = originalMission.Speed,
                    CreateTime = DateTime.Now
                };

                var result = await sql.Insertable(newMission).ExecuteCommandAsync();
                return ApiResult.Success(new
                {
                    code = result,
                    message = "任务复制成功"
                });
            });
        }
    }
}
