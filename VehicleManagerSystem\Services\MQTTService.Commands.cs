﻿using System.Reflection;
using MQTTnet.Extensions.TopicTemplate;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Services
{
    public partial class MQTTService
    {
        #region 定义
        public interface ICommandExecuter
        {
            public void ExecuteAsync(string json,string clientId, Func<string, Task> result);
            public void ExecuteAsync(string json,string clientId);
            public MqttTopicTemplate WithParameter(string key, string val);
        }
        public class CommandExecuter<T> : ICommandExecuter
        {
            private MqttTopicTemplate m_template;
            private Func<T?,string, Task<string>> m_action;
            public MqttTopicTemplate WithParameter(string key, string val)
            {
                return m_template.WithParameter(key, val);
            }
            public CommandExecuter(string template, Func<T?,string, Task<string>> mqtt_action)
            {
                m_template = new MqttTopicTemplate(template);
                m_action = mqtt_action;
            }
            public async void ExecuteAsync(string json,string clientId)
            {
                try
                {
                    T? param;
                    if (json is T p)
                    {
                        param = p;
                    }
                    else
                    {
                        param = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json);
                    }
                    await m_action.Invoke(param, clientId);
                }catch(Exception ex)
                {
                    Console.WriteLine(ex.Message+"-"+json);
                }
            }
            public async void ExecuteAsync(string json,string clientId, Func<string, Task> result)
            {
                T? param;
                if (json is T p)
                {
                    param = p;
                }
                else
                {
                    param = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json);
                }
                var result_t = await m_action.Invoke(param,  clientId);
                if (result_t != null)
                {
                    await result.Invoke(result_t);
                }
            }
        }
        public interface IRemoteCommand
        {
        }
        #endregion

        public class ResultCommand : IRemoteCommand
        {
            public string action { get; set; } = null!;
            public bool success { get; set; }
            public int code { get; set; }
            public object? data { get; set; }
        }
        public class HeartbeatCommand : IRemoteCommand
        {
            public float lng { get; set; }
            public float lat { get; set; }
            public float alt { get; set; }
            public float relative_alt { get; set; }
            public float roll { get; set; }
            public float pitch { get; set; }
            public float yaw { get; set; }
            public bool armed { get; set; }
            public int mode { get; set; }
            public int mission_state { get; set; }
            public int mission_index { get; set; }
            public int battery { get; set; }

            public int rssi { get; set; }
            public string model { get; set; }
            public string maker { get; set; }
            public VehicleType type { get; set; }
            public double home_lat { get; set; }
            public double home_lng { get; set; }
            public double home_alt { get; set; }
            public bool ready { get; set; }
            public long mission_version { get; set; }
           public long time { get; set; }
        }

    }
}
