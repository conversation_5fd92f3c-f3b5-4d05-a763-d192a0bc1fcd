﻿using System.Reflection;
using Microsoft.AspNetCore.SignalR;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Services
{
    public partial class MQTTService
    {
        public async Task<string> OnVehicleResult(ResultCommand command,string clientId)
        {
             _= realTimeService.Clients.Group(clientId).SendAsync("result",Newtonsoft.Json.JsonConvert.SerializeObject( command),clientId,CancellationToken.None); 
            return await JsonAsync(new
            {
                date = DateTimeOffset.Now.ToUnixTimeSeconds()
            });
        }
        public async Task<string> OnHeartbeat(HeartbeatCommand command,string clientId)
        {
            if (command != null)
            {

                await database.SqlExecuteAsync(async sql =>
                {
                    return  await  sql.Updateable<TableVehicle>().SetColumns(it => new TableVehicle()
                    {
                        Latitude = command.lat,
                        Longitude = command.lng,
                        Battery = command.battery,
                        Altitude = command.alt,
                        Rssi = 0, 
                        VehicleStatus = VehicleStatus.Online,
                        VehicleType = command.type
                    })
                    .SetColumnsIF(command.model!="UNKNOW",it=>new TableVehicle()
                    {
                         VehicleModel = command.model,
                    }).SetColumnsIF(command.maker!="UNKNOW",it=>new TableVehicle()
                    {
                         VehicleManufacturer = command.maker,
                    })
                    .Where(it=>it.VehicleSerial==clientId).ExecuteCommandAsync();
                });
                _= realTimeService.Clients.Group(clientId).SendAsync("heartbeat",command,clientId,CancellationToken.None); 
            }

            return Json(new
            {

                date = DateTimeOffset.Now.ToUnixTimeSeconds()
            });
        }
    }
}
