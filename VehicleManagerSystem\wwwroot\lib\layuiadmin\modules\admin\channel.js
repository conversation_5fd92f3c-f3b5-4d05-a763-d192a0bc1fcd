// noinspection JSCheckFunctionSignatures


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion:'/modules/expansion',
}).define(['jquery','form', 'table','expansion'], function (exports) {
    const table_name = "channel-table";
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        $ = layui.jquery,
        module = {
            build: function () {
                let columns = [[]];
                columns[0] = [];
                columns[0].push({field: "ChannelCode", title: "通道编码"});
                columns[0].push({field: "ChannelDescription", title: "通道简介"});
                columns[0].push({field: "Status", title: "状态", templet:"#channel-status-tpl"});
                columns[0].push({field: "Running", title: "在线状态", templet:"#channel-running-tpl"});
                columns[0].push({field: "NeedApplication", title: "需要应用", templet:"#channel-need-application-tpl"});
                columns[0].push({field: "NeedAccount", title: "需要小号", templet:"#channel-need-account-tpl"});
                columns[0].push({field: "NeedInventory", title: "需要库存", templet:"#channel-need-inventory-tpl"});
                columns[0].push({field: "PaymentTimeout", title: "支付超时"});
                columns[0].push({field: "QueryTimeout", title: "查询超时"});
                columns[0].push({field: "QueryInterval", title: "查询间隔"});
                columns[0].push({field: "SupportAmounts", title: "支持金额", templet:"#channel-support-amount-tpl"});
                columns[0].push({ field: "SupportPaymentTypes", title: "支付类型", templet:"#channel-payment-type-tpl"});
                columns[0].push({field: "YesterdayAmount", title: "昨日流水", templet:"#channel-yesterday-amount-tpl"});
                columns[0].push({field: "TodayAmount", title: "今日流水", templet:"#channel-today-amount-tpl"});
                columns[0].push({field: "TotalAmount", title: "总流水", templet:"#channel-total-amount-tpl"}); 
                return columns;
            },
            render: function () {
                table.render({
                    elem:"#"+table_name,
                    url: "/api/system/list/channel",
                    cellMinWidth: 80,
                    height: "100%",
                    page: false,
                    defaultToolbar: ['filter'],
                    toolbar: "#toolbar",
                    limit: 30,
                    cols:module.build(),
                    parseData: function (res) {
                        return expansion.parse(res);
                    }, done: function () {
                        expansion.render();
                    }
                }); 
                let commands={
                    status:function (channel_id,status,obj){
                        $.ajax({
                            url: "/api/system/channel/update/status?channel_id="+channel_id,
                            type: "get",
                            data:{
                                status:status
                            },
                            loading: true,
                            dataType: "json",
                            success: function (res) {
                                if(!res.success){
                                    obj.elem.checked = !obj.elem.checked;
                                    form.render();
                                }
                            },error:function (){
                                obj.elem.checked = !obj.elem.checked;
                                form.render();
                            }
                        })
                    } 
                };
              
                form.on('switch(update-status)', function (obj) {
                    console.log(obj, this);
                    commands.status(this.value,obj.elem.checked,obj);
                }); 
                table.on('tool('+table_name+')',function (obj){
                    event_handler(obj.event,obj);
                });
            },
            submit: function (where) {
                table.reload(table_name, {
                    where: {
                        biz_content: JSON.stringify(where)
                    }
                });
            }
        };

    form.on("submit(search)", function (data) {
        let search={};

        for (let s in data.field) {
            if($(".grid-toolbar-item").hasClass("switch-status") ||s==="keyword" ) {
                let t = String(data.field[s]);
                if (t !== "null" && t !== "undefined" && t !== "") {
                    search[s] = t;
                }
            }
        }

        module.submit(search);
        return false;
    });
    $(document).on("click",'.grid-toolbar-item.show-more',function (){
        $(".grid-toolbar-item").toggleClass("switch-status");
    });
    exports('channel', module);
});
 