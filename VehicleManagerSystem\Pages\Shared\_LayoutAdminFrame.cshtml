﻿@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@{
    var language_list = @language.GetAllStrings(true);
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
     <link href="~/lib/layuiadmin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/css/ui.css" rel="stylesheet" asp-append-version="true" /> 
    <link href="~/font/weilai/weilai.css" rel="stylesheet" />
    <link href="~/font/iconfont.css" rel="stylesheet" />
    <link href="~/css/vui.css" rel="stylesheet" />
   
    <link rel="stylesheet" href="~/VehicleManagerSystem.styles.css" asp-append-version="true" />
      <style>
        body,html{
            padding:0;
            margin:0;
            height:100vh;
            width:100vw;
            min-width:960px;
            display:flex;
            flex-direction:column;
            overflow:hidden;
            font-family: 'weilai';
        }  
        .toolbar{
            height:38px;
            padding:0px 28px;
            display:flex;
            overflow:hidden;
            justify-content:end;
            margin-top:2px;
        }
        .body{
           
            box-sizing:border-box;
            flex:1;
            margin-top:-2px;
        }
        .floor{
            height:12px;
        }

        .layui-table td:first-child, .layui-table th:first-child {
            text-indent: 22px;
            box-sizing:border-box;
        }
    </style>
    @await RenderSectionAsync("Styles", required: false)
</head>
<body style="background: rgba(2,64,101,0.9);">
    <div class="toolbar">
          @await RenderSectionAsync("Toolbar", required: false)
    </div>
    <div class="body">
    @RenderBody()
    </div>
    <div class="floor">

    </div>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/vdos/vdos.js"></script>
    <script src="~/lib/layuiadmin/layui/layui.js"></script>
    <script src="~/js/vui.js"></script>
    <script>
        window. constants={ };
        @foreach (var item in language_list)
        {
            @Html.Raw($"window.constants.{item.Name}=\"{item.Value}\";\n")
        }
	</script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html> 