@page "/Control/Console/{id}/{uid?}/{nonce?}/{token?}"
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Console.ConsoleModel
@{
	Layout = "_LayoutControl";
}
@if (Model.HasErrors)
{
	@section Scripts {
	<script>
		ui.alert("@language[Model.ErrorMessage!]",true);
	</script>
	}
}
else
{
	@section Styles {
	<link href="~/lib/cesium/Widgets/widgets.css" rel="stylesheet" />
	<style>
		.cesium-widget-credits {
			display: none !important;
			visibility: hidden !important;
		}

	</style>
	}
	<div class="root-view" style="display:flex;justify-content:center;align-items:center;">
		<div id="map-view" style="transform-origin:50%;">
		</div>
		<div class="box-view ">
			<div class="title-box">
				@Model.VehicleName
			</div>
			<div class="video-box">
				<div class="video-content">
					<div class="video-top-bar">
						<span class="camera-btn">变焦</span>
						<span class="camera-btn">广角</span>
						<span class="camera-btn">红外</span>
					</div>
					<div class="video-center">
						<div class="video-left-bar">
							<span class="camera-btn">放大</span>
							<span class="camera-btn">缩小</span>
							<span class="camera-btn">录像</span>
							<span class="camera-btn">拍照</span>
							<span class="camera-btn hidden-video" attr-normal="隐藏" attr-hidden="显示"></span>
						</div>
						<div class="player">
							<div class="player-ui">
								<div class="direction">
									<span></span>
									<span class="action-forward iconfont icon-sanjiao2"></span>
									<span></span>
									<span   class="action-go-left  iconfont icon-sanjiao4"></span>
									<span>
									</span>
									<span    class="action-go-right  iconfont icon-sanjiao3"></span>
									<span></span>
									<span   class="action-retreat  iconfont icon-sanjiao1"></span>
									<span></span>
								</div>
							</div>
						</div>
					</div>
					<div class="video-bottom">
						<span class="camera-btn">向下</span>
						<span class="camera-btn">回中</span>
					</div>
				</div>
			</div>
		</div>

		<div class="vehicle-hud">
			<div>
				自动
			</div>
		</div>
		<div class="vehicle-hud-text">
			<div class="row-fill"></div>
			<div class="row-end">
				<div id="hub-lng">
					经度:104.6677889
				</div>
				<div id="hub-alt" class="center">
					海拔:4500米 场高:17米
				</div>
				<div id="hub-lat">
					纬度:61.6677889
				</div>
			</div>
		</div>
		<div class="vehicle-control">
			<div class="status">
				<div id="armed">
					飞行中
				</div>
				<div id="flight-mode">
					任务模式
				</div>
				<span></span>
				<span id="mission-state" class="iconfont icon-stop"></span>
				<div style="flex:1;">
				</div>
				<span class="val-signal iconfont icon-focus-3-line locked">

				</span>
				<div class="val-battery" id="battery">
				</div>
				<div class="val-signal" id="rssi">
				</div>
			</div>

			<div class="commands">

				<div class="command-left">
					<div class="charts">

						<div class="mission-group">
							<span>
								--
							</span>
							<span>
								--
							</span>
						</div>
						<div style="flex:1"></div>
						<div class="time-group">
							<span>
								任务时间:00:00:00
							</span>
							<span>
								飞行时间:00:00:00
							</span>
							<span>
								剩余时间:00:00:00
							</span>
						</div>
					</div>
					<div class="modes">
						<div class="takeoff" id="cmd-takeoff">
							一键起飞
						</div>
						<div class="land" id="cmd-land">
							一键着陆
						</div>
						<div class="rtl" id="cmd-rtl">
							一键返航
						</div>
						<div class="mission" id="cmd-mission">
							一键任务
						</div>
						<div class="missionplan" id="cmd-missionplan">
							任务规划
						</div>
					</div>
				</div>
				<div class="command-right">
					<span></span>
					<span id="v-go-up" class="action-forward iconfont icon-sanjiao2"></span>
					<span></span>
					<span id="v-go-left" class="action-go-left  iconfont icon-sanjiao4"></span>
					<span>
						<input id="v-go-value" type="number" style="width:100%;" value="0.5" />
					</span>
					<span id="v-go-right" class="action-go-right  iconfont icon-sanjiao3"></span>
					<span></span>
					<span id="v-go-down" class="action-retreat  iconfont icon-sanjiao1"></span>
					<span></span>

				</div>

			</div>
			<div class="val-throttle">
			</div>
		</div>


	</div>
	@section Scripts {
	<script src="~/lib/cesium/Cesium.js"></script>
	<script src="~/js/signalr.min.js"></script>
	<script>var query="@Html.Raw(Model.realtime_query)";</script>
	<script type="text/javascript">

		window.CESIUM_BASE_URL = '/lib/cesium/';

		layui.use(['layer', 'form', 'jquery'], function () {

			var layer = layui.layer,form = layui.form; 
			vui.use(["panel","aim"], function () {

				var layer={
					is_rotate:false,
					viewer:{},
					previous_rotate_time:0,
					earth_route:function (e) {
						if (!e.is_rotate) return;
						let spinRate = 50;
						let currentTime = e.viewer.clock.currentTime.secondsOfDay;
						let delta = (currentTime - e.previous_rotate_time) / 1000;
						e.previous_rotate_time = currentTime;
						e.viewer.scene.camera.rotate(Cesium.Cartesian3.UNIT_Z, -spinRate * delta);
					},
					zoomLevel:function (e) {
						let tiles = new Set();
						let tilesToRender = e.viewer.scene.globe._surface._tilesToRender;

						if (Cesium.defined(tilesToRender)) {

							let max = 0;

							if (tilesToRender.length == 0){

								return 9;

							}
							for (let i = 0; i < tilesToRender.length; i++) {

								if (tilesToRender[i].level > max) max = tilesToRender[i].level;

							}
							return max;
						}
						return 9;
					},show_ui:function(e){

					},hide_ui:function(e){

					},
					entity:{},
					loaded:false,
					render:function(e){
						Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1ODJhMjA5Yy02OTU2LTQ4ZmUtYmU2Yi1hMDRlMDNlZjljZmEiLCJpZCI6ODk4NjksImlhdCI6MTY2MjY5NTEwOX0.Z7GZMmZ9sXH740jtl059KaViFXC1Nn0GIjYWyWLKTrg';
						const viewer = new Cesium.Viewer('map-view', {
							terrain: Cesium.Terrain.fromWorldTerrain(),
							shouldAnimate: true,
							//sceneMode: Cesium.SceneMode.SCENE2D,
							animation: false,
							baseLayerPicker: false,
							geocoder: false,
							homeButton: false,
							resolutionScale: 2,
							infoBox: false,
							sceneModePicker: false,
							scene3DOnly: false,
							selectionIndicator: false,
							navigationHelpButton: false,
							fullscreenButton: false,
							timeline: false,
							shadows: true,
							orderIndependentTranslucenucy: true,
							contextOptions: {
								webgl: {
									alpha: true
								}
							}
						});
						e.viewer = viewer;
						viewer.scene.globe.depthTestAgainstTerrain = true;
						viewer.clock.multiplier = 1;

						viewer.clock.onTick.addEventListener(() => {
							var camera_cartographic = Cesium.Cartographic.fromCartesian(e.viewer.camera.position)
							var camera_height = camera_cartographic.height;
							if (camera_height > 10000000 && Math.abs(Cesium.Math.toDegrees(e.viewer.camera.pitch) + 90) < 3) {
								if (!e.is_rotate) {
									e.previous_rotate_time = viewer.clock.currentTime.secondsOfDay;
									e.is_rotate = true;
									vui.hidePanelGroup($(".console-ui-top"));
									vui.hidePanelGroup($(".console-ui-left"));
								}
							} else {
								if (e.is_rotate) {
									e.is_rotate = false;
									vui.showPanelGroup($(".console-ui-top"));
									vui.showPanelGroup($(".console-ui-left"));
								}
							}
							if (e.is_rotate) {
								e.earth_route(e);
							}
						});

						var handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
						handler.setInputAction(function (movement) {
							var cartesian = viewer.scene.pickPosition(movement.position);
							if (Cesium.defined(cartesian)) {

								var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
								var longitudeString = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
								var latitudeString = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
								var heightString = cartographic.height.toFixed(2); // 高度保持两位小数

								//window.logger_pos.push({ lon: longitudeString, lat: latitudeString, alt: Number(heightString) +200})

							} else {
								console.log('No intersection with the globe');
							}
						}, Cesium.ScreenSpaceEventType.LEFT_CLICK);

						e.loaded=true;
					},
					add_vehicle:function(e,vehicle,id){
						vehicle.entity = e.viewer.entities.add({
							id: id,
							position: vehicle.positions,
							orientation: vehicle.orientations,
							model: {
								uri: "/lib/model/vehicle.glb",
								minimumPixelSize: 120,
								maximumScale: 120,
								show: true,
								runAnimations: true,
								clampAnimations: false,
								shadows: Cesium.ShadowMode.ENABLED
							},
						});
						vehicle.mission= e.viewer.entities.add({
							name: "v_m_" + id,
							polyline: {
								positions: new Cesium.CallbackProperty(new (function(vehicle){this.callback=function(){return vehicle.missions();}})(vehicle).callback, false),
								clampToGround: false,
								width: 4,
								loop: false,
								material: Cesium.Color.fromCssColorString("#f20")
							},
						});
						e.viewer.trackedEntity = vehicle.entity;
					},
					locked_complete : null,
					flyto : function (e,idx, cbk) {
						if (e.locked_complete != null) return;
						 var entity = e.vehicles[idx].entity;
						 var currentHeading = e.viewer.camera.heading;
						 var currentPitch = e.viewer.camera.pitch;
						 var offset = new Cesium.HeadingPitchRange(
							currentHeading,
							currentPitch,
							400.0
						 );
						 e.viewer.trackedEntity = null;
						 lockedEntity = entity;
						 e.viewer.flyTo(entity,{
							 duration: 1,
							 offset: offset,
						 });
						setTimeout(new (function (e, entity) {
							this.handler = function () {
								e.viewer.trackedEntity = entity;
								cbk();

							}
						})(e, entity).handler, 1100);
					},
					vehicles:[]


				};
				layer.render(layer);

				var data_connection = function (onmessage, onconnected) {
					const connection = new signalR.HubConnectionBuilder()
						.withUrl("/realtime/vehicle"+query )
						.withAutomaticReconnect({ nextRetryDelayInMilliseconds: (retryCount) => { return 3000; }, maximumRetryCount: 1000 })
						.build();
						for (var name in onmessage) {
							if (typeof (onmessage[name]) == 'function') {
								connection.on(name, onmessage[name]);
							}
						}

						connection.start().then(function () {
							if (onconnected)onconnected();
						}).catch(err => console.error("connect", err));

					return {
						invoke: function (data,vehicle_id, error) {
							connection.invoke("Message",JSON.stringify( data),vehicle_id).catch(error);
						}
					}
				};

				var vehicle_panel = vui.Vehicle;
				var home_position={
					lat:NaN,
					lng:NaN,
					alt:NaN
				};
				var home_position_raw={
					lat:0,
					lng:0,
					alt:0
				};
				var home_valid=false;
				var server=  data_connection({
					result:function(data,vehicle_id){
						if(typeof(data) =="string"){
                            console.log("result",data);
								data=JSON.parse(data);
								if(data.method="mission"){
								if(data.action=="request_list"){
									 if(data.data!=null){
										 if(window.current_vehicle!=null){
											window.current_vehicle.mission_positions=[];
											console.log("clear points");
											for(var n=0;n<window.current_vehicle.mission_points.length;n++){
												layer.viewer.entities.remove(window.current_vehicle.mission_points[n]);
											}
											window.current_vehicle.mission_points=[];
											var home_alt=home_position.alt;
											var index=0;
											for(var n=0;n<data.data.length;n++){
												var item=data.data[n];
												if(item.command==16 && item.frame==3){
													var p = layer.viewer.entities.add({
														description: `航点:[${++index}]`,
														position: Cesium.Cartesian3.fromDegrees(item.y * 1e-7, item.x * 1e-7, item.z + home_alt),
														point: { pixelSize: 20, color: Cesium.Color.BLUE }
													});
													window.current_vehicle.mission_points.push(p);
													window.current_vehicle.mission_positions.push(Cesium.Cartesian3.fromDegrees(item.y * 1e-7, item.x * 1e-7, item.z +home_alt));
												}
											}
										 }
									 }
								}
							}
						}
						//{"type":1,"target":"result","arguments":["{\"action\":\"request_list\",\"success\":false,\"code\":0,\"data\":
						// [{\"x\":314615364,\"y\":1046175728,\"z\":640.0700073242188,\"command\":16,\"frame\":0},
						// {\"x\":314616206,\"y\":1046175268,\"z\":15,\"command\":22,\"frame\":3},
						// {\"x\":0,\"y\":0,\"z\":0,\"command\":178,\"frame\":0},
						// {\"x\":314616206,\"y\":1046175268,\"z\":15,\"command\":16,\"frame\":3},
						// {\"x\":314615375,\"y\":1046178332,\"z\":15,\"command\":16,\"frame\":3},
						// {\"x\":314612300,\"y\":1046174366,\"z\":15,\"command\":16,\"frame\":3},
						// {\"x\":314614453,\"y\":1046171915,\"z\":15,\"command\":16,\"frame\":3},
						// {\"x\":0,\"y\":0,\"z\":0,\"command\":20,\"frame\":0}]}","VDB00ZY2500TW50000AF9000001"]}
						
							 //    public string action { get; set; } = null!;
								// public bool success { get; set; }
								// public int code { get; set; }
								// public object? data { get; set; }
						//{"type":1,"target":"result","arguments":["{\"action\":\"request_list\",\"success\":false,\"code\":0,\"data\":[{\"x\":314615367,\"y\":1046175717,\"z\":640.0700073242188,\"command\":16,\"frame\":0},{\"x\":314614979,\"y\":1046170209,\"z\":20,\"command\":22,\"frame\":3},{\"x\":0,\"y\":0,\"z\":0,\"command\":178,\"frame\":0},{\"x\":314614979,\"y\":1046170209,\"z\":20,\"command\":16,\"frame\":3},{\"x\":314612640,\"y\":1046170280,\"z\":20,\"command\":16,\"frame\":3},{\"x\":314611919,\"y\":1046171670,\"z\":20,\"command\":16,\"frame\":3},{\"x\":314613109,\"y\":1046173020,\"z\":20,\"command\":16,\"frame\":3},{\"x\":314614959,\"y\":1046172629,\"z\":20,\"command\":16,\"frame\":3},{\"x\":314615949,\"y\":1046171489,\"z\":20,\"command\":16,\"frame\":3},{\"x\":0,\"y\":0,\"z\":0,\"command\":20,\"frame\":0}]}","VDBZY012500TW50000AFB03000001"]}
					},
					heartbeat:function(data,vehicle_id){
							var vehicle_t={};
							if(layer.loaded){
							if((home_position_raw.alt!=data.home_alt ||
							   home_position_raw.lat!=data.home_lat ||
							   home_position_raw.lng!=data.home_lng) &&
								!isNaN(data.home_lng) &&!isNaN(data.home_lat)&&!isNaN(data.home_alt) &&
								data.home_lng!=0 && data.home_lat!=0 && data.home_alt!=0
							){

									home_position_raw.alt=data.home_alt
									home_position_raw.lat=data.home_lat;
									home_position_raw.lng=data.home_lng;
									const cartographic = Cesium.Cartographic.fromDegrees(home_position_raw.lng,home_position_raw.lat);
									 Cesium.sampleTerrain(layer.viewer.terrainProvider, 11, [cartographic]).then(function (positions) {
											console.log(positions);
											if (positions.length > 0) {
												home_position.lat=Cesium.Math.toDegrees( positions[0].latitude);
												home_position.lng=Cesium.Math.toDegrees(positions[0].longitude);
												home_position.alt=positions[0].height;
												console.log("read home succ");
												home_valid=true;
												
											}else{
												console.log("read home fail");
												home_position_raw.alt=NaN;
											}
									}).catch(function (error) {
											console.log("read home err");
										home_position_raw.alt=NaN;
									});

							   }
						   }
						   if(!home_valid)return;
						 
						var cur_time = Cesium.JulianDate.fromDate(new Date(data.time));
						if(!layer.vehicles.hasOwnProperty("v_"+vehicle_id)){
							var positionProperty = new Cesium.SampledPositionProperty();
							 positionProperty.forwardExtrapolationType = Cesium.ExtrapolationType.NONE;
							 positionProperty.forwardExtrapolationDuration=0.5;
							 vehicle_t= layer.vehicles["v_"+vehicle_id]={
									positions   : positionProperty,
									orientations: new Cesium.SampledProperty(Cesium.Quaternion),
									position_array: [],
									vehicle_id:vehicle_id,
									ready:false,
									mission_version:0,
									home_valid:false,
									mission_positions:[],
									mission_points:[],
									missions:function(){
										return this.mission_positions
									},
									mission:null
							 };
							 layer.add_vehicle(layer,vehicle_t,vehicle_id);
							 layer.viewer.clock.currentTime=cur_time;
							 (new vehicle_panel(t)).appendTo($(".console-ui-left>.content")).click(function () {
								var _aim = (new aim()).appendTo($(".console-ui-aim"));
								map.flyto(this.root.attr("vid") ,function () {
									setTimeout(function () {
										_aim.remove();
									}, 500);
								});
							});
						}else{
							vehicle_t=layer.vehicles["v_"+vehicle_id];
						}
					
						data.alt_raw=data.alt;
						var t_home={
							alt:home_position.alt,
							lat:home_position.lat,
							lng:home_position.lng
						};
						if(!isNaN(t_home.alt) && !isNaN(t_home.lat) &&!isNaN( t_home.lng)){
							data.alt=(data.relative_alt+t_home.alt);
						}
						window.current_vehicle=vehicle_t;
						let position = Cesium.Cartesian3.fromDegrees(data.lng, data.lat, data.alt);
						let time = Cesium.JulianDate.addSeconds(cur_time,1, new Cesium.JulianDate());
						let time_last = Cesium.JulianDate.addSeconds(cur_time,3600, new Cesium.JulianDate());
						vehicle_t.positions.addSample(time, position);
						vehicle_t.positions._property._times.pop();
						vehicle_t.positions._property._values.pop();
						vehicle_t.positions.addSample(time_last, position);
						var zt= Cesium.Transforms.headingPitchRollQuaternion(position, new Cesium.HeadingPitchRoll(data.yaw + Cesium.Math.toRadians(-90), data.pitch, data.roll))
						vehicle_t.orientations.addSample(time, zt);
						vehicle_t.orientations._times.pop();
						vehicle_t.orientations._values.pop();
						vehicle_t.orientations.addSample(time_last, zt);
						vehicle_t.ready=data.ready;
						$("#hub-lng").text("经度:"+data.lng.toFixed(7));
						$("#hub-lat").text("纬度:"+data.lat.toFixed(7));
						$("#hub-alt").text("海拔:"+data.alt_raw.toFixed(2)+"米 场高:"+data.relative_alt.toFixed(2)+"米");
						$("#armed").text(data.armed?"飞行中":"已锁定");
						$("#flight-mode").text(data.mode==0?"未起飞":
							data.mode==1?"手动飞行":
							data.mode==2?"任务飞行":
							data.mode==3?"自动着陆":
							data.mode==4?"自动返航":
							data.mode==5?"自定义飞行":"未知状态"
						);

						if(data.mission_state==0){
							$("#mission-state").hide();
						}else if(data.mission_state==1){
							$("#mission-state").removeClass("icon-playfill").addClass("icon-stop");
							$("#mission-state").show();
						}else if(data.mission_state==2){
							$("#mission-state").removeClass("icon-stop").addClass("icon-playfill");
							$("#mission-state").show();
						}else if(data.mission_state==3){
							$("#mission-state").hide();
						}
						if(data.ready){
							if(vehicle_t.mission_version!=data.mission_version && home_valid){
								server.invoke({
									method:"mission",
									action:"request_list"
								},current_vehicle.vehicle_id);
								vehicle_t.mission_version=data.mission_version;
							}
						}


						//{"type":1,"target":"heartbeat","arguments":[{"lng":104.61755,"lat":31.46112,"alt":654.45,"relative_alt":0,"roll":0.0069907433,"pitch":-0.5225778,"yaw":-1.3258141,"armed":true,"mode":2,"mission_state":1,"mission_index":5,"battery":74,"rssi":16,"model":"TW50","maker":"五视天下","time":1748936011722},"VDB00ZY2500TW50000AF9000001"]}

					}

				});

				$("#mission-state").on("click",function(){
						if($("#mission-state").hasClass("icon-playfill")){
							if(!current_vehicle.ready)return;
							server.invoke({
								method:"flight",
								action:"resume"
							},current_vehicle.vehicle_id);
							//开始任务
						}else if($("#mission-state").hasClass("icon-stop")){
							if(!current_vehicle.ready)return;
							//暂停任务
							server.invoke({
								method:"flight",
								action:"pause"
							},current_vehicle.vehicle_id);
						}
					});
				$("#cmd-takeoff").on("click",function(){
					if(!current_vehicle.ready)return;
						server.invoke({
								method:"flight",
								action:"takeoff",
								data:{
									alt:20
								}
						},current_vehicle.vehicle_id);
					});

				$("#cmd-land").on("click",function(){
					if(!current_vehicle.ready)return;
						server.invoke({
								method:"flight",
								action:"land"
						},current_vehicle.vehicle_id);
					});
				$("#cmd-rtl").on("click",function(){
					if(!current_vehicle.ready)return;
						server.invoke({
								method:"flight",
								action:"rtl"
						},current_vehicle.vehicle_id);
					});
				$("#cmd-mission").on("click",function(){
					if(!current_vehicle.ready)return;
					server.invoke({
							method:"flight",
							action:"auto"
					},current_vehicle.vehicle_id);
				});
				$("#cmd-missionplan").on("click",function(){
					layui.use("layer",function(){
						var layer = layui.layer;
						layer.open({
							type: 2,
							title: false,
							shadeClose: true,
							shade: 1,
							area: 'auto',
							tipsMore: false,
							content: "./bridge?target=/Control/Mission/@Model.VehicleId.ToString()",
							success: function(layero, index){
								layer.full(index); // 最大化
								$(window).on('resize', function () {
									layer.full(index);
								});
							},
							end: function() {
								// 关闭时解绑事件
								$(window).off('resize');
							}
						});
					});
				});
				
				$("#v-go-up").on("click",function(){
					if(!current_vehicle.ready)return;
						server.invoke({
								method:"position",
								action:"forward",
								data:{
									distance: Number($("#v-go-value").val())
								}
						},current_vehicle.vehicle_id);
				});
				$("#v-go-left").on("click",function(){
					if(!current_vehicle.ready)return;
						server.invoke({
								method:"position",
								action:"left",
								data:{
									distance: Number($("#v-go-value").val())
								}
						},current_vehicle.vehicle_id);
				});
				$("#v-go-right").on("click",function(){
				if(!current_vehicle.ready)return;
						server.invoke({
								method:"position",
								 action:"right",
								data:{
									distance: Number($("#v-go-value").val())
								}
						},current_vehicle.vehicle_id);
				});
				$("#v-go-down").on("click",function(){
						if(!current_vehicle.ready)return;
						server.invoke({
								method:"position",
								action:"retreat",
								data:{
									distance: Number($("#v-go-value").val())
								}
						},current_vehicle.vehicle_id);
				});
				$(".hidden-video").on("click",function(){
					if(!current_vehicle.ready)return;
					$(".box-view").toggleClass("hide-video");
				});
				$(".val-signal").on("click",function(){
					if(!current_vehicle.ready)return;
					$(this).toggleClass("locked");
					if( $(this).hasClass("locked") ){
						layer.viewer.trackedEntity=window.current_vehicle.entity;
					}else{
						layer.viewer.trackedEntity=null;
					}
				});


			});

		});
		function init_video(){ 
			console.log("init_video");
		}
	</script>
	}

}


@* 	//显示航线
						//显示HOME点
						//显示遥控器点
						//显示飞行HUD
						//显示视频控制
						//显示飞机控制 

						//加载飞机航线

						[锁定状态  飞行状态 () 电量 信号]
						[           飞行时间
						  航向      任务剩余时间
						            任务预计时间
						]
						油门显示
						跟踪开关
						//显示视频和关闭显示视频
						//视频中  需要 1.控制相机
						//		       2.
						//显示航向和隐藏航线
						//显示轨迹和隐藏轨迹  航点 需要显示球体,并且中间显示航点号,然后已经经过的航点显示灰色,目标航点显示绿色,未飞行的显示蓝色
						//指令反馈
						//制作 *@