﻿using SqlSugar;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Services
{
    public partial class DataBaseService
    {
        private class UserInherit(bool enabled)
        {
            public int Id { get; set; }
            public int ParentId { get; set; }
            public bool InheritEnabled { get; private set; } = enabled;
            private readonly object m_mutex = new();
            public void UpdateEnabled(bool enabled)
            {
                lock (m_mutex)
                {
                    InheritEnabled = enabled;
                }
                
            }
            public void UpdateChildEnabled(bool enabled)
            {
                lock (m_mutex)
                {
                    InheritEnabled = enabled && InheritEnabled;
                    foreach (var child in Childs)
                    {
                        child.UpdateChildEnabled(InheritEnabled);
                    }
                }
            } 
            public List<UserInherit> Childs { get; set; } = [];
        } 
        private readonly System.Collections.Concurrent.ConcurrentDictionary<int, UserInherit> UserInherits = new(); 
        public async Task<bool>  InitializeAsync(SqlSugarClient sql)
        {
            logger.LogInformation("{name}", "正在分析用户关系");
            var user_list = await sql.Queryable<TableUser>().ToListAsync();
            foreach (var user in user_list)
            {

                var parent = UserInherits.GetOrAdd(user.ParentUserId, (key) => {
                    return new UserInherit(true) { Id = user.ParentUserId };
                });
                var current = UserInherits.AddOrUpdate(user.Id, new UserInherit(user.UserStatus == UserStatus.Normal) { Id = user.Id, ParentId = user.ParentUserId }, (key, src) =>
                {
                    src.UpdateEnabled(user.UserStatus == UserStatus.Normal);
                    return src;
                });
                parent.Childs.Add(current);
            }
            if (UserInherits.TryGetValue(0, out var root))
            {
                root.UpdateChildEnabled(true);
            }

            return true;
        }
        public bool UserEnabled(int id)
        {
            if (UserInherits.TryGetValue(id, out var result)) { return result.InheritEnabled; } 
            return false;
        }


        public void UpdateUserEnabled(TableUser user)
        {
            lock (UserInherits)
            {
                if (UserInherits.Count == 0)
                {
                    UserInherits.TryAdd(0, new UserInherit(true)
                    {
                        Id = 0,
                        ParentId = 0,
                    });
                }
                if (UserInherits.TryGetValue(user.ParentUserId, out var parent))
                {
                    if (UserInherits.TryGetValue(user.Id, out var current))
                    {
                        current.UpdateChildEnabled(user.UserStatus == UserStatus.Normal && parent.InheritEnabled);
                    }
                    else
                    {
                        UserInherits.TryAdd(user.Id, current = new UserInherit(user.UserStatus == UserStatus.Normal && parent.InheritEnabled)
                        {
                            ParentId = parent.Id,
                            Id = user.Id,
                        });
                        parent.Childs.Add(current);
                    }

                }

            }
        }

    }
}
