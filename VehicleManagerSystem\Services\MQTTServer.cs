﻿using System.Buffers;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using Dm.filter;
using Dm.util;
using MQTTnet.Server;
using VehicleManagerSystem.Tables;
using VehicleManagerSystem.Utils;

namespace VehicleManagerSystem.Services
{
    public class MQTTServer(DataBaseService database,ILogger<MqttServer> logger)
    {
        public bool isRunning { get; private set; } = false;
        private MqttServerFactory m_mqtt_server_factory = new();
        private MqttServer m_mqtt_server = null!;
        public async Task StopAsync(CancellationToken token)
        {
            await m_mqtt_server.StopAsync();
        }
        private async Task StoppedAsync(EventArgs arg)
        {
            isRunning = false;
            await Task.CompletedTask;
        }
        public async Task StartAsync(CancellationToken token)
        {
            m_mqtt_server = m_mqtt_server_factory.CreateMqttServer(new MqttServerOptionsBuilder()
                  .WithDefaultEndpoint()
                  .WithDefaultEndpointPort(11883)
                  .Build());
            m_mqtt_server.InterceptingPublishAsync += OnInterceptingPublishAsync;
            m_mqtt_server.ClientConnectedAsync += OnClientConnectedAsync;
            m_mqtt_server.ClientDisconnectedAsync += OnClientDisconnectedAsync;
            m_mqtt_server.ClientUnsubscribedTopicAsync += ClientUnsubscribedTopicAsync;
            m_mqtt_server.ClientSubscribedTopicAsync += ClientSubscribedTopicAsync;
            m_mqtt_server.StartedAsync += StartedAsync;
            m_mqtt_server.StoppedAsync += StoppedAsync;

            m_mqtt_server.ValidatingConnectionAsync += ValidatingConnectionAsync;
            await m_mqtt_server.StartAsync();
        }
        private async Task StartedAsync(EventArgs arg)
        {
            isRunning = true;
            await Task.CompletedTask;
        }
        private async Task ValidatingConnectionAsync(ValidatingConnectionEventArgs arg)
        {
            if (string.IsNullOrWhiteSpace(arg.UserName) || string.IsNullOrWhiteSpace(arg.Password))
            {
                arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.BadUserNameOrPassword;
                return;
            }
            if (arg.UserName.startsWith("@"))
            {
                string UserName = arg.UserName.substring(1);
                if (!await database.SqlExecuteAsync(async sql =>
                 {
                     var vehicle = await sql.Queryable<TableVehicle>().Where(it => it.VehicleSerial == UserName).FirstAsync();
                     if (vehicle == null)
                     {
                         return false;
                     }//f78851e1f794b46a5d463c2441aa6af2
                     var md5 = string.Join("", MD5.HashData(Encoding.UTF8.GetBytes(arg.Password + "@@vehicle")).Select(it => it.ToString("x2")));
                     //if (md5 != vehicle.CommunSecret)
                     //{
                     //    return false;
                     //}
                     return true;
                 }))
                {
                    arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.BadUserNameOrPassword;
                    return;
                }
                else
                {
                    arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.Success;
                       return;
                }
            }
            else if (arg.UserName.startsWith("#"))
            {  
                if (arg.UserName != "#LOCAL" || arg.Password != "#PASSWORD") { 
                    arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.NotAuthorized;
                    return;
                }
                if(arg.RemoteEndPoint is IPEndPoint remote)
                {
                    if (remote.Address.MapToIPv4().ToString() != "127.0.0.1")
                    {
                        arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.NotAuthorized;
                        return;
                    }
                    else
                    {
                        arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.Success;
                          return;
                    }
                }
                else
                {
                    arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.NotAuthorized;
                    return;
                }
                
            }
            else if (arg.UserName.startsWith("!"))
            {
                string UserName = arg.UserName.substring(1);
                if (!await database.SqlExecuteAsync(async sql =>
                {
                    var user = await sql.Queryable<TableUser>().Where(it => it.UserName == UserName).FirstAsync();
                    if (user == null)
                    {
                        return false;
                    }
                    if (string.Join("", MD5.HashData(Encoding.UTF8.GetBytes(arg.Password)).Select(it => it.ToString("x2"))) != user.Password)
                    {
                        return false;
                    }
                    if (user.UserStatus != UserStatus.Normal)
                    {
                        return false;
                    }
                    if (!database.UserEnabled(user.Id))
                    {
                        return false;
                    }
                    return true;
                }))
                {
                    arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.BadUserNameOrPassword;
                    return;
                }
                else
                {
                    arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.Success;
                       return;
                }
            }
            arg.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.NotAuthorized;
            return;

        }
        private async Task ClientUnsubscribedTopicAsync(ClientUnsubscribedTopicEventArgs e)
        {
               logger.LogWarning($"取消订阅消息:{e.ClientId} {e.TopicFilter}");
               await Task.CompletedTask;
        }
        private async Task ClientSubscribedTopicAsync(ClientSubscribedTopicEventArgs e)
        { 
              logger.LogWarning($"订阅消息:{e.ClientId} {e.TopicFilter}");
            await Task.CompletedTask;
        }

        private  Task OnClientConnectedAsync(ClientConnectedEventArgs e)
        {
              logger.LogWarning($"客户端已连接: ClientId = {e.ClientId}");
            return Task.CompletedTask;
        }
        private  Task OnClientDisconnectedAsync(ClientDisconnectedEventArgs e)
        {
            logger.LogWarning($"客户端已断开连接: ClientId = {e.ClientId}");
            return Task.CompletedTask;
        }
        private  Task OnInterceptingPublishAsync(InterceptingPublishEventArgs e)
        {
            logger.LogInformation($"收到消息: Topic = {e.ApplicationMessage.Topic}, Payload = {Encoding.UTF8.GetString(e.ApplicationMessage.Payload.ToArray())}");
            return Task.CompletedTask;
        }
    }
}
