﻿@page
@using VehicleManagerSystem.Tables
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Admin.UserModel
@{
	Layout = "_LayoutAdminFrame";
	ViewData["Title"] = language["INDEX_MENU_USER_MANAGER"];
}

@section Toolbar {
	<form name="toolbar-search" action="javascript:;" style='display:flex;display: flex;align-items: center;gap: 8px;'>
		<layui-select target="UserStatus.None" name="status" lay-verify=""></layui-select>
		<layui-select target="UserRole.None" name="type" lay-verify=""></layui-select>
		<input readonly readonly-fill type="text" name="keyword" lay-verify="required" placeholder="@Html.Raw(language["INPUT_TEXT_SEARCH"])" autocomplete="off" class="layui-input search-ipt" />
		<button type="submit" class='search-submit layui-btn' lay-submit lay-filter="search">@Html.Raw(language["BUTTON_TEXT_SEARCH"])</button>
		<div class='layui-icon-down search-submit layui-btn add-btn'>@Html.Raw(language["BUTTON_TEXT_ADD"])</div>
	</form>
}
<table id="table-user"></table>
<div type="text/template" id="add-template" style="display:none">
	<div vui-label="@Html.Raw(language["LABEL_USER_NAME"])"><input name="username" readonly readonly-fill required type="text" placeholder="@Html.Raw(language["INPUT_CREATE_TEXT_USERNAME"])" title="@Html.Raw(language["INPUT_CREATE_TEXT_USERNAME"])" autocomplete="off" /></div>
	<div vui-label="@Html.Raw(language["LABEL_PASSWORD"])"><input name="password" readonly readonly-fill required type="text" placeholder="@Html.Raw(language["INPUT_CREATE_TEXT_PASSWORD"])" title="@Html.Raw(language["INPUT_CREATE_TEXT_PASSWORD"])" autocomplete="off" /></div>
	<div vui-label="@Html.Raw(language["LABEL_USER_NICK"])"><input name="displayname" readonly readonly-fill required type="text" placeholder="@Html.Raw(language["INPUT_TEXT_DISPLAY_NAME"])" title="@Html.Raw(language["INPUT_TEXT_DISPLAY_NAME"])" autocomplete="off" /></div>
	<div vui-label="@Html.Raw(language["LABEL_USER_ROLE"])"><layui-select target="UserRole.Monitor" name="status" lay-verify="" style="flex:1"></layui-select></div>
	
</div>
 
@section Scripts {
	<script id="user-status-tpl" type="text/html">
		 {{# if(false){ }}
		@foreach (var val in Enum.GetValues<UserStatus>())
		{
			@:{{# } else if(d.userStatus== @((int)val)){ }}
						<span>@Html.Raw(language[Utils.EnumsUtil.DisplayName(val)])</span>
		}
		{{# } }}

	</script>
	<script id="user-role-tpl" type="text/html">
		 {{# if(false){ }}
		@foreach (var val in Enum.GetValues<UserRole>())
		{
			@:{{# } else if(d.userRole== @((int)val)){ }}
						<span>@Html.Raw(language[Utils.EnumsUtil.DisplayName(val)])</span>
		}
		{{# } }}
	</script>
	<script id="action-tpl" type="text/html">
		{{# if(d.userStatus !=@((int)UserStatus.Disabled)){ }}
		<span class="vui-button">@Html.Raw(language["BUTTON_ACTION_DISABLE"])</span>
		{{# }else if(d.userStatus!=@((int)UserStatus.Normal)){ }}
			<span class="vui-button normal">@Html.Raw(language["BUTTON_ACTION_ENABLE"])</span>
		{{# } }}
		<span class="vui-button normal">@Html.Raw(language["BUTTON_ACTION_PASSWORD"])</span>
		<span class="vui-button normal">@Html.Raw(language["PAGE_USER_BUTTON_ACTION_VEHICLE_LIST"])</span>
	</script>
	<script>
		//第一个实例
			layui.use(['layer', 'form','table'], function () {
				var layer = layui.layer
				   , form = layui.form
				   ,table = layui.table;
				   table.render({
						elem: '#table-user'
					   ,height: "100%"
					   ,url: '/api/account/list'
					   ,page: true
					   ,cols: [[
						  {field: 'userName'			, title: '@Html.Raw(language["LABEL_USER_NAME"])'}
						 ,{field: 'nickName'			, title: '@Html.Raw(language["LABEL_USER_NICK"])'}
						 ,{field: 'userRole'			, title: '@Html.Raw(language["LABEL_USER_ROLE"])',templet:"#user-role-tpl"}
						 ,{field: 'userStatus'			, title: '@Html.Raw(language["LABEL_USER_STATUS"])',templet:"#user-status-tpl"}
						 ,{field: 'action'				, title: '@Html.Raw(language["LABEL_ACTION"])',templet:"#action-tpl"}
					   ]],
					   parseData:function(res){
						   if(res.success){
							   return {
								   "code": res.data.code, // 解析接口状态
								   "msg": "", // 解析提示文本
								   "count": res.data.count, // 解析数据长度
								   "data": res.data.list// 解析数据列表
							   };
						   }else{
							   return {
								   "code": -1, // 解析接口状态
								   "msg": res.data.message, // 解析提示文本
								   "count": res.data.count, // 解析数据长度
								   "data":null// 解析数据列表
							   };
						   }

					   }

					 });
					 $("[name='toolbar-search']").on("submit",function(){
						 table.reload('table-user', {
							   page: {curr: 1},
							   where: {
									   type:$('[name="toolbar-search"]>vui-select[name="type"]>vui-selected').attr("value"),
									   status:$('[name="toolbar-search"]>vui-select[name="status"]>vui-selected').attr("value"),
									   keyword:$('[name="toolbar-search"]>input[name="keyword"]').val()
							   }
						   });

					 });
					 vui.use(["panel"], function () {

						 $(".add-btn").on("click",function(){

							   new vui.Form({
								   before:function(content){
									   content.append($("#add-template").html());
								   },
								   yes:function(field){
									   api.addVehicle(field.serial,field.vehicle_name,function(res){
										   if(res.success){
											   //刷新页面或添加到第一条
										   }
									   });
								   }
							   }).appendTo($("body"));
						 });
					 });



		   });


	</script>
}
