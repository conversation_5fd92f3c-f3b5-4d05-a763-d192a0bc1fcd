﻿using Microsoft.AspNetCore.Localization;
using System.Globalization;

namespace VehicleManagerSystem.Languages
{
    public class LanguageSupportProvider: RequestCultureProvider
    { 
        public override async Task<ProviderCultureResult?> DetermineProviderCultureResult(HttpContext httpContext)
        {
            if (httpContext == null)
                throw new ArgumentNullException(nameof(httpContext));
            var language = httpContext.Request.Query["language"].ToString();
            if (string.IsNullOrEmpty(language))
                return await NullProviderCultureResult;
            var cultureInfo = new CultureInfo(language);
            return await Task.FromResult(new ProviderCultureResult(cultureInfo.Name, cultureInfo.Name));
        }
    }
}
