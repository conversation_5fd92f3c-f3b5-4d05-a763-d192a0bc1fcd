using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace VehicleManagerSystem.Pages
{
    public class SetLanguageModel : PageModel
    {
        public string Language { get; set; } = null!;
        public string Source { get; set; } = null!;
        public void OnGet(string locale,string source)
        {
            Language = locale;
            Source = source;
            if (!string.IsNullOrWhiteSpace(locale))
            { 
                Response.Cookies.Append(
                    CookieRequestCultureProvider.DefaultCookieName,
                    CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(locale)),
                    new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
                );
            }
             
        }
    }
}
