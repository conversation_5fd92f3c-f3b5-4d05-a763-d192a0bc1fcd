// noinspection JSCheckFunctionSignatures


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion: '/modules/expansion',
    qrcode:'/lib/qrcode'
}).define(['jquery', 'form', 'table', 'expansion', 'handle','qrcode'], function (exports) {
    const table_name = "user-table";
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        handle = layui.handle,
        $ = layui.jquery,
        qrcode = layui.qrcode,
        module = {
            build: function (target,role) {
                let columns = [[]];
                columns[0] = [];
                columns[0].push({ field: "Id", title: "用户ID"});
                if (role == "superadmin") columns[0].push({ field: "ParentUid", title: "上级" });
                columns[0].push({field: "UserName", title: "用户名"});
                columns[0].push({ field: "TodayAmount", title: "今日流水",  templet: "#today-amount-tpl"});
                columns[0].push({ field: "TotalAmount", title: "总流水",  templet: "#total-amount-tpl"});
                columns[0].push({ field: "Balance", title: "余额",  templet: "#balance-amount-tpl"});
                columns[0].push({ field: "FreezeBalance", title: "冻结余额",  templet: "#freeze-balance-amount-tpl"});
                columns[0].push({ field: "Status", title: "用户状态", templet: "#user-status-tpl" });
                if (target =="merchant")  columns[0].push({ field: "acc_num", fixed: "right", width: 80, title: "资源数量", templet: "#account-tpl" });
                columns[0].push({ field: "operations",fixed:"right", title: "操作",templet: "#operation-tpl",minWidth:185});
                return columns;
            },
            render: function (target,role) {
                table.render({
                    elem: "#" + table_name,
                    url: "/api/account/list/" + target,  
                    height: "100%",
                    cellMinWidth: 80,
                    page: true,
                    skin:"line ",
                    defaultToolbar: ['filter'],
                    toolbar: "#toolbar",
                    limit: 30,
                    cols:module.build(target,role),
                    parseData: function (res) {
                        return expansion.parse(res);
                    }, done: function () {
                       expansion.render();
                    }
                });
                
                let commands={
                    /*重置密码*/
                    reset_user_password:function (uid){
                        $.ajax({
                            url:"/api/account/reset/"+target+"/password?user_uid="+uid,
                            type:"get",
                            loading:true,
                            dataType: "json",
                            success:function (res){
                                if(res.success){
                                    layer.msg("密码已重置", { icon: 1, time: 2000 }, function () { });
                                }
                            }
                        });
                    },
                    /*重置谷歌*/
                    reset_user_google:function (uid){
                        $.ajax({
                            url:"/api/account/reset/"+target+"/google?user_uid="+uid,
                            type:"get",
                            loading:true,
                            dataType: "json",
                            success:function (res){
                                if(res.success){
                                    layer.msg("谷歌验证器已解绑", { icon: 1, time: 2000 }, function () { });
                                }
                            }
                        });
                    },
                    /*余额重置*/
                    charge_user_balance:function (uid,amount,obj){
                        $.ajax({
                            url:"/api/account/charge/"+target+"?user_uid="+uid,
                            type:"post",
                            loading:true,
                            dataType: "json",
                            data: JSON.stringify({
                                amount:Number(amount)
                            }),
                            success:function (res){
                                if(res.success){
                                    layer.msg("操作成功", { icon: 1, time: 2000 }, function () { });
                                    if (res.body) {
                                        obj.update({
                                            Balance: res.body.balance
                                        });
                                    }
                                }
                            }
                        });
                    },
                    update_status:function (user_uid,status,obj){
                        $.ajax({
                            url: "/api/account/update/"+target+"/status?user_uid="+user_uid,
                            type: "post",
                            data: JSON.stringify({
                                status:status
                            }),
                            loading: true,
                            dataType: "json",
                            success: function (res) {
                                if(!res.success){
                                    obj.elem.checked = !obj.elem.checked;
                                    form.render();
                                }
                            },error:function (){
                                obj.elem.checked = !obj.elem.checked;
                                form.render();
                            }
                        })
                    },

                    update_priority: function (user_uid, priority,old,obj) {
                        $.ajax({
                            url: "/api/account/update/" + target + "/priority?user_uid=" + user_uid,
                            type: "post",
                            data: JSON.stringify({
                                priority: priority
                            }),
                            loading: true,
                            dataType: "json",
                            success: function (res) {
                                if (!res.success) {
                                    obj.update({
                                        Priority: old
                                    });
                                }
                            }, error: function () {
                                obj.update({
                                    Priority: old
                                });
                            }
                        })
                    }
                };
                var get_channel = function (uid,success) {
                    $.ajax({
                        url: "/api/resource/merchant/channel?user_uid="+uid,
                        type: "get",
                        loading: true,
                        dataType: "json",
                        success: function (res) {
                            if (res.success) {
                                success(res.body);
                            }
                        }
                    });
                };
                var wait_select_channel = function (list, selected,title,s,d) {

                  
                    if (typeof (title) == 'undefined') title = "请选择通道";
                    if (typeof (s) == 'undefined') s = "ChannelCode";
                    if (typeof (d) == 'undefined') d = "Description";

                    let select_key = "s_" + new Date().valueOf();
                    var select = $('<select lay-verify="required" name="' + select_key + '"></select>');
                    select.append($("<option value=''" + title +"</option>"))
                    for (var n in list) {
                        let item = list[n];
                        select.append($("<option value='" + item[s] + "'>" + item[d] + "</option>"))
                    }
                    layer.prompt({
                        formType: 0,
                        title: title,
                        value: '',
                        offset: ["165px"],
                        success: (layero) => {
                            $(layero).addClass("layui-form")
                            $(layero).find("input").before(select).hide();
                            layui.form.render($(layero));
                        }
                    }, function (value, index, elem) {
                        elem = elem.parent().find("select");
                        var channel_code = elem.val();
                        if (channel_code === "") {
                            layer.tips(title, elem.parent().find(".layui-form-select"), { tips: 1 });
                            return;
                        }
                        selected(channel_code);
                        layer.close(index);
                    });
                };
                var wait_payment_type = function (channel,selected) {
                    $.ajax({
                        url: "/api/resource/channel/payment_type?channel=" + channel,
                        type: "get",
                        loading: true,
                        dataType: "json",
                        success: function (res) {

                            if (res.success) {
                                wait_select_channel(res.body, selected,"请选择支付类型","val","name")
                            }
                        }
                    })
                }
                let event_handler=function (event,obj){
                
                    switch (event) {
                        case "create-test-order": {
                            get_channel(obj.data.Id, function (lst) {
                                wait_select_channel(lst, function (channel) {
                                    wait_payment_type(channel, function (payment) {
                                        layer.prompt({
                                            formType: 1,
                                            title: '请输入订单金额',
                                            value: '',
                                            success: (layero, index, that) => {
                                                layui.$(layero).find('input').attr('type', 'number');
                                                layui.form.render(layui.$(layero).find('input'));
                                            }
                                        }, function (value, index, elem) {
                                            if (isNaN(value) || value.trim() === "") {
                                                layer.tips('请输入正确的金额', this);
                                                return;
                                            }
                                            $.ajax({
                                                url: "/api/order/testcreate?amount=" + value + "&channel=" + channel + "&user_uid=" + obj.data.Id + "&type=" + payment,
                                                type: "get",
                                                dataType: "json",
                                                success: function (res) {
                                                    if (res.success) {
                                                        layer.open({
                                                            type: 1,
                                                            area: '350px',
                                                            offset: ["165px"],
                                                            resize: false,
                                                            shadeClose: false,
                                                            title: '测试订单',
                                                            offset: ["24px"],
                                                            content: $("#build-order-tpl").html(),
                                                            success: function (layero, index, elm) {
                                                                var qr = new qrcode.qrcode($(layero).find('[ui-name="qr-image"]')[0], {
                                                                    text: res.body.url, //扫描二维码后的内容
                                                                    width: 128, //二维码的宽
                                                                    height: 128, //二维码的高
                                                                    colorDark: "#000", //二维码线条颜色
                                                                    colorLight: "#ffffff", //二维码背景颜色
                                                                    correctLevel: QRCode.CorrectLevel.H //二维码等级
                                                                });
                                                                $(layero).find('[lay-filter="open-url"]').attr("next", res.body.url)
                                                                form.on('submit(open-url)', function (data) {
                                                                    window.open($(this).attr("next"));
                                                                    return false;
                                                                });
                                                            }
                                                        });
                                                    }
                                                }
                                            })
                                            layer.close(index);
                                        });
                                    }); 

                                });
                            });    
                          
                            break;
                        }
                        case "user-reset-password":{
                            layer.confirm('此操作会重置用户密码为初始密码', { icon: 3, title: '警告' }, function (index) {
                                commands.reset_user_password(obj.data.Id);
                                layer.close(index);
                            });
                            break;
                        }
                        case "user-reset-google":{
                            layer.confirm('此操作会解绑谷歌验证器', { icon: 3, title: '警告' }, function (index) {
                                commands.reset_user_google(obj.data.Id);
                                layer.close(index);
                            });
                            break;
                        }
                        case "user-charge":{
                            layer.prompt({
                                formType: 1,
                                title: '请输入授信金额',
                                value: '',
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的金额', this);
                                    return;
                                }
                                commands.charge_user_balance(obj.data.Id,value,obj);
                                layer.close(index);
                            });
                            break;
                        }
                        case "user-module-authorize":{

                            var call_update = function (event,data,result) {
                                $.ajax({
                                    url: "/api/account/set/channel/" + target +"/" + event,
                                    type: "post",
                                    data: JSON.stringify(data),
                                    loading: true,
                                    dataType: "json",
                                    success: function (res) {
                                        result(res.success);
                                       
                                      
                                    },error:function (){
                                        result(false);
                                    }

                                });
                            }
                            var build = function () { 

                                let target_uid=obj.data.Id;
                                $.ajax({
                                    url: "/api/account/get/channels/" + target +"?user_uid=" + target_uid,
                                    type: "get",
                                    dataType: "json",
                                    success: function (res) {
                                        if (res.success) {
                                            if (res.body.channels.length === 0) {
                                                layer.msg("无可用通道", { icon: 2, time: 2000 }, function () { })
                                            } else {
                                                var content = $("<div class='channel-module-view'></div>");
                                                for (let n = 0; n < res.body.channels.length; n++) {
                                                    var channel = res.body.channels[n];
                                                    var item = $("<div class='channel-module'></div>");
                                                    item.append("<div class='title'><span>" + channel.ChannelDescription + "</span><span lay-copy>" + channel.ChannelCode + "</span></div>")
                                                    var rate = $("<input class='layui-input'  readonly type='number'  value='" + (channel.Rate / 100.0).toFixed(2) + "'/>");
                                                    item.append(
                                                        $("<div label='' class='config' channel='" + channel.ChannelCode + "'  lay-change='input[type=\"number\"]' ></div>")
                                                            .append(rate)
                                                            .append('<input type="checkbox" ' + (channel.Enabled ? "checked" : "") + ' lay-skin="switch" lay-text="开|关" lay-filter="update-channel-state">'));
                                                    content.append(item);

                                                }
                                                layer.open({
                                                    title: "通道权限",
                                                    type: 1,
                                                    fixed: true,
                                                    maxmin: false,
                                                    shadeClose: false,
                                                    resize: true,
                                                    offset: ['30px'],
                                                    area: ['280px'],
                                                    content: content.prop('outerHTML'),
                                                    success: function (layero, index, that) {
                                                        $(layero).addClass("layui-form"); 
                                                        $(layero).find(".layui-layer-content").attr('style', function (i, style) {
                                                            return style + "height:unset; max-height: 80vh; overflow-y: auto !important;";
                                                        });
                                                        ;
                                                        layui.form.render($(layero));
                                                        handle.render($(layero));
                                                        layero.find("input").on("save", function (e, elem, succ) {
                                                            if(Number(elem.val())>100){
                                                                layer.tips("费率不能超过100%", elem, {
                                                                    tips: 1
                                                                });
                                                                return ;
                                                            }
                                                            call_update("rate", {channel: elem.parent().attr("channel"), rate: elem.val(), user_uid: target_uid }, function (s) {
                                                                if (s) {
                                                                    succ();
                                                                }
                                                            });
                                                        });
                                                         
                                                        layui.form.on("switch(update-channel-state)", function (checkbox) {
                                                            call_update("status", { rate_id: $(checkbox.elem).parent().attr("rid"), channel: $(checkbox.elem).parent().attr("channel"), status: checkbox.elem.checked, user_uid: target_uid }, function (s) {
                                                                if (!s) {
                                                                    checkbox.elem.checked = !checkbox.elem.checked;
                                                                    form.render($(checkbox.elem));
                                                                }
                                                            });
                                                        });

                                                    }
                                                });
                                            }
                                        }
                                    }
                                });
                            }

                            build();
                            break;
                        }
                        case "user-create":{ 
                            let begin=function (){
                                layer.open({
                                    title:  function (){switch (target) {
                                        case "merchant": return "添加商户";
                                        case "inventory": return "添加订单代理";
                                        case "account": return "添加小号代理"; 
                                        case "application": return "添加应用代理";
                                    }}(),
                                    type:1,
                                    fixed: true,
                                    maxmin: false,
                                    shadeClose: false,
                                    resize: true,
                                    offset: ['30px'],
                                    area: ['280px'],
                                    content: $("#create-user-tpl").html(),
                                    success: function (layero, index, that) {
                                        form.on('submit(create-user-btn)', function (data) {
                                            $.ajax({
                                                url: "/api/account/create/"+target,
                                                type: "post",
                                                dataType: "json",
                                                loading:true,
                                                data: JSON.stringify(data.field),
                                                success: function (res) {
                                                    if (res.success) {
                                                        layer.msg("创建成功", { icon: 1, time: 2000 }, function () { });
                                                        table.reload(table_name);
                                                        layer.close(index)
                                                    }
                                                }
                                            });
                                            return false;
                                        });
                                    }
                                });
                            }; 
                            begin();
                            break;
                        }
                        case "resource-number": {
                            get_channel(obj.data.Id, function (lst) {
                                wait_select_channel(lst, function (channel) {
                                    $.ajax({
                                        url: "/api/resource/valid_number/" + channel  +"?uid=" + obj.data.Id,
                                        type: "get",
                                        loading: true,
                                        dataType: "json",
                                        success: function (res) {
                                            if (res.success) {
                                                layer.msg(res.body.data, { icon: 1, time: 6000, shadeClose:true }, function () { });
                                            }
                                        }
                                    })
                                });
                            });
                            break;
                        }
                    }
                }
                form.on('switch(update-status)', function (obj) {
                    commands.update_status(this.value,obj.elem.checked,obj);
                });
                table.on('edit(' + table_name +')', function (obj) {

                    var old_val = obj.oldValue;
                    var priority = Number(obj.value);
                    if (isNaN(priority) || priority <= 0 || priority > 100) {
                        layer.msg("请输入一个0-100的数值");
                        obj.update({
                            Priority: old_val
                        });
                        return;
                    }
                    commands.update_priority(obj.data.Id, priority, old_val,obj); 
                });
                $(document).on("click",'[name="user-create"]', function () {
                    event_handler("user-create");
                });
                table.on('tool('+table_name+')',function (obj){ 
                    event_handler(obj.event,obj);
                });
            },
            submit: function (where) {
                table.reload(table_name, {
                    page: {curr: 1},
                    where: {
                        biz_context: JSON.stringify(where)
                    }
                });
            }
        };
  
    form.on("submit(search)", function (data) {
        let search={};
        
            for (let s in data.field) {
                if($(".grid-toolbar-item").hasClass("switch-status") ||s==="keyword" ) { 
                    let t = String(data.field[s]);
                    if (t !== "null" && t !== "undefined" && t !== "") {
                        search[s] = t;
                    }
                }
            }
      
        module.submit(search);
        return false;
    });
    $(document).on("click",'.grid-toolbar-item.show-more',function (){
        $(".grid-toolbar-item").toggleClass("switch-status");
    });
    exports('user', module);
});
 