﻿iframe {
    width: 100%;
    height: 100%;
    border: none;
    position: absolute;
    left: 0;
    bottom: 0;
    Z-INDEX: 0;
    BACKGROUND: TRANSPARENT;
}
* {
    transition: all .3s;
    -webkit-transition: all 0.3s;
}
.layui-table-click{
    background-color:unset;
}
.layui-table-view {
    height: 100%;
    border: none;
    display: flex;
    flex-direction: column;
}

.layui-table-view > .layui-table-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.layui-table-body {
    flex: 1;
}

.layui-table-header > .layui-table {
    background: transparent;
    color: #eee;
}



select {
    border: none;
    background: transparent;
    color: #eee;
    text-align: center;
    min-width: 80px;
}

option {
    background-color: #064266;
    color: #fff; /* 选项的文字颜色 */
    padding: 1px 4px;
    border-color: #ccc;
    margin: 0;
    padding: 0;
    -webkit-appearance: none;
    appearance: none;
}

option:checked, option:hover {
    background-color: #222; /* 悬停或选中的背景颜色 */
}

.layui-table-header {
    border-width: 1px;
    border-color: #02d6fb;
}

.search-ipt {
    flex: 1;
    border-left-color: #02d6fb66 !important;
    border-bottom-color: #02d6fb66 !important;
    border-top-color: #02d6fb66 !important;
    border-right-color: #02d6fb66 !important;
    background: transparent;
    border-radius: 0;
    height: 28px;
    color: #fff;
    outline: none;
}
.layui-input:hover {
    border-color: #02d6fbAA !important;
}
.layui-input:focus{
    border-color: #02d6fb !important;
}
.layui-table-grid-down{
   display:none !important;
}
.layui-laypage-limits > select {
    border-color: #02d6fb;
    border-radius: 0;
}

.search-submit {
    background: transparent;
    border-radius: 0;
    BOX-SIZING: border-box;
    padding: 0px 12px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #eee;
    font-family: weilai !important;
}

.search-submit:active {
    scale: 0.9;
}

.layui-table-page {
    border-color: #02d6fb !important;
}

.add-btn {
    margin-left: 0 !important;
    width: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    box-sizing: content-box;
}

[lay-submit] {
    border: 1px solid #02d6fb;
}

.add-btn:before {
    content: "";
    clip-path: polygon(50% 0,calc(100% - 12px) 0,100% 12px,100% 100%,0 100%,0 0,50% 0,50% 1px,1px 1px,1px calc(100% - 1px),calc(100% - 1px) calc(100% - 1px), calc(100% - 1px) 12.5px,calc(100% - 12.5px) 1px,50% 1px);
    background: #02d6fb;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.add-btn:after {
    content: "";
    background: #02d6fb33;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    clip-path: polygon(50% 0,calc(100% - 12px) 0,100% 12px,100% 100%,0 100%,0 0);
}
table {
    background-color: transparent !important;
}
.layui-table td {
    border-color: #02d6fb88;
}
.layui-table-view .layui-table td:first-child{
    border-left: 0.8px solid #02d6fb88;
}
.layui-table-view .layui-table th:last-child {
    border-right: none;
}
.layui-table-header > .layui-table th {
    border-color: #02d6fb88;
}
.layui-table-hover {
    background: #02d6fb33 !important;
}
.layui-table-pageview{
    margin-left:22px;
}
.layui-table-pageview .layui-laypage-skip input,
.layui-table-pageview .layui-laypage-skip button {
    border: 1px solid #02d6fb;
    background: transparent;
    border-radius: 0;
    color: #fff;
}
.layui-table-pageview .layui-laypage-curr {
    border: 1px solid #02d6fb;
    background: transparent !important;
    border-radius: 0;
    color: #fff;
}
.layui-table-pageview .layui-laypage-curr em:first-child {
   
    background: transparent !important;
    border-radius: 0;
    color: #fff;
}
.layui-laypage a[data-page] {
    background: transparent !important;
    border-radius: 0;
    color: #888;
}
.layui-laypage a[data-page]:hover {
    color: #02d6fb;
}
.layui-table-pageview .layui-laypage-skip button:hover {
    background: #02d6fb;
}
.layui-table-pageview .layui-laypage-skip button:active{
    scale:0.9;
}

.layui-table-pageview .layui-laypage-count {
    color:#888;
}
.layui-table-init{
    background:transparent !important;
}
.layui-table {
    color: #02d6fb;
}
.vui-button {
    border: 1px solid #02d6fb;
    background: #02d6fb88;
    color: #fff;
    padding: 4px 10px;
    cursor: pointer;
    padding-top: 6px;
    user-select: none;
}
.vui-button:hover {
    opacity: 0.8;
    background: #f2000088;
    border-color: #02d6fb;
}
.vui-button:active {
    border-color: #f2000088;
}
.vui-button.normal:hover {
    opacity: 0.8;
    background: #000000AA;
    border-color: #02d6fb;
}

.vui-button.normal:active {
    border-color: #000000AA;
}

/* 整体滚动条样式 */
*::-webkit-scrollbar {
    width: 12px; /* 垂直滚动条宽度 */
    height: 12px; /* 水平滚动条高度（可选） */
}

/* 滚动条轨道 */
*::-webkit-scrollbar-track {
    background: #02d6fb22;
    border-radius: 0px;
}

/* 滚动条滑块（拖拽区域） */
*::-webkit-scrollbar-thumb {
    background: #02d6fb;
    border-radius: 0px;
}

/* 鼠标悬停时的滑块颜色 */
*::-webkit-scrollbar-thumb:hover {
    background: #02d6fb88;
}

/* 可选：水平滚动条轨道 */
*::-webkit-scrollbar:horizontal {
    height: 12px;
}

vui-select {
    height: 28px;
    display: block;
    flex-direction: column;
    color: #fff;
    cursor:pointer;
    user-select:none;
}
vui-select > vui-selected {
    background: rgba(2, 64, 101, 0.9);
    border: 1px solid #02d6fb;
    width: 100%;
    display: block;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 4px;
    
}
vui-select > vui-selected::after {
    content: "\e61a";
    font-family: layui-icon !important;
    font-size: 16px;
    font-style: normal;
    padding:4px;
}
vui-select.open > vui-selected::after {
    content: "\e619";
}
vui-select > vui-selected::before {
    content: attr(title);
    padding: 4px;
    flex: 1;
}
vui-select > vui-options {
        
    display: none;
    position: absolute;
    background: rgba(2, 64, 101, 0.9);
    border: 1px solid #02d6fb;
    margin-top: 4px;
    box-shadow: 0px 0px 5px 0px #000;
    z-index: 2;
}
vui-select.open > vui-options {
    display: block;
}
vui-select > vui-options > vui-option {
    display: list-item;
    list-style:none;
    padding:4px 12px;
}
vui-select > vui-options > vui-option:hover {
    background: #02d6fb;
    color:#fff;
}