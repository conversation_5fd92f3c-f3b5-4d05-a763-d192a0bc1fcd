﻿using System.ComponentModel.DataAnnotations;

namespace VehicleManagerSystem.Tables
{
     
    public class TableUserToken : ITable
    {
        [SqlSugar.SugarColumn(ColumnDescription = "用户ID", IsIdentity = true, IsPrimaryKey =true)]
        public int Id { get; set; }

        [SqlSugar.SugarColumn(ColumnDescription ="用户ID",UniqueGroupNameList = ["USER_TOKEN"])]
        public int UserId { get; set; } = 0;

        [SqlSugar.SugarColumn(ColumnDescription = "登录密钥",UniqueGroupNameList = ["USER_TOKEN"])]
        public string  UserToken { get; set; } = null!;

        [SqlSugar.SugarColumn(ColumnDescription = "登录时间")]
        public DateTime LoginTime { get; set; } =DateTime.Now;
         
        [SqlSugar.SugarColumn(ColumnDescription = "登录IP",Length =16)]
        public string  IPAddress { get; set; } = null!;
        [SqlSugar.SugarColumn(ColumnDescription = "登录设备",Length =4096)]
        public string UserAgent { get; set; } = null!;
    }
}
