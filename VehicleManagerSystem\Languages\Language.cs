﻿using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Razor;
using System.Globalization;
namespace VehicleManagerSystem{ 
public class LanguageResource
    {
    

    }
}
namespace VehicleManagerSystem.Languages
{
   
    public static class LanguageExtension
    {
        public static void DefineLanguage(this WebApplicationBuilder? builder)
        {
            #region 多语言定义
            builder?.Services.AddLocalization(options => options.ResourcesPath = "Languages");
            builder?.Services.AddControllersWithViews()
                    .AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix)
                    .AddDataAnnotationsLocalization();
            builder?.Services.Configure<RequestLocalizationOptions>(options =>
            {
                var supportedCultures = new[]
                {
                    new CultureInfo("en"),
                    new CultureInfo("zh-CN"),
                };
                options.DefaultRequestCulture = new RequestCulture("zh-CN");
                options.SupportedCultures = supportedCultures;
                options.SupportedUICultures = supportedCultures;
                options.RequestCultureProviders.Insert(0, new LanguageSupportProvider());
            });
            #endregion
        }

    }
}
