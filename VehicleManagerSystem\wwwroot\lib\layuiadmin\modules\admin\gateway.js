// noinspection JSCheckFunctionSignatures


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion:'/modules/expansion',
}).define(['jquery','form', 'table','expansion'], function (exports) {
    const table_name = "gateway-table";
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        $ = layui.jquery,
        module = {
            build: function () {
                let columns = [[]];
                columns[0] = [];
                columns[0].push({field: "GatewayAddress", title: "网关地址"});
                columns[0].push({field: "IsEnabled", title: "状态",templet: "#gateway-status-tpl"});
                columns[0].push({field: "IsDefault", title: "默认",templet: "#gateway-default-tpl"});
                columns[0].push({field: "CreateTime", title: "添加时间"}); 
                return columns;
            },
            render: function () {
                table.render({
                    elem:"#"+table_name,
                    url: "/api/system/list/gateway",
                    cellMinWidth: 80,
                    height: "100%",
                    page: false,
                    defaultToolbar: ['filter'],
                    toolbar: "#toolbar",
                    limit: 30,
                    cols:module.build(),
                    parseData: function (res) {
                        return expansion.parse(res);
                    }, done: function () {
                        expansion.render();
                    }
                });

                let commands={
                    update_enabled:function (gateway_id,enabled,obj){
                        $.ajax({
                            url: "/api/system/gateway/update/enabled?gateway_id="+gateway_id,
                            type: "get",
                            data:{
                                enabled:enabled
                            },
                            loading: true,
                            dataType: "json",
                            success: function (res) {
                                if(!res.success){
                                    obj.elem.checked = !obj.elem.checked;
                                    form.render();
                                }
                            },error:function (){
                                obj.elem.checked = !obj.elem.checked;
                                form.render();
                            }
                        })
                    },
                    update_default:function (gateway_id,def,obj){
                        $.ajax({
                            url: "/api/system/gateway/update/default?gateway_id="+gateway_id,
                            type: "get",
                            data:{
                                default:def
                            },
                            loading: true,
                            dataType: "json",
                            success: function (res) {
                                if(!res.success){
                                    obj.elem.checked = !obj.elem.checked;
                                    form.render();
                                }else{
                                    let cache = table.cache[table_name];
                                    for (let n = 0; n < cache.length; n++) {
                                        if (Number(cache[n].GatewayId) === Number(gateway_id)) {
                                            cache[n].IsEnabled = true;
                                            cache[n].IsDefault = true;
                                           
                                        } else {
                                            cache[n].IsDefault = false;
                                        }
                                    }
                                    table.renderData(table_name);
                                    //修改自己
                                }
                            },error:function (){
                                obj.elem.checked = !obj.elem.checked;
                                form.render();
                            }
                        })
                    }
                };
                let event_handler=function (event,obj){
                    switch (event){
                        case "gateway-create":{
                            let begin=function (gateways){
                                layer.open({
                                    title:  "添加网关",
                                    type:1,
                                    fixed: true,
                                    maxmin: false,
                                    shadeClose: false,
                                    resize: true,
                                    offset: ['30px'],
                                    area: ['280px'],
                                    content: $("#create-gateway-tpl").html(),
                                    success: function (layero, index, that) { 
                                        form.on('submit(create-gateway-btn)', function (data) {
                                            
                                            $.ajax({
                                                url: "/api/system/gateway/create",
                                                type: "post",
                                                dataType: "json",
                                                loading:true,
                                                data: JSON.stringify(data.field),
                                                success: function (res) {
                                                    if (res.success) {
                                                        layer.msg("创建成功", { icon: 1, time: 2000 }, function () { });
                                                        table.reload(table_name);
                                                        layer.close(index)
                                                    }
                                                }
                                            });
                                            return false;
                                        });
                                    }
                                });
                            };
                            begin();
                            break;
                        }
                    }
                }
                form.on('switch(update-enabled)', function (obj) {
                    commands.update_enabled(this.value,obj.elem.checked,obj);
                });
                form.on('switch(update-default)', function (obj) {
                    commands.update_default(this.value,obj.elem.checked,obj);
                });
                $(document).on("click",'[name="gateway-create"]', function () {
                    event_handler("gateway-create");
                });
                table.on('tool('+table_name+')',function (obj){
                    event_handler(obj.event,obj);
                });
            },
            submit: function (where) {
                table.reload(table_name, {
                    where: {
                        biz_content: JSON.stringify(where)
                    }
                });
            }
        };

    form.on("submit(search)", function (data) {
        let search={};

        for (let s in data.field) {
            if($(".grid-toolbar-item").hasClass("switch-status") ||s==="keyword" ) {
                let t = String(data.field[s]);
                if (t !== "null" && t !== "undefined" && t !== "") {
                    search[s] = t;
                }
            }
        }

        module.submit(search);
        return false;
    });
    $(document).on("click",'.grid-toolbar-item.show-more',function (){
        $(".grid-toolbar-item").toggleClass("switch-status");
    });
    exports('gateway', module);
});
 