// noinspection JSCheckFunctionSignatures


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion:'/modules/expansion',
}).define(['jquery','form', 'table','expansion'], function (exports) {
    const table_name = "channel-table";
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        $ = layui.jquery,
        module = {  
            render: function () {
                $.ajax({
                    url: "/api/system/list/permission",
                    type: "get",
                    dataType: "json",
                    loading: true,
                    success: function (res) {
                        if (res.success) {
                            res = res.permission;
                            let root_node = {};
                            for (let role in res.all) {
                                let root = root_node[role] = { children: {} };
                                for (let pi in res.all[role]) {
                                    permission = res.all[role][pi];
                                    if (permission.Permission.indexOf("/") == -1) {
                                        let cur = root.children[permission.Permission];
                                        if (!cur) {
                                            root.children[permission.Permission] = {
                                                name: permission.Name,
                                                edit: permission.CanEdit,
                                                enabled: false,
                                                children: {}
                                            };
                                        } else {
                                            cur.name = permission.Name;
                                            cur.edit = permission.CanEdit;
                                        }
                                    }
                                    else {
                                        var path = permission.Permission.split("/");
                                        var node = root;
                                        for (var i in path) {
                                            var next_node = node.children[path[i]];
                                            if (!next_node) {
                                                node.children[path[i]] = next_node = { children: {} };
                                            }
                                            node = next_node;
                                        }
                                        if (node != root) {
                                            node.name = permission.Name;
                                            node.edit = permission.CanEdit;
                                            node.enabled = false;
                                        }
                                    }
                                }
                            }

                            for (let hold in res.current_permission) {
                                let root = root_node[hold];
                                for (let pi in res.current_permission[hold]) {
                                    let p = res.current_permission[hold][pi];
                                    if (p.indexOf("/") == -1) {
                                        let cur = root.children[p];
                                        if (cur) {
                                            cur.enabled = true;
                                        }
                                    } else {
                                        var path = p.split("/");
                                        var node = root;
                                        for (var i in path) {
                                            node = node.children[path[i]];
                                        }
                                        if (node && node != root) {
                                            node.enabled = true;
                                        }
                                    }
                                }
                            }

                            var build = function (elem, node) {
                                var ul = $("<ul class='permission-node'></ul>");
                                elem.append(ul); 
                                for (var child in node.children) {
                                    let li = $("<li name='" + child + "'></li>");
                                    child = node.children[child];
                                    if (child.enabled) {
                                        li.append("<input type='checkbox' " + (child.edit ? '' : 'disabled') + " lay-filter='permission-checkbox' checked  title='" + child.name + "'/>");
                                    } else {
                                        li.addClass("disibled-child")
                                        li.append("<input type='checkbox' " + (child.edit ? '' : 'disabled') + " lay-filter='permission-checkbox'  title='" + child.name + "'/>");
                                    }
                                    if (child.children && Object.keys(child.children).length > 0) {
                                        li.prepend("<i class='permission-action layui-icon layui-icon-subtraction'></i>");
                                        build(li, child);
                                    } else {
                                        li.addClass("no-child");
                                        
                                    }
                                    ul.append(li);
                                } 
                            }
                            for (var role in root_node) {
                                var elem = $("#permission_" + role); 
                                if (elem.length > 0) {
                                    build(elem, root_node[role]);
                                } 
                            }
                            form.render()
                            //permission_SuperAdmin
                        }
                    }
                })
            },
           
        }; 
    $(document).on("click", ".permission-action", function () {
        if ($(this).hasClass("disibled")) return;
        if ($(this).hasClass("layui-icon-subtraction")) {
            $(this).removeClass("layui-icon-subtraction").addClass("layui-icon-addition");
            $(this).parent().removeClass("expand").addClass("collapse");
        } else {
            $(this).removeClass("layui-icon-addition").addClass("layui-icon-subtraction");
            $(this).parent().removeClass("collapse").addClass("expand");
        }
    });
    form.on("checkbox(permission-checkbox)", function (data) {
        var elem = $(data.elem);
        if (elem.css("cursor").indexOf("not-allowed") != -1) {
            data.elem.checked = !data.elem.checked;
            form.render();
            return;
        }
        var permission = "";
        while (!elem.hasClass("root")) {
            elem = elem.parent();
            if (elem[0].tagName == "LI") {
                var node = elem.attr("name");
                if (node) {
                    if (permission.length > 0) {
                        permission = "/" + permission
                    }
                    permission = node + permission;
                }
            }
        }
        var root_role = elem.attr("role");
        $.ajax({
            url: "/api/system/update/permission",
            data: JSON.stringify({
                permission: permission,
                role: root_role,
                status: data.elem.checked
            }),
            type: "post",
            loading: true,
            dataType: "json",
            success: function (res) {
                if (res.success) { 
                    if (data.elem.checked) {
                        $(data.elem).parent().removeClass("disibled-child");
                    } else {
                        $(data.elem).parent().find("[type='checkbox']").removeAttr("checked"); 
                        $(data.elem).parent().addClass("disibled-child");
                    }
                    form.render();
                } else {
                    data.elem.checked = !data.elem.checked;
                    form.render();
                    return;
                }
            }, error: function () {
                data.elem.checked = !data.elem.checked;
                form.render();
                return;
            }
        });

    });
    exports('permission', module);
});
 