﻿@page
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@{
	ViewData["Title"] = language["PAGE_TITLE_LOGIN"];
	Random rand = new Random(this.GetHashCode());
	var input_suffx = rand.Next(int.MinValue, int.MaxValue).ToString("x8");
}
<page>
	<ui-frame  >
		<form action="javascript:;" name="@input_suffx" autocomplete="off">
			<login>
				<item style="padding-top:8px">
					@language["BOX_TITLE_LOGIN"]
				</item>
				<item>
					<input type="text"
						   placeholder="@language["INPUT_TEXT_USERNAME"]"
						   required 
						pattern="^[A-Za-z0-9]{6,16}$"
						   title="@language["FORMAT_USERNAME"]"
					    name="@input_suffx-u"
						readonly readonly-fill autocomplete="off" />
				</item>
				<item>
					<input type="password"
						   placeholder="@language["INPUT_TEXT_PASSWORD"]"
						   required 
						pattern="^[^\s\u4e00-\u9fa5]{6,16}$"
						   title="@language["FORMAT_PASSWORD"]"
						name="@input_suffx-p"
						readonly readonly-fill autocomplete="off" />
				</item>
				<item class="button-item">
					<button type="submit">@language["BUTTON_ACTION_LOGIN"]</button>
				</item>
			</login>
		</form>
	</ui-frame>
</page>
@section Scripts{

	<script>
		$('[name="@input_suffx"]').submit(function(){
			var username=$("[name='@input_suffx-u']").val();
			var password=$("[name='@input_suffx-p']").val();
			api.login(username,password,function(res){
				if(res.success){
					localStorage.setItem("uid",res.data.uid);
					localStorage.setItem("token",res.data.token);
					location.href="/bridge?target="+res.data.url
				}
			},true,true);
		});
	</script>
}