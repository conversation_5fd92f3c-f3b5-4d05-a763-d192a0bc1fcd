<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.TCPForward"
        tools:targetApi="31" >
        <meta-data android:name="is_vdos_ground_module" android:value="true"/>
        <meta-data android:name="main_define_class_name"     android:value="com.scvdos.vds.ground.module.tcp.Define"/>
    </application>

</manifest>