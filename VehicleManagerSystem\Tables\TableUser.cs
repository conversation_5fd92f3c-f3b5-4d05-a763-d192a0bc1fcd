using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace VehicleManagerSystem.Tables
{
    public enum UserRole
    { 
        [Display(Name ="GENERAL_ALL")]
        None,
        [Display(Name = "USER_ROLE_MONITOR")]        //可以查看飞机状态,但是不能控制
        Monitor,
        [Display(Name = "USER_ROLE_CONTROL")]    //添加第三方管理员 
        Controller,
        [Display(Name = "USER_ROLE_THIRD_PARTY_ADMIN")]    //添加飞机和把飞机授权给飞行控制人员-可以开通监控用户和控制用户
        ThirdPartyAdmin,
        [Display(Name = "USER_ROLE_SUPER_ADMIN")]       //添加第三方管理员 可以开通所有用户
        SuperAdmin
    }

    public enum UserStatus
    {
        [Display(Name ="GENERAL_ALL")]
        None,
        [Display(Name ="USER_STATUS_NORMAL")]
        Normal,
        [Display(Name = "USER_STATUS_DISABLED")]
        Disabled,

    }
    public class TableUser: ITable
    {
        [SqlSugar.SugarColumn(ColumnDescription = "用户ID", IsIdentity = true, IsPrimaryKey =true)]
        public int Id { get; set; }

        [SqlSugar.SugarColumn(ColumnDescription ="用户名",UniqueGroupNameList = ["USER_NAME"])]
        public string UserName { get; set; } = null!;

        [SqlSugar.SugarColumn(ColumnDescription = "密码")]
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string Password { get; set; } = null!;

        [SqlSugar.SugarColumn(ColumnDescription = "显示名称",IsNullable =true)]
        public string NickName { get; set; } = null!;
        [SqlSugar.SugarColumn(ColumnDescription = "用户类型")]
        public UserRole UserRole { get; set; } = UserRole.Monitor;
        [SqlSugar.SugarColumn(ColumnDescription = "用户状态")]
        public UserStatus UserStatus { get; set; } = UserStatus.Normal;

        [SqlSugar.SugarColumn(ColumnDescription = "控制密钥",IsNullable =true)]
        public string Secret { get; set; } =null!;

        [SqlSugar.SugarColumn(ColumnDescription = "父级ID")]
        [JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public int ParentUserId { get; set; } = 0;
    }
}
