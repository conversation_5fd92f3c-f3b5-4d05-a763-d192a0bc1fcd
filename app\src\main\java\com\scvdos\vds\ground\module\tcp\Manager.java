package com.scvdos.vds.ground.module.tcp;

import android.content.Context;
import android.util.Log;
import android.view.ViewGroup;

import com.scvdos.vds.ground.api.messages.vdoslink.commands.command_long_base;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.APM_FLIGHT_MODE;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.GPS_FIX_TYPE;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.MAV_AUTOPILOT;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.MAV_FRAME;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.MAV_MODE_FLAG;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.MAV_SYS_STATUS_SENSOR;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.MISSION_STATE;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_altitude;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_attitude;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_command_long;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_file_transfer_protocol;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_global_position_int;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_gps2_raw;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_gps_raw_int;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_heartbeat;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_home_position;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_ack;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_clear_all;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_count;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_current;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_item_int;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_request;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_mission_request_int;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_param_read;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_param_request_list;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_param_set;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_param_value;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_request_data_stream;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_serial_control;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_servo_output_raw;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_status_text;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_sys_status;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_time_sync;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_vfr_hud;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_mission_request_list;
import com.scvdos.vds.ground.module.groundapi.CommandCallback;
import com.scvdos.vds.ground.module.groundapi.DataManagerApi;
import com.scvdos.vds.ground.module.groundapi.IProxy;
import com.scvdos.vds.ground.module.groundapi.ResultCommandCallback;
import com.scvdos.vds.ground.module.groundapi.link.IManager;
import com.scvdos.vds.ground.module.groundapi.link.IVehicle;
import com.scvdos.vds.ground.module.groundapi.util.ViewCancelHandler;
import com.scvdos.vds.ground.module.groundapi.util.ViewContext;
import com.scvdos.vds.ground.module.tcp.MQTTForward.MQTTClient;
import com.scvdos.vds.ground.module.tcp.TCPForward.ForwardService;

import org.json.JSONObject;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class Manager extends DataManagerApi {
    public final static String TAG="Module-TCPForwardTCPService";
    @Override
    public ViewContext showView(Context context, ViewGroup viewGroup, ViewCancelHandler viewCancelHandler) {
        return null;
    }

    private MQTTClient m_mqtt_client;
    private  IManager m_manager;
    private  IProxy m_proxy;
    private ForwardService m_forward_service=null;
    public IManager getManager(){
        return m_manager;
    }
    public IProxy getProxy(){
        return m_proxy;
    }
    public ForwardService getService(){
        return m_forward_service;
    }
    @Override
    public void viewClosed(ViewContext viewContext) {

    }
    @Override
    public void onCreate(IManager manager, IProxy proxy) {
        m_manager=manager;
        m_proxy=proxy;
        m_forward_service=new ForwardService(this);
        m_forward_service.start();
        m_mqtt_client=new MQTTClient(this,"VDBZY012500TW50000AFB03000001","************",11883,"@VDBZY012500TW50000AFB03000001","ss123456");
        m_mqtt_client.start();
    }


    public boolean CommandTakeOff(int rel_alt,CommandCallback callback){
        Log.d(TAG, "CommandTakeOff:BEGIN "+rel_alt);
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        Log.d(TAG, "CommandTakeOff: "+rel_alt);
        m_manager.takeoff(vehicle, m_proxy, rel_alt, callback);
        return true;
    }
    public boolean CommandLand(CommandCallback callback){
        Log.d(TAG, "CommandLand:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.land(vehicle, m_proxy, callback);
        return true;
    }
    public boolean CommandRTL(CommandCallback callback){
        Log.d(TAG, "CommandRTL:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.rtl(vehicle, m_proxy, callback);
        return true;
    }
    public boolean CommandMissionStart(CommandCallback callback){
        Log.d(TAG, "CommandMissionStart:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.mission_start(vehicle, m_proxy, callback);
        return true;
    }
    public boolean CommandMissionPause(CommandCallback callback){
        Log.d(TAG, "CommandMissionPause:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.mission_pause(vehicle, m_proxy, callback);
        return true;
    }
    public boolean CommandMissionResume(CommandCallback callback){
        Log.d(TAG, "CommandMissionResume:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.mission_resume(vehicle, m_proxy, callback);
        return true;
    }
    public boolean CommandRequestMission(ResultCommandCallback callback){
        Log.d(TAG, "CommandRequestMission:BEGIN");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.request_missions(vehicle, m_proxy,callback);
        return true;
    }
    public boolean CommandAutoMission(byte[] mission,CommandCallback callback){
        Log.d(TAG, "CommandAutoMission:BEGIN");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        Log.d(TAG, "CommandAutoMission:SEND");
        m_manager.auto_mission(vehicle, m_proxy,mission, callback);
        return true;
    }
    public boolean CommandCommandLong(vdoslink_message_command_long command,CommandCallback callback){
        Log.d(TAG, "CommandCommandLong:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.send_message(vehicle,m_proxy,command);
        callback.onComplete((byte) 1,(byte)0);
        return true;
    }

    public boolean CommandGuidedControl(@MAV_FRAME int frame,float x,float y,float z,float vx,float vy,float vz,float yaw,float yaw_rate,CommandCallback callback){
        Log.d(TAG, "CommandGuidedControl:BEGIN ");
        IVehicle vehicle=m_vehicle;
        if(vehicle==null ||!vehicle.is_online()){
            return false;
        }
        m_manager.guided_control(vehicle, m_proxy, MAV_FRAME.MAV_FRAME_BODY_OFFSET_NED, x, y, z, vx, vy, vz, yaw, yaw_rate, callback);
        return true;
    }
    private IVehicle m_vehicle=null;
    private volatile boolean armed=false;
    private volatile double m_latitude=0;
    private  volatile double m_longitude=0;
    private volatile float m_altitude=0;
    private volatile float m_relative_alt=0;
    private volatile int m_gps_fix_type=0;
    private volatile float m_pitch=0;
    private volatile float m_roll=0;
    private volatile float m_yaw=0;
    private volatile long m_onboard_control_sensors_present=0;
    private volatile short autopilot_type=-1;
    private volatile int mission_state;
    private volatile int mission_current;
    private volatile boolean global_position_valid=false;
    private volatile boolean real_altitude_valid=false;


    @Override
    public void onHeartbeat(IVehicle vehicle, vdoslink_message_heartbeat heartbeat) {
        if(m_vehicle==null || !m_vehicle.equals(vehicle)){
            m_vehicle=vehicle;
        }

        autopilot_type=heartbeat.autopilot;
        boolean new_armed= (heartbeat.base_mode & MAV_MODE_FLAG.MAV_MODE_FLAG_SAFETY_ARMED)== MAV_MODE_FLAG.MAV_MODE_FLAG_SAFETY_ARMED;
        if(MAV_AUTOPILOT.MAV_AUTOPILOT_ARDUPILOTMEGA==heartbeat.autopilot){
            if(!vehicle.arming_not_required()  || MAV_SYS_STATUS_SENSOR.MAV_SYS_STATUS_SENSOR_MOTOR_OUTPUTS!=(m_onboard_control_sensors_present & MAV_SYS_STATUS_SENSOR.MAV_SYS_STATUS_SENSOR_MOTOR_OUTPUTS)){
                armed=new_armed;
            }
        }

        var mode=   (m_relative_alt<=0 && !armed)?0:
                    (heartbeat.custom_mode== APM_FLIGHT_MODE.AUTO)?2:
                    (heartbeat.custom_mode== APM_FLIGHT_MODE.LAND)?3:
                    (heartbeat.custom_mode== APM_FLIGHT_MODE.RTL)?4:
                    (heartbeat.custom_mode== APM_FLIGHT_MODE.AUTO_RTL)?4:
                    (heartbeat.custom_mode== APM_FLIGHT_MODE.SMART_RTL)?4:1;//判断起飞中//引导模式并且
        var mission= ((mission_state==MISSION_STATE.MISSION_STATE_NOT_STARTED||
                        mission_state==MISSION_STATE.MISSION_STATE_NO_MISSION)?0:
                        mission_state==MISSION_STATE.MISSION_STATE_ACTIVE?1:
                        mission_state==MISSION_STATE.MISSION_STATE_PAUSED?2:
                        mission_state==MISSION_STATE.MISSION_STATE_COMPLETE?3:0xFF);


        byte[] send_buffer=new byte[27];
        ByteBuffer buffer=ByteBuffer.wrap(send_buffer);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        buffer.putInt((int)((long)((m_latitude+90)*1e7)&0xFFFFFFFFL));
        buffer.putInt((int)((long)((m_longitude+180)*1e7)&0xFFFFFFFFL));
        buffer.putInt((int)(m_altitude*1e3));
        buffer.putInt((int)(m_relative_alt*1e3));
        buffer.putShort((short) (m_roll*10));
        buffer.putShort((short) (m_pitch*10));
        buffer.putShort((short) (m_yaw*10));
        buffer.put((byte) (armed?0:1));
        buffer.put((byte) (mode));
        buffer.put((byte)mission);
        buffer.put((byte)mission_current);
        buffer.put((byte)vehicle.battery_remaining());

        m_forward_service.send_all_message(0x50,send_buffer);

        try {
            JSONObject m_json = new JSONObject();
            m_json.put("lng", m_longitude);
            m_json.put("lat", m_latitude);
            m_json.put("alt", m_altitude);
            m_json.put("relative_alt", m_relative_alt);
            m_json.put("roll", m_roll);
            m_json.put("pitch", m_pitch);
            m_json.put("yaw", m_yaw);
            m_json.put("armed", armed);
            m_json.put("mode", mode);
            m_json.put("mission_state", mission);
            m_json.put("mission_index", mission_current);
            m_json.put("battery", vehicle.battery_remaining());
            m_json.put("rssi", vehicle.delay());
            m_json.put("model", vehicle.model());
            m_json.put("maker", vehicle.maker());
            m_json.put("type", vehicle.frameType());
            m_json.put("home_lat",home_position_lat);
            m_json.put("home_lng", home_position_lng);
            m_json.put("home_alt",home_position_alt);
            m_json.put("ready",vehicle.ready());
            m_json.put("mission_version",vehicle.missionVersion());
            m_json.put("time", System.currentTimeMillis());

            //摄像头类
            //支持变焦
            //支持广角
            //支持红外
            //支持缩放
            //支持云台

            m_mqtt_client.publish_heartbeat(m_json.toString());
        }catch (Exception ignored){

        }


        //每次心跳应 推送数据 [电池剩余电量],姿态信息[滚转俯仰偏航],位置信息[经纬度,海拔,相对高度],解锁状态[已解锁,已锁定],飞行状态[悬停,自动任务],任务状态[未执行,暂停中,执行中]
    }
    @Override
    public void onFileTransferProtocol(IVehicle vehicle, vdoslink_message_file_transfer_protocol protocol) {
       // Log.d(TAG, "onFileTransferProtocol: ");
    }
    @Override
    public void onAttitude(IVehicle vehicle, vdoslink_message_attitude attitude) {

        m_yaw=attitude.yaw;
        m_roll=attitude.roll;
        m_pitch=attitude.pitch;

       // Log.d(TAG, "onAttitude: "+attitude.yaw+","+attitude.pitch+","+attitude.roll);
    }
    public static boolean valid(double lat,double lng){
        if(lat==0 || lng==0)return false;
        if(lat>85.0511 ||lat<-85.0511 )return false;
        return (lng < 180 && lng > -180);
    }
    @Override
    public void onGlobalPosition(IVehicle vehicle, vdoslink_message_global_position_int position) {



        if(!real_altitude_valid){
            m_relative_alt=position.relative_alt*1e-3f;
            m_altitude=position.alt*1e-3f;
            Log.d(TAG, "onGlobalPosition real: "+m_altitude+","+m_relative_alt);
        }
        if(!valid(position.lat, position.lon)){
            return;
        }
        Log.d(TAG, "onGlobalPosition realx: "+m_altitude+","+m_relative_alt);
        global_position_valid=true;
        Log.d(TAG, "onGlobalPosition: "+position.lat +","+position.lon);
        m_latitude=position.lat *1e-7;
        m_longitude= position.lon *1e-7;
    }
    @Override
    public void onVfrHud(IVehicle vehicle, vdoslink_message_vfr_hud vfr_hud) {
       // Log.d(TAG, "onVfrHud: ");
    }
    @Override
    public void onGps2Raw(IVehicle vehicle, vdoslink_message_gps2_raw gps2) {
    //    Log.d(TAG, "onGps2Raw: ");
    }
    @Override
    public void onGpsRaw(IVehicle vehicle, vdoslink_message_gps_raw_int gps) {
        if(!global_position_valid && gps.fix_type>= GPS_FIX_TYPE.GPS_FIX_TYPE_3D_FIX){

            m_latitude=gps.lat*1e-7;
            m_longitude=gps.lon*1e-7;

            if(!real_altitude_valid){
                m_altitude =gps.alt*1e-3f;
            }
            Log.d(TAG, "onGpsRaw: "+m_altitude+","+m_relative_alt);
        }
        m_gps_fix_type= gps.fix_type;
    }
    @Override
    public void onSysStatus(IVehicle vehicle, vdoslink_message_sys_status status) {
        m_onboard_control_sensors_present=status.onboard_control_sensors_present;
        if(MAV_AUTOPILOT.MAV_AUTOPILOT_ARDUPILOTMEGA ==autopilot_type   && vehicle.arming_not_required()){
            armed=(status.onboard_control_sensors_enabled & MAV_SYS_STATUS_SENSOR.MAV_SYS_STATUS_SENSOR_MOTOR_OUTPUTS)==MAV_SYS_STATUS_SENSOR.MAV_SYS_STATUS_SENSOR_MOTOR_OUTPUTS;
        }
    }
    @Override
    public void onServoOutput(IVehicle vehicle, vdoslink_message_servo_output_raw servo) {
       // Log.d(TAG, "onServoOutput: ");
    }
    @Override
    public void onAltitude(IVehicle vehicle, vdoslink_message_altitude altitude) {
        real_altitude_valid=true;

        Log.d(TAG, "onAltitude: "+altitude.altitude_relative+","+altitude.altitude_amsl);
        m_relative_alt=altitude.altitude_relative;
        m_altitude=altitude.altitude_amsl;
    }
    @Override
    public void onStatusText(IVehicle vehicle, vdoslink_message_status_text status_text) {
        //Log.d(TAG, "onStatusText: ");
    }
    private volatile double home_position_lat=0;
    private volatile double home_position_lng=0;
    private volatile double home_position_alt=0;

    @Override
    public void onHomePosition(IVehicle vehicle, vdoslink_message_home_position position) {
        home_position_lat=  position.latitude * 1e-7;
        home_position_lng=  position.longitude*1e-7;
        home_position_alt= position.altitude*1e-3f;
        //Log.d(TAG, "onHomePosition: ");
    }
    @Override
    public void onTimeSync(IVehicle vehicle, vdoslink_message_time_sync timeSync) {
        //Log.d(TAG, "onTimeSync: ");
    }
    @Override
    public void onRequestDataStream(IVehicle vehicle, vdoslink_message_request_data_stream stream) {
        //Log.d(TAG, "onRequestDataStream: ");
    }
    @Override
    public void onCommandLong(IVehicle vehicle, vdoslink_message_command_long command) {
        //Log.d(TAG, "onCommandLong: ");
    }
    @Override
    public void onParamSet(IVehicle vehicle, vdoslink_message_param_set param_set) {
        //Log.d(TAG, "onParamSet: ");
    }
    @Override
    public void onParamRead(IVehicle vehicle, vdoslink_message_param_read param_read) {
        //Log.d(TAG, "onParamRead: ");
    }
    @Override
    public void onParamValue(IVehicle vehicle, vdoslink_message_param_value param_value) {
        //Log.d(TAG, "onParamValue: ");
    }
    @Override
    public void onMissionRequestList(IVehicle vehicle, vdoslink_mission_request_list request) {
        //Log.d(TAG, "onMissionRequestList: ");
    }
    @Override
    public void onMissionCount(IVehicle vehicle, vdoslink_message_mission_count count) {
        //Log.d(TAG, "onMissionCount: ");
    }
    @Override
    public void onMissionRequestInt(IVehicle vehicle, vdoslink_message_mission_request_int request) {
        //Log.d(TAG, "onMissionRequestInt: ");
    }
    @Override
    public void onMissionRequest(IVehicle vehicle, vdoslink_message_mission_request request) {
        //Log.d(TAG, "onMissionRequest: ");
    }
    @Override
    public void onMissionAck(IVehicle vehicle, vdoslink_message_mission_ack ack) {
       // Log.d(TAG, "onMissionAck: ");
    }
    @Override
    public void onMissionCurrent(IVehicle vehicle, vdoslink_message_mission_current current) {
        mission_state=current.mission_state;
        mission_current=current.seq;
    }
    @Override
    public void onMissionItemInt(IVehicle vehicle, vdoslink_message_mission_item_int item) {
       // Log.d(TAG, "onMissionItemInt: ");
    }
    @Override
    public void onParamRequestList(IVehicle vehicle, vdoslink_message_param_request_list request) {
       // Log.d(TAG, "onParamRequestList: ");
    }
    @Override
    public void onMissionClearAll(IVehicle vehicle, vdoslink_message_mission_clear_all clear) {
        //Log.d(TAG, "onMissionClearAll: ");
    }
    @Override
    public void onSerialControl(IVehicle vehicle, vdoslink_message_serial_control serial) {
        //Log.d(TAG, "onSerialControl: ");
    }



}

