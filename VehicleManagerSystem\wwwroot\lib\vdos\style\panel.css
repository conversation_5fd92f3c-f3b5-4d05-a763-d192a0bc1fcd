﻿.vui-status-island {
    position: relative;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    display: flex;
    font-family: weilai;
  
}
.vui-status-island:before {
    content: "";
    width: 100%;
    height: 100%;
    background: #02d6fb;
    position: absolute;
    z-index: 1;
    clip-path: polygon(calc(50% + 1px) -1px,100% -1px, 100% calc(100% - 26px),calc(100% - 26px) 100%, /*右下*/
    calc(100% - 80px) 100%,calc(100% - 106px) calc(100% - 26px) /*右下*/, 106px calc(100% - 26px),80px 100%,26px 100%,0 calc(100% - 26px), /*左下*/
    0 -1px,calc(50% - 1px) -1px,calc(50% - 1px) 0px,1px 0px, 1px calc(100% - 27px),14px calc(100% - 14px),92px calc(100% - 14px),105px calc(100% - 27px),
    calc(50% - 50px) calc(100% - 27px),calc(50% - 34px) calc(100% - 43px), 
    calc(50% + 34px) calc(100% - 43px),calc(50% + 50px) calc(100% - 27px),calc(100% - 105.5px) calc(100% - 27px),calc(100% - 92px) calc(100% - 14px),calc(100% - 14px) calc(100% - 14px),calc(100% - 1px) calc(100% - 27px),calc(100% - 1px) 0px,calc(50% + 1px) 0px);
}

.vui-status-island:after {
    content: "";
    bottom: 0;
    width: 100%;
    position: absolute;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.4));
    clip-path: polygon(calc(50% + 1px) -1px,100% -1px, 100% calc(100% - 26px),calc(100% - 26px) 100%, /*右下*/
    calc(100% - 80px) 100%,calc(100% - 106px) calc(100% - 26px) /*右下*/, 106px calc(100% - 26px),80px 100%,26px 100%,0 calc(100% - 26px), /*左下*/
    0 -1px);
    z-index: 0;
}
 
.vui-status-island > .vui-status-island-progress {
    width: calc(100% - 210px);
    position: absolute;
    bottom: 0px;
    height: 13px;
    background: #02d6fb33;
    left: 105px;
    clip-path: polygon(13px 0, calc(100% - 13px) 0,100% 100%, 0px 100% );
}

.vui-status-island-progress-value {
    background: #02d6fb;
    height: 100%;
}
.vui-status-island > .vui-flight-speed {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #EEE;
    height: 65px;
    font-size: 32px;
    width: 106px;
    flex-direction: column;
    padding: 6px;
    box-sizing: border-box;
}


.vui-status-island > .vui-flight-speed:before {
    content: "速度(m/s)";
    font-size: 10px;
    width: 100%;
}
.vui-status-island > .vui-flight-height {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #EEE;
    height: 65px;
    font-size: 32px;
    width: 106px;
    flex-direction: column;
    padding: 6px;
    box-sizing: border-box;
}


.vui-status-island > .vui-flight-height:before {
    content: "高度(m)";
    font-size: 10px;
    width: 100%;
    text-align: right;
}
.vui-status-island > .vui-flight-content {
    flex: 1;
    height: 54px;
    display: flex;
    flex-direction: column;
    z-index: 2;
}
    .vui-status-island > .vui-flight-content > .vui-flight-content-top {
        background: rgba(2,214,251,0.2);
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #02d6fb;
        clip-path: polygon(25% 0px,75% 0px,75% calc(100% - 6px),calc(75% - 6px) 100%,calc(25% + 6px) 100%,25% calc(100% - 6px));
    }
    .vui-status-island > .vui-flight-content > .vui-flight-content-fill {
        flex: 1;
        margin-top: 8px;
        margin-bottom: 8px;
      
        height: 1px;
        opacity:0.4;
    }

.vui-status-island > .vui-flight-content > .vui-flight-content-bottom {
    height: 17px;
    display:flex;
}
.vui-status-island .veh-lat {
    flex: 1;
    display: flex;
    justify-content: end;
    color: #EEE;
}
.vui-status-island .veh-mode {
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
    font-size:10px;
}
.vui-status-island .veh-lon {
    flex: 1;
    display: flex;
    justify-content: start;
    color: #EEE;
}
.vui-vehicle {
    height: 90px;
    width: 200px;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr; /* 创建两列，每列占据可用空间的一半 */
    gap: 4px;
    padding: 1px 10px 0px 1px;
    box-sizing: border-box;
    grid-template-rows:1fr;
}

.vui-vehicle:before {
    content: "";
    width: 100%;
    height: 100%;
    background: #02d6fb;
    opacity:0.8;
    position: absolute;
    z-index: 1;
    clip-path: polygon(0 20px,20px 0,calc(100% - 9px) 0,100% 9px,100% calc(100% - 18px - 9px),calc(100% - 9px) calc(100% - 18px),50% calc(100% - 18px),calc(50% - 18px) 100%,0 100%, 0 20px,1px 20px,1px calc(100% - 9.5px),calc(50% - 10px) calc(100% - 9.5px),calc(50% - 0.5px) calc(100% - 19px),calc(100% - 9.5px) calc(100% - 19px),calc(100% - 9.5px) 1px,20px 1px,1px 20px);
}

.vui-vehicle:after {
    content: "";
    bottom: 0;
    left: 0;
    width: 100%;
    position: absolute;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.4));
    clip-path: polygon(0 20px,20px 0,calc(100% - 9px) 0,100% 9px,100% calc(100% - 18px - 9px),calc(100% - 9px) calc(100% - 18px),50% calc(100% - 18px),calc(50% - 18px) 100%,0 100%);
    z-index: 0;
}

.vui-vehicle > .status {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 17px;
    width: calc(50% + 16px);
    background: #02d6fb;
    opacity:0.6;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.8em;
    font-weight: 400;
    color: #333;
    clip-path: polygon(0 17px, 17px 0, calc(100% - 10px) 0, calc(100% - 10px - 17px) 17px);
}
.vui-vehicle > .vehicle-control {
    padding: 1px 1px 10px 1px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    z-index: 1;
}
.vui-vehicle > .vehicle-control > .veh-att{
    color:#02d6fb;
    display:flex;
   
}
.vui-vehicle > .vehicle-control > .veh-att > .veh-handing-box {
    display: flex;
    justify-content: end;
    align-items: end;
    flex: 1;
    padding:2px;
}
.vui-vehicle > .vehicle-control > .veh-att > .veh-handing-box > .veh-handing-bor {
    width: 20px;
    height: 20px;
    position: relative;
    display: flex;
    justify-content: stretch;
    align-items: stretch;
    border: 1px solid transparent;
    box-sizing: border-box;
}
    .vui-vehicle > .vehicle-control > .veh-att > .veh-handing-box > .veh-handing-bor:before {
        content: "";
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        border: 1px solid #1e9fff;
        box-sizing: border-box;
        clip-path: polygon(40% -1px,40% 40%,60% 40%,60% -1px,101% -1px,101px 40%,60% 40%,60% 60%,101% 60%,101% 101%,60% 101%,60% 60%,40% 60%,40% 101%,-1px 101%,-1px 60%,40% 60%,40% 40%,-1px 40%,-1px -1px);
    }
.vui-vehicle > .vehicle-control > .veh-att > .veh-handing-box > .veh-handing-bor > .veh-handing {
    background: #e6e6cF;
    flex: 1;
    clip-path: polygon(50% 15%,25% 75%,50% 50%,75% 75%);
}
.vui-vehicle > .vehicle-control > .veh-att > .veh-local {
    height: 32px;
  
    padding:2px;
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    grid-template-columns: 1fr;
    font-size:8px;
}
.vui-vehicle > .vehicle-control > .veh-att > .veh-local>*{
    display:flex;
    align-items:center;
    justify-content:start;
    border-bottom:1px solid rgba(222,222,222,0.4);
    word-break:keep-all;

}
.vui-vehicle > .vehicle-control > .veh-att > .veh-local > .veh-lat {
}
.vui-vehicle > .vehicle-control > .veh-user {
   
    flex: 1;
    display:flex;


}
.vui-vehicle > .vehicle-control > .veh-user > .veh-avatar-box {
    width: 32px;
   
    padding:2px;
    display:flex;
    justify-content:stretch;
    align-items:stretch;
}
    .vui-vehicle > .vehicle-control > .veh-user > .veh-avatar-box > .veh-avatar {
        padding: 2px;
        flex: 1;
        border: 1px solid #02d6fb;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top:2px;
    }
    .vui-vehicle > .vehicle-control > .veh-user > .veh-avatar-box > .veh-avatar:before {
        font-family: "iconfont" !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        content: "\e72a";
        color: #02d6fb;
        font-size:24px;
    }
.vui-vehicle > .vehicle-control > .veh-user > .veh-detail {
    word-break: keep-all;
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    grid-template-columns: 1fr;
    font-size: 8px;
    padding: 2px;
    color: #02d6fb;
    flex: 1;
    clip-path: polygon(-1px -1px,101% -1px,101% calc(100% - 8px),calc(100% - 8px) 101%,-1px 101%);
}
.vui-vehicle > .vehicle-control > .veh-user > .veh-detail > * {
    display: flex;
    align-items: center;
    justify-content: start;
    border-bottom: 1px solid rgba(222,222,222,0.4);
    word-break: keep-all;
    width: 100%;
}
.vui-vehicle > .vehicle-control > .veh-user > .veh-detail > .veh-user-name {
}
.vui-vehicle > .vehicle-control > .veh-user > .veh-detail > .veh-user-no {
}
.vui-vehicle > .vehicle-control > .veh-user > .veh-detail > .veh-user-info {
}
.vui-vehicle > .vehicle-media {
    height: calc(100% - 18px);
    display: flex;
    flex-direction: column;
    gap: 4px;
    box-sizing: border-box;
    padding: 0 0 2px 0;
    z-index: 1;
}
.vui-vehicle > .vehicle-media > .infos {
       
    box-sizing: content-box;
    display: flex;
    justify-content: right;
    align-items: center;
    padding: 2px 4px;
    gap: 4px;
}
  
.vui-vehicle > .vehicle-media > .media-bg {
        flex: 1;
        box-sizing: border-box;
        padding:2px;
        display:flex;
        justify-content:stretch;
        align-items:stretch;
}
    .vui-vehicle > .vehicle-media > .media-bg > .media-video {
        background: #000;
        border-radius: 2px;
        flex: 1;
        display: flex;
        position: relative;
        justify-content: center;
        align-items: center;
        pointer-events: all;
    }
.vui-vehicle > .vehicle-media > .media-bg > .media-video:before {
    content: "无信号";
    color: #eee;
    font-size: 10px;
    width: 100%;
    height: 100%;
    position: absolute;
    justify-content: center;
    align-items: center;
    display: flex;
}
.vui-vehicle > .vehicle-media > .media-bg > .media-video:hover:before{
    content:"";
}
        .vui-vehicle > .vehicle-media > .media-bg > .media-video:hover::after {
            content: "";
            width: 20%;
            height: 20%;
            border: 2px solid #eee;
            pointer-events: all;
            cursor:pointer;
            clip-path: polygon(40% -1px,40% 40%,60% 40%,60% -1px,101% -1px,101px 40%,60% 40%,60% 60%,101% 60%,101% 101%,60% 101%,60% 60%,40% 60%,40% 101%,-1px 101%,-1px 60%,40% 60%,40% 40%,-1px 40%,-1px -1px);
        }
 .vui-vehicle > .vehicle-media > .infos > .veh-battery {
        background: #02d6fbaa;
        width: 16px;
        height: 8px;
        position: relative;
        clip-path: polygon(0 0,calc(100% - 2px) 0px,calc(100% - 2px) 25%,100% 25%,100% 75%,calc(100% - 2px) 75%,calc(100% - 2px) 100%,0 100%);
    }
.vui-vehicle > .vehicle-media > .infos > .veh-battery:before {
    content: "";
    position: absolute;
    width: 50%;
    background: #02d6fb;
    height: 100%;
    left: 0;
    top: 0;
}
.vui-vehicle > .vehicle-media > .infos > .veh-no {
    color: #fff;
    font-size: 8px;
}

.vui-vehicle > .vehicle-media > .infos > .veh-rssi {
    background: #02d6fb33;
    width: 16px;
    height: 8px;
    clip-path: polygon(0 100%,100% 0, 100% 100%);
    position: relative;
}
.vui-vehicle > .vehicle-media > .infos > .veh-rssi:before {
    content:"";
    position: absolute;
    width: 50%;
    background: #02d6fb;
    height: 100%;
    left:0;
    top:0;
}

.vui-hexagon.checked {
        color: #333;
    }
.vui-hexagon {
    height: 60px;
    width: 60px;
    position: relative;
    pointer-events: all;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #62F6fb;
    font-size: 28px;
    clip-path: polygon(0 50%, 25% 6.7%, 75% 6.7%, 100% 50%, 75% 93.3%, 25% 93.3%);
}
.vui-hexagon > * {
    z-index: 1;
}
.vui-hexagon:before {
        content: "";
        left: 0;
        top: 0;
        position: absolute;
        height: 100%;
        width: 100%;
        background: #02d6fb;
        /* clip-path: polygon(0 43.3%, 25% 0, 75% 0, 100% 43.3%, 75% 86.6%, 25% 86.6%, 0 43.3%, 2px 43.3%, calc(25% + 1px) calc(86.6% - 2px), calc(75% - 1px) calc(86.6% - 2px), calc(100% - 2px) 43.3%, calc(75% - 1px) 2px, calc(25% + 1px) 2px, 2px 43.3%);
       */
        clip-path: polygon(0 50%, 25% 6.7%, 75% 6.7%, 100% 50%, 75% 93.3%, 25% 93.3%, 0 50%, 2px 50%, calc(25% + 1px) calc(93.3% - 2px), calc(75% - 1px) calc(93.3% - 2px), calc(100% - 2px) 50%, calc(75% - 1px) calc(6.7% + 2px), calc(25% + 1px) calc(6.7% + 2px), 2px 50%);
        z-index: 1;
    }

.vui-hexagon:after {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.4));
        clip-path: polygon(0 50%, 25% 6.7%, 75% 6.7%, 100% 50%, 75% 93.3%, 25% 93.3%);
        z-index: 0;
    }

.vui-hexagon:hover:after {
    animation: hexagon-active 0.1s linear;
}
@keyframes hexagon-active {
    0% {
        transform: scale(1.4);
        background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.4));
    }

    100% {
        transform: scale(1);
        background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.8));
    }
}
.vui-hexagon:hover:after {
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.8));
}

.vui-hexagon.checked:after {
    background: #02d6fbaa !important;
    scale: 1 !important;
}
.vui-hexagon:active > * {
    scale: 0.8;
}


.vui-alert-shade {
    left:0;
    top:0;
    position: fixed;
    background: #333333ef;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

@keyframes vui-alert-show {
    0% {
        width: 4px;
    }

    100% {
        width: var(--width);
    }
}

@keyframes vui-alert-hide {
 

    100% {
        width: 4px;
    }
}
@keyframes vui-alert-show-content {
    0% {
        opacity: 0;
        display: none;
    }

    99% {
        opacity: 0;
        display: flex;
    }

    100% {
        opacity: 1;
    }
}

.vui-alert {
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(2,214,251,0.3));
    padding: 15px;
    position: relative;
    height: 80px;
    display: none;
    --width: 220px;
    width: var(--width);
    color: #eee;
    display: flex;
    flex-direction: column;
    border-bottom: 2px solid #02d6fb88;
    overflow: hidden;
}

.vui-alert-show > .vui-alert {
    display: flex;
    animation: vui-alert-show 0.2s linear;
}
.vui-alert-hidden .vui-alert {
    animation: vui-alert-hide 0.2s linear;
}
.vui-alert-hidden > .vui-alert > * {
    display: none;
}
.vui-alert:before {
    content: "";
    height: 100%;
    width: 2px;
    position: absolute;
    left: 0;
    top: 0;
    background: radial-gradient(circle, rgba(2,214,251,0.9) 0%, rgba(0,0,0,0) 100%);
}

.vui-alert:after {
    content: "";
    height: 100%;
    width: 2px;
    position: absolute;
    right: 0;
    top: 0;
    background: radial-gradient(circle, rgba(2,214,251,0.9) 0%, rgba(0,0,0,0) 100%);
}

.vui-alert > .vui-alert-title {
    user-select: none;
    font-size: 1.2em;
    color: #AAA;
}


.vui-alert > .vui-alert-content {
    flex: 1;
    display: none;
    align-items: center;
    justify-content: center;
}


.vui-alert-show > .vui-alert > * {
    display: flex;
    animation: vui-alert-show-content 0.2s linear;
}

.vui-alert-button {
    display: flex;
    justify-content: end;
    user-select: none;
}

.vui-alert-button-text {
    cursor: pointer;
}

.vui-alert-button-text:hover {
    color: #02d6fb;
}

.vui-alert-button-text:active {
    scale: 0.9;
    user-select: none;
}

[vui-label] {
    display: flex;
    padding: 6px 32px;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

[vui-label]:before {
    content: attr(vui-label);
    color: #fff;
    padding: 0px 8px;
}
[vui-label] > .vui-fake-input,
[vui-label] > input {
    background: transparent;
    border: 1px solid #02d6fb;
    color: #fff;
    padding: 4px 6px;
    flex: 1;
 
}
    [vui-label] > .vui-fake-input {
        text-align: center;
        background: #02d6fb33;
    }

    [vui-label] > select {
        background: transparent;
        border: 1px solid #02d6fb;
        color: #fff;
        padding: 2px 6px;
        flex: 1;
    }
.vui-dialog-shade {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}

.vui-dialog-general {
    min-width: 360px;
}

.vui-box-form {
    position: relative;
}

.vui-box-form-content {
    padding: 22px 28px 22px 12px;
    z-index: 1;
    min-height: 105px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.vui-box-form:before {
    content: "";
    background: #02d6fb;
    clip-path: polygon(
    /*外框*/ 10px 50%,10px calc(100% - 10px),100px calc(100% - 10px),110px 100%,calc(100% - 20px) 100%,100% calc(100% - 20px),100% calc(100% - 100px),calc(100% - 20px) calc(100% - 120px),calc(100% - 20px) 20px,calc(100% - 40px) 0px,calc(100% - 100px) 0px,calc(100% - 110px) 10px,30px 10px,10px 30px,10px 50%,
    /*内框*/ 11px 50%,11px 30.5px,30.5px 11px, calc(100% - 30.5px) 11px,calc(100% - 21px) 20.5px,calc(100% - 21px) calc(100% - 119.5px),calc(100% - 1px) calc(100% - 99.5px),calc(100% - 1px) calc(100% - 20.5px),calc(100% - 11px) calc(100% - 10.5px),11px calc(100% - 11px),11px 50% );
    position: absolute;
    width: 100%;
    height: 100%;
}

.vui-box-form:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #222222EE;
    clip-path: polygon(
    /*外框*/ 10px 50%,10px calc(100% - 10px),100px calc(100% - 10px),110px 100%,calc(100% - 20px) 100%,100% calc(100% - 20px),100% calc(100% - 100px),calc(100% - 20px) calc(100% - 120px),calc(100% - 20px) 20px,calc(100% - 40px) 0px,calc(100% - 100px) 0px,calc(100% - 110px) 10px,30px 10px,10px 30px);
    z-index: -1;
}
.vui-box-form-content input {
    background: rgba(2, 64, 101, 0.9);
}
.vui-box-form > .close {
    position: absolute;
    width: 40px;
    height: 20px;
    right: 0;
    top: 0;
    clip-path: polygon(40px 10px, 40px 20px, 22px 20px, 2px 0px, 40px 0px);
}

    .vui-box-form > .close:active {
        scale: 0.9;
        background: #f20000aa;
    }

    .vui-box-form > .close:before {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        background: #02d6fb;
        clip-path: polygon(40px 10px,40px 20px,22px 20px,2px 0px,40px 0px,40px 10px,39px 10px,39px 1px,4.5px 1px,22.5px 19px,39px 19px,39px 10px);
    }

    .vui-box-form > .close:after {
        content: "x";
        position: absolute;
        width: 100%;
        height: 100%;
        padding: 0px 0px 0px 20px;
        background: #02d6fb22;
        clip-path: polygon(40px 10px, 40px 20px, 22px 20px, 2px 0px, 40px 0px);
        color: #fff;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
    }

.vui-box-form > .confirm {
    position: absolute;
    width: 40px;
    height: 120px;
    right: 0;
    bottom: 0;
    background: transparent;
    border: none;
    clip-path: polygon(calc(100% - 2px) calc(100% - 50px), calc(100% - 2px) calc(100% - 99px), calc(100% - 22px) calc(100% - 119px), calc(100% - 42px) calc(100% - 97px), calc(100% - 40px) calc(100% - 12px),calc(100% - 11px) calc(100% - 12px),calc(100% - 2px) calc(100% - 21px),calc(100% - 2px) calc(100% - 50px));
}

    .vui-box-form > .confirm:before {
        content: "";
        padding: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        width: 100%;
        height: 100%;
        top:0;
        position: absolute;
        background: #02d6fb;
        box-sizing: border-box;
        clip-path: polygon(calc(100% - 2px) calc(100% - 50px), calc(100% - 2px) calc(100% - 99px), calc(100% - 22px) calc(100% - 119px), calc(100% - 42px) calc(100% - 97px), calc(100% - 40px) calc(100% - 12px),calc(100% - 11px) calc(100% - 12px),calc(100% - 2px) calc(100% - 21px),calc(100% - 2px) calc(100% - 50px),
        /*内*/ calc(100% - 3px) calc(100% - 50px),calc(100% - 3px) calc(100% - 21.5px),calc(100% - 11.5px) calc(100% - 13px),calc(100% - 39px) calc(100% - 13px),calc(100% - 39px) calc(100% - 98.5px),calc(100% - 22px) calc(100% - 117.5px),calc(100% - 3px) calc(100% - 98.5px),calc(100% - 3px) calc(100% - 50px));
        width: 100%;
    }

    .vui-box-form > .confirm:after {
        content: attr(data-text);
        padding: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        width: 100%;
        height: 100%;
        position: absolute;
        background: #02d6fb22;
        box-sizing: border-box;
        width: 100%;
        right: 0;
        bottom: 0;
        writing-mode: vertical-lr;
    }

.vui-box-form > .confirm:active {
    scale: 0.9;
}

.vui-box-form > .end {
    width: 19px;
    height: calc(100% - 124px);
    top: 22px;
    right: 0;
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(100% 50%,100% calc(100% - 36px),10px calc(100% - 45px),10px calc(100% - 36px),100% calc(100% - 27px), 100% calc(100% - 18px),10px calc(100% - 27px),10px calc(100% - 18px),100% calc(100% - 9px), 100% 100%,10px calc(100% - 9px),10px 0px,11px 0,100% 0px );
}

.vui-box-form > .left {
    width: 100%;
    height: 100px;
    top: 0px;
    left: 0;
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(0 60px,1px 60px,1px 1px,calc(100% - 130px) 1px,calc(100% - 129px) 0px,0px 0px)
}

.vui-box-form > .bottom {
    width: 100%;
    height: 100px;
    bottom: 0px;
    left: 0;
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(0 calc(100% - 60px),0px 100%,80px 100%,79px calc(100% - 1px),1px calc(100% - 1px),1px calc(100% - 60px));
}
.vui-add {
    border: 1px solid #02d6fb;
    padding: 3px 4px;
    box-sizing: border-box;
    height: 23px;
    color:#02d6fb;
    cursor:pointer;
}
.vui-add:active{
    scale:0.9;
}
.vui-sub {
    border: 1px solid #02d6fb;
    padding: 3px 8px;
    box-sizing: border-box;
    height: 23px;
    color: #02d6fb;
    cursor: pointer;
    background: #02d6fb44;
}

.vui-sub:active {
    scale: 0.9;
}
