﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>飞行管理系统</value>
  </data>
  <data name="ERROR_UNKNOW" xml:space="preserve">
    <value>未知错误</value>
  </data>
  <data name="ERROR_LOGIN_DISABLED" xml:space="preserve">
    <value>用户已被禁止登录</value>
  </data>
  <data name="ERROR_USERNAME_OR_PASSWORD" xml:space="preserve">
    <value>用户名或密码错误</value>
  </data>
  <data name="FORMAT_PASSWORD" xml:space="preserve">
    <value>密码长度需要6-16位</value>
  </data>
  <data name="BUTTON_ACTION_LOGIN" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="INPUT_TEXT_PASSWORD" xml:space="preserve">
    <value>请输入密码</value>
  </data>
  <data name="INPUT_TEXT_USERNAME" xml:space="preserve">
    <value>请输入用户名</value>
  </data>
  <data name="BOX_TITLE_LOGIN" xml:space="preserve">
    <value>请登录</value>
  </data>
  <data name="FORMAT_USERNAME" xml:space="preserve">
    <value>用户名应仅包含(6-16个)字母和数字</value>
  </data>
  <data name="JS_ALERT_TITLE_WARNING" xml:space="preserve">
    <value>警告</value>
  </data>
  <data name="JS_ALERT_TITLE_ERROR" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="UI_LOADING" xml:space="preserve">
    <value>加载中</value>
  </data>
  <data name="ERROR_PERMISSION_ERROR" xml:space="preserve">
    <value>无操作权限</value>
  </data>
  <data name="ERROR_PAGE_NOT_FOUND" xml:space="preserve">
    <value>找不到页面</value>
  </data>
  <data name="ERROR_SERVER_ERROR" xml:space="preserve">
    <value>服务器错误</value>
  </data>
  <data name="PAGE_TITLE_LOGIN" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="PAGE_TITLE_ADMIN_INDEX" xml:space="preserve">
    <value>管理中心</value>
  </data>
  <data name="INDEX_MENU_VEHICLE_MANAGER" xml:space="preserve">
    <value>飞机管理</value>
  </data>
  <data name="INDEX_MENU_HANGAR_MANAGER" xml:space="preserve">
    <value>机库管理</value>
  </data>
  <data name="INDEX_MENU_BASE_STATION_MANAGER" xml:space="preserve">
    <value>基站管理</value>
  </data>
  <data name="INDEX_MENU_USER_MANAGER" xml:space="preserve">
    <value>成员管理</value>
  </data>
  <data name="INDEX_MENU_MISSION_MANAGER" xml:space="preserve">
    <value>任务管理</value>
  </data>
  <data name="INDEX_MENU_FLIGHT_LOGGER" xml:space="preserve">
    <value>飞行记录</value>
  </data>
  <data name="LANGUAGE_NAME" xml:space="preserve">
    <value>简体中文</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_SERIAL" xml:space="preserve">
    <value>输入飞机序列号</value>
  </data>
  <data name="BUTTON_TEXT_SEARCH" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="INPUT_TEXT_SEARCH" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="BUTTON_TEXT_ADD" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="BUTTON_CONTROL" xml:space="preserve">
    <value>控制台</value>
  </data>
  <data name="VEHICLE_STATUS_OFFLINE" xml:space="preserve">
    <value>离线</value>
  </data>
  <data name="VEHICLE_STATUS_ONLINE" xml:space="preserve">
    <value>在线</value>
  </data>
  <data name="VEHICLE_STATUS_UNKNOW" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="GENERAL_ALL" xml:space="preserve">
    <value>全部</value>
  </data>
  <data name="VEHICLE_TYPE_HELI" xml:space="preserve">
    <value>直升机</value>
  </data>
  <data name="VEHICLE_TYPE_QUAD" xml:space="preserve">
    <value>四轴四桨</value>
  </data>
  <data name="VEHICLE_TYPE_HEXA" xml:space="preserve">
    <value>六轴六桨</value>
  </data>
  <data name="VEHICLE_TYPE_QUAD_X2" xml:space="preserve">
    <value>四轴八桨</value>
  </data>
  <data name="VEHICLE_TYPE_HEXA_X2" xml:space="preserve">
    <value>六轴十二桨</value>
  </data>
  <data name="VEHICLE_TYPE_OTHER" xml:space="preserve">
    <value>其它</value>
  </data>
  <data name="VEHICLE_TYPE_UNKNOW" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="LABEL_VEHICLE_SERIAL" xml:space="preserve">
    <value>飞机编号</value>
  </data>
  <data name="LABEL_VEHICLE_NAME" xml:space="preserve">
    <value>飞机名称</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_DISPLAY_NAME" xml:space="preserve">
    <value>输入飞机名称</value>
  </data>
  <data name="BUTTON_CONFIRM" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="LABEL_VEHICLE_TYPE" xml:space="preserve">
    <value>飞机类型</value>
  </data>
  <data name="LABEL_VEHICLE_MODEL" xml:space="preserve">
    <value>飞机型号</value>
  </data>
  <data name="LABEL_VEHICLE_MANUFACTURER" xml:space="preserve">
    <value>制造商</value>
  </data>
  <data name="LABEL_VEHICLE_STATUS" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="LABEL_VEHICLE_BATTERY" xml:space="preserve">
    <value>电量</value>
  </data>
  <data name="LABEL_VEHICLE_RSSI" xml:space="preserve">
    <value>信号延迟</value>
  </data>
  <data name="LABEL_VEHICLE_LONGITUDE" xml:space="preserve">
    <value>经度</value>
  </data>
  <data name="LABEL_VEHICLE_LATITUDE" xml:space="preserve">
    <value>纬度</value>
  </data>
  <data name="LABEL_VEHICLE_ALTITUDE" xml:space="preserve">
    <value>海拔</value>
  </data>
  <data name="LABEL_ACTION" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="ERROR_RESULT_ADD_VEHICLE_FAILED_UNIQUE" xml:space="preserve">
    <value>此飞机已存在</value>
  </data>
  <data name="ERROR_PARAMS" xml:space="preserve">
    <value>参数错误</value>
  </data>
  <data name="ERROR_RESULT_ADD_VEHICLE_FAILED_SERIAL" xml:space="preserve">
    <value>序列号错误</value>
  </data>
  <data name="UNIT_METER" xml:space="preserve">
    <value>米</value>
  </data>
  <data name="BUTTON_ACTION_DEL" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_SECRET" xml:space="preserve">
    <value>输入通讯密钥</value>
  </data>
  <data name="LABEL_VEHICLE_SECRET" xml:space="preserve">
    <value>通讯密钥</value>
  </data>
  <data name="BUTTON_ACTION_SECRET" xml:space="preserve">
    <value>密钥</value>
  </data>
  <data name="LABEL_USER_NAME" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="LABEL_USER_NICK" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="LABEL_USER_ROLE" xml:space="preserve">
    <value>用户类型</value>
  </data>
  <data name="LABEL_USER_STATUS" xml:space="preserve">
    <value>用户状态</value>
  </data>
  <data name="LABEL_PASSWORD" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="INPUT_CREATE_TEXT_USERNAME" xml:space="preserve">
    <value>输入用户名</value>
  </data>
  <data name="INPUT_CREATE_TEXT_PASSWORD" xml:space="preserve">
    <value>输入密码</value>
  </data>
  <data name="INPUT_TEXT_DISPLAY_NAME" xml:space="preserve">
    <value>输入名称</value>
  </data>
  <data name="USER_ROLE_MONITOR" xml:space="preserve">
    <value>监控人员</value>
  </data>
  <data name="USER_ROLE_CONTROL" xml:space="preserve">
    <value>飞行控制</value>
  </data>
  <data name="USER_ROLE_THIRD_PARTY_ADMIN" xml:space="preserve">
    <value>飞机管理员</value>
  </data>
  <data name="USER_ROLE_SUPER_ADMIN" xml:space="preserve">
    <value>超级管理员</value>
  </data>
  <data name="USER_STATUS_NORMAL" xml:space="preserve">
    <value>正常</value>
  </data>
  <data name="USER_STATUS_DISABLED" xml:space="preserve">
    <value>已禁用</value>
  </data>
  <data name="BUTTON_ACTION_PASSWORD" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="BUTTON_ACTION_ENABLE" xml:space="preserve">
    <value>启用</value>
  </data>
  <data name="BUTTON_ACTION_DISABLE" xml:space="preserve">
    <value>禁用</value>
  </data>
  <data name="PAGE_USER_BUTTON_ACTION_VEHICLE_LIST" xml:space="preserve">
    <value>飞机列表</value>
  </data>
  <data name="ERROR_USER_EXISTS" xml:space="preserve">
    <value>用户不存在</value>
  </data>
  <data name="ERROR_SIGNTURE" xml:space="preserve">
    <value>参数错误</value>
  </data>
  <data name="ERROR_MISS_LOGIN" xml:space="preserve">
    <value>用户未登录</value>
  </data>
  <data name="ERROR_VEHICLE_NOT_EXISTS" xml:space="preserve">
    <value>此飞机不存在</value>
  </data>
  <data name="ERROR_MISS_VEHICLE_PERMISSION" xml:space="preserve">
    <value>无此飞机控制权限</value>
  </data>
  <data name="BUTTON_ACTION_CONTROL" xml:space="preserve">
    <value>控制台</value>
  </data>
</root>