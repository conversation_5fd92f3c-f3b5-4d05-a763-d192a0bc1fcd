﻿layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion: '/modules/expansion',
    qrcode:"/lib/qrcode"
}).define(['jquery', 'form', 'table', 'expansion', 'handle','qrcode'], function (exports) {
    const table_name = "order-table";
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        handle = layui.handle,
        qrcode = layui.qrcode,
        $ = layui.jquery,
        module = {
            build: function (role) {
                let columns = [[]];
 
                columns[0] = [];
                columns[0].push({ field: "Id", title: "ID" });
                columns[0].push({ field: "PlatformOrderNo", title: "编号" });
                columns[0].push({ field: "test", title: "测试", templet: "#test-open-tpl" });
                columns[0].push({ field: "MerchantOrderNo",  title: "单号" });
                columns[0].push({ field: "ChannelCode", title: "通道" });
                if (role === "superadmin") columns[0].push({ field: "MerchantUid", minWidth: 90, title: "商户" });
                columns[0].push({ field: "Device", title: "设备", templet: "#device-type-tpl" });
                columns[0].push({ field: "PaymentType", title: "类型", templet: "#payment-type-tpl" });
                columns[0].push({ field: "Amount", title: "金额", templet: "#amount-tpl" });
                columns[0].push({ field: "IPAddress",title: "IP" });
                columns[0].push({ field: "IPArea", title: "位置" });
                columns[0].push({ field: "AccountIdentity", title: "小号" });
                columns[0].push({ field: "Description", title: "描述" });
                columns[0].push({ field: "Remark",title: "备注" });
                columns[0].push({ field: "ChannelExecuteTime", title: "匹配" });
                columns[0].push({ field: "CreateTime"  ,  title: "创建" });
                columns[0].push({ field: "CompleteTime",  title: "完成" });
                columns[0].push({ field: "UserAgent"   , title: "环境" });
                columns[0].push({ field: "NotifyStatus",title: "回调", fixed: "right", templet: "#notify-status-tpl" });
                columns[0].push({ field: "Status", title: "状态", fixed: "right", templet: "#order-status-tpl" });
                columns[0].push({ field: "operations", minWidth: 120, title: "操作", fixed: "right", templet: "#operation-tpl" });
                return columns;
            },
            render: function (role) {
                table.render({
                    elem: "#" + table_name,
                    url: "/api/order/list",
                    cellMinWidth: 80,
                    height: "100%",
                    page: true,
                    defaultToolbar: ['filter'],
                    toolbar: "#toolbar",
                    limit: 30,
                    cols: module.build(role),
                    parseData: function (res) {
                        return expansion.parse(res);
                    }, done: function () {
                        expansion.render();
                    }
                });
                const connection = new signalR.HubConnectionBuilder()
                    .withUrl("/realtime?token=" + localStorage.getItem("token"))
                    .withAutomaticReconnect({nextRetryDelayInMilliseconds: (retryCount) => { return 3000; },maximumRetryCount: 5})
                    .build();
                connection.on("OrderStatus", function (id, data) {
                    if (Number(id) > 0) {
                        var data = JSON.parse(data);
                        var cache = table.cache[table_name];
                        for (var n = 0; n < cache.length; n++) {
                            if (Number(cache[n].Id) == Number(id)) {
                                if (typeof (data.status) != 'undefined') {
                                    cache[n].Status = data.status;
                                }
                                if (typeof (data.error) != 'undefined') {
                                    cache[n].Description = data.error;
                                }
                                if (typeof (data.device) != 'undefined') {
                                    cache[n].Device = data.device;
                                }
                                if (typeof (data.execute_time) != 'undefined') {
                                    cache[n].ChannelExecuteTime = data.execute_time;
                                }
                                if (typeof (data.useragent) != 'undefined') {
                                    cache[n].UserAgent = data.useragent;
                                }
                                if (typeof (data.ipaddress) != 'undefined') {
                                    cache[n].IPAddress = data.ipaddress;
                                }
                                if (typeof (data.address) != 'undefined') {
                                    cache[n].IPArea = data.address;
                                }
                                if (typeof (data.notify_status) != 'undefined') {
                                    cache[n].NotifyStatus = data.notify_status;
                                }
                                cache[n].operations = (new Date()).valueOf();
                                table.renderData(table_name);
                                break;
                            }
                        }
                       
                    }
                  
                });

                connection.start().catch(err => console.error("CONNECT", err));
            },
            submit: function (where) {
                table.reload(table_name, {
                    page: { curr: 1 },
                    where: {
                        biz_context: JSON.stringify(where)
                    }
                });
            }
        };

        var event_handler = {
            open: function (obj) {
                layer.open({
                    type: 1,
                    area: '350px',
                    offset: ["165px"],
                    resize: false,
                    shadeClose: false,
                    title: '测试订单',
                    offset: ["24px"],
                    content: $("#build-order-tpl").html(),
                    success: function (layero, index, elm) {
                        var url = location.protocol + "//" + location.host + "/payment/" + obj.data.PlatformOrderNo +"/templat.html" ;
                        var qr = new qrcode.qrcode($(layero).find('[ui-name="qr-image"]')[0], {
                            text: url,
                            width: 128,
                            height: 128,
                            colorDark: "#000",
                            colorLight: "#ffffff",
                            correctLevel: QRCode.CorrectLevel.H
                        });
                        $(layero).find('[lay-filter="open-url"]').attr("next", url)
                        form.on('submit(open-url)', function (data) {
                            window.open(url);
                            return false;
                        });
                    }
                });
            },
            query: function (obj) {
                $.ajax({
                    url: "api/order/queryresource?id=" + obj.data.Id,
                    type: "get",
                    loading: true,
                    dataType: "json",
                    success: function (res) {
                        if (res.success) {
                            layer.msg(res.body.msg, { icon: 1, time: 10000 }, function () { });
                        }
                    }
                });
            },
            notify_confirm: function (id,amount,code) {
                $.ajax({
                    url: "api/order/notify",
                    type: "post",
                    data: JSON.stringify( {
                        id: id,
                        amount: amount,
                        code: code
                    }),
                    loading: true,
                    dataType: "json",
                    success: function (res) {
                        if (res.success) {
                            if (res.body && res.body.google == true) {
                                layer.prompt({
                                    formType: 0,
                                    title: '请输入谷歌验证码',
                                    value: '',
                                    offset: ["165px"],
                                    success: (layero, index, that) => {
                                        layui.$(layero).find('input').attr('type', 'number');
                                        layui.form.render(layui.$(layero).find('input'));
                                    }
                                }, function (value, index, elem) {
                                    if (isNaN(value) || value.trim() === "") {
                                        layer.msg("请输入正确的验证码", { icon: 1, time: 2000 }, function () { });
                                        return;
                                    }
                                    event_handler.notify_confirm(id, amount, value);
                                    layer.close(index);
                                });
                            } else {

                            }
                        }
                    }
                });
            },
            notify: function (obj) {
                layer.prompt({
                    formType: 1,
                    title: '请输入实际金额',
                    value: obj.data.Amount/1000.0,
                    success: (layero, index, that) => {
                        layui.$(layero).find('input').attr('type', 'number');
                        layui.form.render(layui.$(layero).find('input'));
                    }
                }, function (value, index, elem) {
                    if (isNaN(value) || value.trim() === "") {
                        layer.msg("请输入正确的金额", { icon: 1, time: 2000 }, function () { });
                        return;
                    }
                    event_handler.notify_confirm(obj.data.Id, value, 0);
                    layer.close(index);
                    
                });
            }
        };
        table.on('tool(' + table_name + ')', function (obj) {
            event_handler[obj.event](obj);
        });
        form.on("submit(search)", function (data) {
            let search = {};

            for (let s in data.field) {
                if ($(".grid-toolbar-item").hasClass("switch-status") || s === "keyword") {
                    let t = String(data.field[s]);
                    if (t !== "null" && t !== "undefined" && t !== "") {
                        search[s] = t;
                    }
                }
            }
            module.submit(search);
            return false;
        });
        $(document).on("click", '.grid-toolbar-item.show-more', function () {
            $(".grid-toolbar-item").toggleClass("switch-status");
        });

     

    exports('order', module);
});