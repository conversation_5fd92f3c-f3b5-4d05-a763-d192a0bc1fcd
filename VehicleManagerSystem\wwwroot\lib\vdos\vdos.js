﻿var vui = {
    handler: {},
    seq:0,
    callbacks: function (inlibs,xcallback) {
        this.libs = {};
        if (Array.isArray(inlibs)) {
            for (let n in inlibs) {
                this.libs[inlibs[n]] = 0;
            }
        } else {
            this.libs[inlibs]=0;
        }
        this.callback = function (lib) {
           
            delete this.libs[lib];
            for (var n in this.libs) {
                return;
            } 
            xcallback ? xcallback() : void 0;
        }
    },
    loaded: function (modulename) {
        let rm = [];
        for (let n in vui.handler[modulename]) {
            vui.handler[modulename][n].callback(modulename);
            rm.push(n);
        }
        for (let n in rm) {
            delete vui.handler[modulename][n];
        }
       
    },
    use: function (libs,callback) {
        if (Array.isArray(libs)) {
            var callback_handler = new vui.callbacks(libs, callback);
            for (var idx in libs) {
                (vui.handler[libs[idx]] || (vui.handler[libs[idx]] = []))[++this.seq]=callback_handler;
                vui.loadjs(libs[idx]); 
            }
        } else {
            var callback_handler = new vui.callbacks(libs, callback);
            (vui.handler[libs] || (vui.handler[libs] = []))[++this.seq]=callback_handler;
            vui.loadjs(libs);
           
        }
    },
    loadjs: function (modulename) {
        if (vui.hasOwnProperty(modulename)) {
            vui.loaded(modulename); 
        } else if ($("body").find("script[name='vui-" + modulename +"']").length==0) {
            $("body").append($("<script name='vui-" + modulename + "' src='/lib/vdos/lib/" + modulename + ".js?v=1.0.2' type='text/javascript'></script>"));
        }
    },
    loadcss: function (modulename) {
        if ($("head").find("link[name='vui-style-" + modulename + "']").length == 0) {
            $("head").append($("<link name='vui-style-" + modulename + "' href='/lib/vdos/style/" + modulename + ".css?v=1.0.2' rel='stylesheet'/>"));
        }
    } 
};
