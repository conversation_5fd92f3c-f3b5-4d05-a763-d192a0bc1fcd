﻿


layui.config({
    base: '/layuiadmin/'
}).define(['jquery','form'], function (exports) {
    const layer = layui.layer, 
          form = layui.form, 
          $ = layui.jquery;
    form.verify({
        username: function (t, i) {
            if (t.trim().length < 4) {
                return "用户名至少4位"
            }
        },
        password: function (t, i) {
            if (t.trim().length < 6) {
                return "密码至少6位"
            }
        },
    })
   
    form.on("submit(install)", function (data) {
        console.log(data);
        data=data.field;
        $.ajax({
            url:"/api/account/install",
            type:"post",
            dataType:"json",
            data:JSON.stringify(data),
            success:function (res){
                if(res.success){
                    location.href="/login";
                }
            }
        })
        return false; 
    });
     
    exports('install', {});
});
 