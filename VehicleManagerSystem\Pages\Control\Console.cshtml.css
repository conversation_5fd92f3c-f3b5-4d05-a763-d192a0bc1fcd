﻿.root-view {
    width: 100%;
    height: 100%;
    position: relative;
}

#map-view {
    height: 100%;
    width: 100%;
}

#ui-view {
    user-select: none;
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}


.console-ui-top {
    height: 80px;
    width: 60%;
    top: 0;
    margin: 0 auto;
}

.console-ui-center {
    flex: 1;
    max-height: calc(100% - 160px);
    justify-content: start;
    align-items: center;
    display: flex;
}

.console-ui-center > .console-ui-left {
    height: 100%;
    gap: 10px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    column-count: 2;
    overflow: hidden;
    padding: 10px;
    box-sizing: border-box;
}

.console-ui-center > .console-ui-left > * {
    pointer-events: all;
} 
.console-ui-center > .console-ui-aim {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    position: absolute;
    width: 100%;
    pointer-events: none;
    overflow: hidden;
}

.console-ui-bottom {
    height: 80px;
    display: flex;
    align-items: center;
}

.node-list-action {
    display: flex;
    padding: 0px 24px;
    position: relative;
}

.node-list-action > * {
    margin-top: -30px;
    margin-left: -10px;
}

    .node-list-action > *:first-child {
        margin-top: 0;
        margin-left: 0;
    }

    .node-list-action > *:last-child {
        margin-top: 0;
        margin-left: 0;
        margin-left: -10px;
    }















.box-view {
    position:fixed;
    left:0;
    top:0;
    width:100vw;
    height:100vh;
   
    --island-status-height:26px;
    --island-status-width:40px;
    --island-total-height:120px;
    --island-total-width:200px; 
      background: rgba(2,64,101,0.9);
     clip-path:polygon(50% 31px,calc(50% - 70.5px) 31px,calc(50% - 80.5px) 11px,20.5px 11px,11px 20.5px,11px calc(100% - 20.5px),20.5px calc(100% - 11px),
                      calc(50% - 322.5px) calc(100% - 11px),
                      calc(50% - 313px) calc(100% - 20.5px),calc(50% - 313px) calc(100% - 170.5px),calc(50% - 302.5px) calc(100% - 181px),
                      calc(50% + 302.5px)  calc(100% - 181px), calc(50% + 313px)  calc(100% - 170.5px), calc(50% + 313px)  calc(100% - 20.5px),
                      calc(50% + 322.5px)  calc(100% - 11px),
                      calc(100% - 20.5px) calc(100% - 11px),calc(100% - 11px) calc(100% - 20.5px),
                      calc(100% - 11px) calc(50% + 140.5px),calc(100% - 20.5px) calc(50% + 131px),
                      calc(100% - 400.5px)  calc(50% + 131px),calc(100% - 411px) calc(50% + 120.5px),
                      calc(100% - 411px) calc(50% - 120.5px),calc(100% - 400.5px) calc(50% - 131px),
                      calc(100% - 20.5px) calc(50% - 131px),calc(100% - 11px) calc(50% - 140.5px),
                      calc(100% - 11px) 20.5px,calc(100% - 20.5px) 11px,calc(50% + 80.5px) 11px,calc(50% + 70.5px) 31px,50% 31px,
                      50% 0px,100% 0,100% 100%,0 100%,0 0,50% 0
     
     );
     
}
.box-view:before{
    content:"";
    background:#02d6fb;
    left:0;
    top:0;
    width:100%;
    height:100%;
    position:absolute;
    clip-path:polygon(50% 30px, calc(50% - 70px) 30px,calc(50% - 80px) 10px,20px 10px,10px 20px,10px calc(100% - 20px),20px calc(100% - 10px),
                      calc(50% - 322px) calc(100% - 10px),
                      calc(50% - 312px) calc(100% - 20px),calc(50% - 312px) calc(100% - 170px),calc(50% - 302px) calc(100% - 180px),
                      calc(50% + 302px)  calc(100% - 180px), calc(50% + 312px)  calc(100% - 170px), calc(50% + 312px)  calc(100% - 20px),
                      calc(50% + 322px)  calc(100% - 10px),
                      calc(100% - 20px) calc(100% - 10px),calc(100% - 10px) calc(100% - 20px),
                      calc(100% - 10px) calc(50% + 140px),calc(100% - 20px) calc(50% + 130px),calc(100% - 400px)  calc(50% + 130px),calc(100% - 410px) calc(50% + 120px),
                      calc(100% - 410px) calc(50% - 120px),calc(100% - 400px) calc(50% - 130px),calc(100% - 20px) calc(50% - 130px),calc(100% - 10px) calc(50% - 140px),
                      calc(100% - 10px) 20px,calc(100% - 20px) 10px,calc(50% + 80px) 10px,calc(50% + 70px) 30px,50% 30px
    
    );
}
.box-view.hide-video{
      clip-path:polygon(50% 31px,calc(50% - 70.5px) 31px,calc(50% - 80.5px) 11px,20.5px 11px,11px 20.5px,11px calc(100% - 20.5px),20.5px calc(100% - 11px),
                      calc(50% - 322.5px) calc(100% - 11px),
                      calc(50% - 313px) calc(100% - 20.5px),calc(50% - 313px) calc(100% - 170.5px),calc(50% - 302.5px) calc(100% - 181px),
                      calc(50% + 302.5px)  calc(100% - 181px), calc(50% + 313px)  calc(100% - 170.5px), calc(50% + 313px)  calc(100% - 20.5px),
                      calc(50% + 322.5px)  calc(100% - 11px),
                      calc(100% - 20.5px) calc(100% - 11px),calc(100% - 11px) calc(100% - 20.5px),
                      calc(100% - 11px) calc(50% + 60.5px),calc(100% - 20.5px) calc(50% + 51px),
                      calc(100% - 30.5px)  calc(50% + 51px),calc(100% - 41px) calc(50% + 40.5px),
                      calc(100% - 41px) calc(50% - 40.5px),calc(100% - 30.5px) calc(50% - 51px),
                      calc(100% - 20.5px) calc(50% - 51px),calc(100% - 11px) calc(50% - 60.5px),
                      calc(100% - 11px) 20.5px,calc(100% - 20.5px) 11px,calc(50% + 80.5px) 11px,calc(50% + 70.5px) 31px,50% 31px,
                      50% 0px,100% 0,100% 100%,0 100%,0 0,50% 0
     
     );
}

.box-view.hide-video:before{
   clip-path:polygon(50% 30px, calc(50% - 70px) 30px,calc(50% - 80px) 10px,20px 10px,10px 20px,10px calc(100% - 20px),20px calc(100% - 10px),
                      calc(50% - 322px) calc(100% - 10px),
                      calc(50% - 312px) calc(100% - 20px),calc(50% - 312px) calc(100% - 170px),calc(50% - 302px) calc(100% - 180px),
                      calc(50% + 302px)  calc(100% - 180px), calc(50% + 312px)  calc(100% - 170px), calc(50% + 312px)  calc(100% - 20px),
                      calc(50% + 322px)  calc(100% - 10px),
                      calc(100% - 20px) calc(100% - 10px),calc(100% - 10px) calc(100% - 20px),
                      calc(100% - 10px) calc(50% + 60px),calc(100% - 20px) calc(50% +  50px),calc(100% - 30px)  calc(50% + 50px),calc(100% - 40px) calc(50% + 40px),
                      calc(100% - 40px) calc(50% - 40px),calc(100% - 30px) calc(50% - 50px),calc(100% - 20px) calc(50% - 50px),calc(100% - 10px) calc(50% - 60px),
                      calc(100% - 10px) 20px,calc(100% - 20px) 10px,calc(50% + 80px) 10px,calc(50% + 70px) 30px,50% 30px
    
    );
}

.box-view>.title-box{
    position:absolute;
    height:30px;
    width:100%;
    display:flex;
    justify-content:center;
    align-items:center;
    color:#02d6fb;

}
.box-view>.video-box{
    width:421px;
    height:100%;
    position:absolute;
    right:0;
    top:0;
    display:flex;
    justify-content:center;
    align-items:center;
}
.box-view>.video-box .player{
     background:#000;
     width:320px;
     height:180px;
     border-right: 1px solid #02d6fb;
     position:relative;
}
.box-view>.video-box .player>.player-ui{
   position:absolute;
   justify-content:center;
   display:flex;
   align-items:center;
   height:100%;
   width:100%;

}
.box-view>.video-box .player>.player-ui>.direction{
    display:grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 5px;
    width:100%;
    height:100%;
}
.box-view>.video-box .player>.player-ui>.direction>span{
  
    display:flex;
    justify-content:center;
    align-items:center;
    color:#ffffff88;
    padding:4px;
}
.box-view>.video-box .player>.player-ui>.direction>span.action-forward{
    justify-content:center;
    align-items:start;
}
.box-view>.video-box .player>.player-ui>.direction>span.action-go-left{
    justify-content:start;
    align-items:center;
}
.box-view>.video-box .player>.player-ui>.direction>span.action-go-right{
    justify-content:end;
    align-items:center;
}
.box-view>.video-box .player>.player-ui>.direction>span.action-retreat{
    justify-content:center;
    align-items:end;
}
.box-view>.video-box .player>.player-ui>.direction>span:hover{
    color:#02d6fb;
}
.box-view>.video-box>.video-content{
    display:flex;
    flex-direction:column;
}
.box-view>.video-box>.video-content>.video-center{
    display:flex;
    flex-direction:row;
}
.box-view>.video-box>.video-content>.video-top-bar{
    height:30px;
    padding-left:60px;
    display:flex;
    justify-content:end;
    gap:2px;
}
.box-view>.video-box>.video-content>.video-bottom{
    height:30px;
    display:flex;
    justify-content:end;
     gap:2px;
     padding-left:60px;
     
}
.box-view>.video-box>.video-content>.video-center>.video-left-bar{
    width:60px;
    display:flex;
    flex-direction:column;
    justify-content:end;
    gap:2px;
}
.box-view>.video-box>.video-content>.video-top-bar>.camera-btn,
.box-view>.video-box>.video-content>.video-bottom>.camera-btn,
.box-view>.video-box>.video-content>.video-center>.video-left-bar>.camera-btn{
    flex:1;
}
 .box-view.hide-video>.video-box>.video-content .player,
 .box-view.hide-video>.video-box>.video-content .video-bottom,
.box-view.hide-video>.video-box>.video-content .video-top-bar{
     display:none !important;
 }

 .box-view.hide-video>.video-box>.video-content>.video-center>.video-left-bar>.camera-btn{
     display:none;
 }
 .box-view.hide-video>.video-box{
     width:42px;
 }
  .box-view.hide-video>.video-box>.video-content>.video-center>.video-left-bar{
     height:120px;
     width:40px;
 }
 .box-view.hide-video>.video-box>.video-content>.video-center>.video-left-bar> .hidden-video{
     width:41px;
      border:none;
     writing-mode: vertical-rl;
 }
 .hidden-video{
     display:flex !important;
 }
  .box-view  .hidden-video:before{
      content:attr(attr-normal);
  }
  .box-view.hide-video  .hidden-video:before{
      content:attr(attr-hidden);
  }
.camera-btn{
    width:58px;
    border:1px solid #02d6fb;
    color:#fff;
    height:28px;
    display:flex;
    justify-content:center;
    align-items:center;
    cursor:pointer; 
}
 
.camera-btn:hover{
    background:#02d6fb;
     
}

.left-view {
    width: 300px;
    height: 100%;
    padding: 10px;
    position: absolute;
    left: 0px;
    top: 0px;
    box-sizing: border-box;
    display: none;
    justify-content: stretch;
    align-items: stretch;
}
.left-view > .content {
    border: 1px solid #02d6fb;
    background: rgba(2,64,101,0.9);
    width:100%;
    
}


.vehicle-hud {
    position: absolute;
    top: 0;
    height: 60px;
    background: rgba(2,64,101,0.9);
    width: 600px;
    border-top: none;
    clip-path:polygon(50% 36px,calc(50% + 140px) 36px,calc(50% + 150px) calc(100% - 1px),calc(100% - 1px) calc(100% - 1px),100% 100%,
    0 100%,1px calc(100% - 1px),calc(50% - 150px) calc(100% - 1px),calc(50% - 140px) 36px
    );
}
.vehicle-hud:before{
    content:"";
    position:absolute;
    left:0;
    top:0;
    width:100%;
    height:100%;
    background: #02d6fb;
     clip-path:polygon(50% 37px,calc(50% + 139.5px) 37px,calc(50% + 149px) calc(100% - 1px),calc(50% - 149px) calc(100% - 1px),calc(50% - 139.5px) 37px,50% 37px,
     50% 0,0 0,0 100%,100% 100%,100% 0,50% 0);
}
.vehicle-hud-text{
    position: absolute;
    top: 0;
    height: 60px;
    
    width: 600px;
    border-top: none;
    display:flex;
    flex-direction:column;
}
.vehicle-hud-text>.row-fill{
    flex:1;
}
.vehicle-hud-text>.row-end{
   
    height:20px;
    display:flex;
    color:#02d6fb;
}
.vehicle-hud-text>.row-end>div:first-child{
   flex:1;
   display:flex;
   justify-content:right;
     align-items:center;
}
.vehicle-hud-text>.row-end>div:last-child{
   flex:1;
   display:flex;
   justify-content:left;
   align-items:center;
}
.vehicle-hud-text>.row-end>.center{
    width:300px;
   display:flex;
   justify-content:center;
   align-items:center;
}
.val-signal.locked
{
    display:flex; 
    justify-content: center;
    align-items: center;
    color:#fff;
    gap:4px;
}
.val-signal.locked::after{
    content:"已锁定";
    font-family:weilai;
    font-size:0.8em;

}
.vehicle-control {
    position: absolute;
    bottom: 10px;
    width: 600px;
  
   
    --border-width:2px;
    --border-length:24px;
    height:150px;
    padding:8px;
    box-sizing:content-box;
    display:flex;
    flex-direction:column;
    justify-content:center;
    user-select:none;
    
}

.vehicle-control1:before {
    background: #02d6fb;
    content: "";
    width: 100%;
    height: 100%;
    left:0;
    top:0;
    position: absolute;
    clip-path: polygon(                                 var(--border-length) 0,
                                        var(--border-length) var(--border-width),
            var(--border-width) var(--border-width),
            var(--border-width) var(--border-length),
        0 var(--border-length), 0 calc(100% - var(--border-length)), var(--border-width)  calc(100% - var(--border-length)),
        var(--border-width)  calc(100% - var(--border-width)),var(--border-length) calc(100% - var(--border-width)),
        var(--border-length) 100%, calc(100% - var(--border-length)) 100%,calc(100% - var(--border-length)) calc(100% - var(--border-width)),
        calc(100% - var(--border-width))  calc(100% - var(--border-width)),
        calc(100% - var(--border-width))  calc(100% - var(--border-length)),
        100%  calc(100% - var(--border-length)),100%  var(--border-length),
        calc(100% - var(--border-width)) var(--border-length) ,calc(100% - var(--border-width)) var(--border-width),
        calc(100% - var(--border-length))  var(--border-width), calc(100% - var(--border-length))  0,   var(--border-length) 0,
    100% 0,100% 100%,0 100%,0 0);
}
.vehicle-control>.status{
    display:flex;
    flex-direction:row;
    gap:10px;
    height:20px;
    justify-content:start;
    align-items:center;
     color:#02d6fb;
}
.vehicle-control>.status>div{
    color:#02d6fb;
    font-size:0.9em;
}
.vehicle-control>.status>.iconfont{
    font-size:18px;
    border:1px solid transparent;
    cursor:pointer;
}
.vehicle-control>.status>.iconfont:hover{
    border:1px solid #02d6fb;
}
.vehicle-control>.status>.val-battery{

    width:25px;
    height:12px;
    position:relative;
    --battery:80%;
    clip-path:polygon(0px 50%,0px 0px,calc(100% - 2px) 0,calc(100% - 2px) 2px,100% 2px,100% calc(100% - 2px),
    calc(100% - 2px) calc(100% - 2px),calc(100% - 2px) 100%,0 100%);
      
    box-sizing:border-box;
      
}
.vehicle-control>.status>.val-battery::after{
    content:"";
    width:100%;
    height:100%;
    position:absolute;
    background:#02d6fbAA;
    left:0;
    top:0;
    clip-path:polygon(-1px 50%,2px 50%,2px calc(100% - 2px),calc(100% - 4px) calc(100% - 2px),calc(100% - 4px) calc(100% - 4px),calc(100% - 2px) calc(100% - 4px),
    calc(100% - 2px) 4px,calc(100% - 4px) 4px,calc(100% - 4px) 2px,2px 2px,2px 50%,0px 50%,-2px -2px,101% 0,101% 101%,0 101%);
    z-index:1;
     
}
.vehicle-control >.status> .val-battery::before {
        content:"";
        left:0;
        top:0;
        width:var(--battery);
        height:100%;
        background:#02d6fbAA;
        display:inline-block;
        position:absolute;
        z-index:0;
      
}
.vehicle-control>.commands{
    display:flex;
    flex-direction:row;
    justify-content:stretch;
    flex:1;
    
}
.vehicle-control>.commands>.command-left{
    display:flex;
    flex-direction:column;
    flex:1;
}
.vehicle-control>.commands>.command-left>.child-command{
    height:14px;
   
}
.vehicle-control>.commands>.command-left>.charts{
    flex:1;
    display:flex;
    gap:10px;
    justify-content:center;
    align-items:center;

}
.vehicle-control>.commands>.command-left>.charts>.mission-group{

    height:74px;
    display:flex;
    flex-direction:column;
    gap:5px;
    
    position:relative;
     padding:5px 15px;
    justify-content:center;
    color:#02d6fb;
}
.vehicle-control>.commands>.command-left>.charts>.time-group{
   
    height:74px;
    display:flex;
    flex-direction:column;
    gap:5px;
    --border-width:2px;
    --border-length:18px;
    position:relative;
    padding:5px 15px;
    justify-content:center;
    color:#02d6fb;
}
.vehicle-control>.commands>.command-left>.charts>.mission-group:before,
.vehicle-control>.commands>.command-left>.charts>.time-group:before {
    background: #02d6fb;
    content: "";
    width: 100%;
    height: 100%;
    left:0;
    top:0;
    position: absolute;
    clip-path: polygon(var(--border-length) 0,
        var(--border-length) var(--border-width),
        var(--border-width) var(--border-width),
        var(--border-width) var(--border-length),
        0 var(--border-length), 0 calc(100% - var(--border-length)), var(--border-width)  calc(100% - var(--border-length)),
        var(--border-width)  calc(100% - var(--border-width)),var(--border-length) calc(100% - var(--border-width)),
        var(--border-length) 100%, calc(100% - var(--border-length)) 100%,calc(100% - var(--border-length)) calc(100% - var(--border-width)),
        calc(100% - var(--border-width))  calc(100% - var(--border-width)),
        calc(100% - var(--border-width))  calc(100% - var(--border-length)),
        100%  calc(100% - var(--border-length)),100%  var(--border-length),
        calc(100% - var(--border-width)) var(--border-length) ,calc(100% - var(--border-width)) var(--border-width),
        calc(100% - var(--border-length))  var(--border-width), calc(100% - var(--border-length))  0,   var(--border-length) 0,
    100% 0,100% 100%,0 100%,0 0);
}
.vehicle-control>.commands>.command-left>.charts>.heading{
    background:#1e9fff;
    height:64px;
    width:64px;
}
.vehicle-control>.commands .modes{
     display:flex;
    flex-direction:row;
    display:flex;
    justify-content:center;
    align-items:end;
    gap:4px;
    height:32px;
}
.vehicle-control>.commands .modes>div{
     border:2px solid #02d6fbEE ;
     color:#02d6fbEE;
     padding:6px 8px 4px 8px;
     cursor:pointer;
     flex:1;
     text-align:center;
}
.vehicle-control>.commands .modes>div:hover{
    background:#02d6fbEE;
    color:#fff;
    border-color:#fff;
}
.vehicle-control>.commands>.command-right{
    display:grid;
    grid-template-columns: repeat(3, 1fr); /* 三列，每列占据剩余空间的1/3 */
    grid-template-rows: repeat(3, 1fr); /* 三行，每行固定高度为100px */
     gap: 5px;
    width:130px;
    height:130px;

}
.vehicle-control>.commands>.command-right>span{
    height:40px;
    width:40px;
    font-size:24px;
    color:#02d6fb;
       display:flex;
     justify-content:center;
     align-items:center;
}
.vehicle-control>.commands>.command-right>span:hover{
     color:#FFF;
     cursor:pointer;
  
}
.vehicle-control>.commands>.command-right input{
    background:transparent;
    border:none;
    outline:none;
    color:#02d6fb;
    text-align:center;
}
input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    input[type="number"] {
      -moz-appearance: textfield;
    }