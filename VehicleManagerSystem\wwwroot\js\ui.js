﻿$(function (ui) {
    ui.loading = function (no_border,text) {
        var shade = $("<shade></shade>");
        var load = $("<loading " + (no_border ? "no-border" : "") + "></loading>");
       
        shade.append(load);
        if (text) {
            shade.append($("<div style='position:absolute'>" + text + "</div>"));
        }
        $("body").append(shade);
        return {
            loading: shade,
            close: function () {
                 this.loading.remove();
            }
        }
    }
    ui.alert = function (text,callback) {
        var shade = $("<shade></shade>");
        var load = $("<alert><title>" + window.constants.JS_ALERT_TITLE_WARNING +"</title><content>" + text +"</content></alert>");
        $("body").append(shade.append(load));
        shade.on("click", function () {
            if (callback) {
                if (callback()!=true) {
                    $(this).remove();
                }
            } else {
                $(this).remove();
            }
           
        }) 
    }
    window.ui = ui;
}(window.ui || {}));