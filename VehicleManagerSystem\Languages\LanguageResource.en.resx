﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>FlightManagerSystem</value>
  </data>
  <data name="ERROR_UNKNOW" xml:space="preserve">
    <value>unknow error.</value>
  </data>
  <data name="ERROR_LOGIN_DISABLED" xml:space="preserve">
    <value>This user is disabled</value>
  </data>
  <data name="ERROR_USERNAME_OR_PASSWORD" xml:space="preserve">
    <value>Username or Password Error</value>
  </data>
  <data name="FORMAT_PASSWORD" xml:space="preserve">
    <value>Password length requires 6-16 characters</value>
  </data>
  <data name="BUTTON_ACTION_LOGIN" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="INPUT_TEXT_PASSWORD" xml:space="preserve">
    <value>Enter your password</value>
  </data>
  <data name="INPUT_TEXT_USERNAME" xml:space="preserve">
    <value>Enter your user name</value>
  </data>
  <data name="BOX_TITLE_LOGIN" xml:space="preserve">
    <value>Please Login</value>
  </data>
  <data name="FORMAT_USERNAME" xml:space="preserve">
    <value>The username should only contain (6-16) letters and numbers</value>
  </data>
  <data name="JS_ALERT_TITLE_WARNING" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="JS_ALERT_TITLE_ERROR" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="UI_LOADING" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="ERROR_PERMISSION_ERROR" xml:space="preserve">
    <value>No operation permission</value>
  </data>
  <data name="ERROR_PAGE_NOT_FOUND" xml:space="preserve">
    <value>NotFound</value>
  </data>
  <data name="ERROR_SERVER_ERROR" xml:space="preserve">
    <value>Server Error</value>
  </data>
  <data name="PAGE_TITLE_LOGIN" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="PAGE_TITLE_ADMIN_INDEX" xml:space="preserve">
    <value>Manager</value>
  </data>
  <data name="INDEX_MENU_VEHICLE_MANAGER" xml:space="preserve">
    <value>VehicleManager</value>
  </data>
  <data name="INDEX_MENU_HANGAR_MANAGER" xml:space="preserve">
    <value>HangarManager</value>
  </data>
  <data name="INDEX_MENU_BASE_STATION_MANAGER" xml:space="preserve">
    <value>BaseStationManager</value>
  </data>
  <data name="INDEX_MENU_USER_MANAGER" xml:space="preserve">
    <value>UserManager</value>
  </data>
  <data name="INDEX_MENU_MISSION_MANAGER" xml:space="preserve">
    <value>MissionManager</value>
  </data>
  <data name="INDEX_MENU_FLIGHT_LOGGER" xml:space="preserve">
    <value>FlightLogger</value>
  </data>
  <data name="LANGUAGE_NAME" xml:space="preserve">
    <value>Englist</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_SERIAL" xml:space="preserve">
    <value>Enter aircraft serial no</value>
  </data>
  <data name="BUTTON_TEXT_SEARCH" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="INPUT_TEXT_SEARCH" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="BUTTON_TEXT_ADD" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="BUTTON_CONTROL" xml:space="preserve">
    <value>Control</value>
  </data>
  <data name="VEHICLE_STATUS_OFFLINE" xml:space="preserve">
    <value>Offline</value>
  </data>
  <data name="VEHICLE_STATUS_ONLINE" xml:space="preserve">
    <value>Online</value>
  </data>
  <data name="VEHICLE_STATUS_UNKNOW" xml:space="preserve">
    <value>Unknow</value>
  </data>
  <data name="GENERAL_ALL" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="VEHICLE_TYPE_HELI" xml:space="preserve">
    <value>HeliCopter</value>
  </data>
  <data name="VEHICLE_TYPE_QUAD" xml:space="preserve">
    <value>QuadCopter</value>
  </data>
  <data name="VEHICLE_TYPE_HEXA" xml:space="preserve">
    <value>HexaCopter</value>
  </data>
  <data name="VEHICLE_TYPE_QUAD_X2" xml:space="preserve">
    <value>Dual-Prop Quadcopter</value>
  </data>
  <data name="VEHICLE_TYPE_HEXA_X2" xml:space="preserve">
    <value>Dual-Prop HexaCopter</value>
  </data>
  <data name="VEHICLE_TYPE_OTHER" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="VEHICLE_TYPE_UNKNOW" xml:space="preserve">
    <value>Unknow</value>
  </data>
  <data name="LABEL_VEHICLE_SERIAL" xml:space="preserve">
    <value>SerialNo</value>
  </data>
  <data name="LABEL_VEHICLE_NAME" xml:space="preserve">
    <value>DisplayName</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_DISPLAY_NAME" xml:space="preserve">
    <value>Enter aircraft  display name</value>
  </data>
  <data name="BUTTON_CONFIRM" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="LABEL_VEHICLE_TYPE" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="LABEL_VEHICLE_MODEL" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="LABEL_VEHICLE_MANUFACTURER" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="LABEL_VEHICLE_STATUS" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="LABEL_VEHICLE_BATTERY" xml:space="preserve">
    <value>Battery</value>
  </data>
  <data name="LABEL_VEHICLE_RSSI" xml:space="preserve">
    <value>Delay</value>
  </data>
  <data name="LABEL_VEHICLE_LONGITUDE" xml:space="preserve">
    <value>Longitude</value>
  </data>
  <data name="LABEL_VEHICLE_LATITUDE" xml:space="preserve">
    <value>Latitude</value>
  </data>
  <data name="LABEL_VEHICLE_ALTITUDE" xml:space="preserve">
    <value>Altitude</value>
  </data>
  <data name="LABEL_ACTION" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="ERROR_RESULT_ADD_VEHICLE_FAILED_UNIQUE" xml:space="preserve">
    <value>This aircraft already exists</value>
  </data>
  <data name="ERROR_PARAMS" xml:space="preserve">
    <value>Params error</value>
  </data>
  <data name="ERROR_RESULT_ADD_VEHICLE_FAILED_SERIAL" xml:space="preserve">
    <value>Serial error</value>
  </data>
  <data name="UNIT_METER" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="BUTTON_ACTION_DEL" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_SECRET" xml:space="preserve">
    <value>Enter secret</value>
  </data>
  <data name="LABEL_VEHICLE_SECRET" xml:space="preserve">
    <value>Secret</value>
  </data>
  <data name="BUTTON_ACTION_SECRET" xml:space="preserve">
    <value>Secret</value>
  </data>
  <data name="LABEL_USER_NAME" xml:space="preserve">
    <value>UserName</value>
  </data>
  <data name="LABEL_USER_NICK" xml:space="preserve">
    <value>DisplayName</value>
  </data>
  <data name="LABEL_USER_ROLE" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="LABEL_USER_STATUS" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="LABEL_PASSWORD" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="INPUT_CREATE_TEXT_USERNAME" xml:space="preserve">
    <value>Enter the user name</value>
  </data>
  <data name="INPUT_CREATE_TEXT_PASSWORD" xml:space="preserve">
    <value>Enter the password</value>
  </data>
  <data name="INPUT_TEXT_DISPLAY_NAME" xml:space="preserve">
    <value>Enter the nick name</value>
  </data>
  <data name="USER_ROLE_MONITOR" xml:space="preserve">
    <value>Monitor</value>
  </data>
  <data name="USER_ROLE_CONTROL" xml:space="preserve">
    <value>Controller</value>
  </data>
  <data name="USER_ROLE_THIRD_PARTY_ADMIN" xml:space="preserve">
    <value>AircraftAdmin</value>
  </data>
  <data name="USER_ROLE_SUPER_ADMIN" xml:space="preserve">
    <value>SuperAdmin</value>
  </data>
  <data name="USER_STATUS_NORMAL" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="USER_STATUS_DISABLED" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="BUTTON_ACTION_PASSWORD" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="BUTTON_ACTION_ENABLE" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="BUTTON_ACTION_DISABLE" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="PAGE_USER_BUTTON_ACTION_VEHICLE_LIST" xml:space="preserve">
    <value>Vehicles</value>
  </data>
  <data name="ERROR_USER_EXISTS" xml:space="preserve">
    <value>User not exists</value>
  </data>
  <data name="ERROR_SIGNTURE" xml:space="preserve">
    <value>Params error</value>
  </data>
  <data name="ERROR_MISS_LOGIN" xml:space="preserve">
    <value>Please login</value>
  </data>
  <data name="ERROR_VEHICLE_NOT_EXISTS" xml:space="preserve">
    <value>vehicle not found</value>
  </data>
  <data name="ERROR_MISS_VEHICLE_PERMISSION" xml:space="preserve">
    <value>You do not have permission to control this vehicle.</value>
  </data>
  <data name="BUTTON_ACTION_CONTROL" xml:space="preserve">
    <value>Control</value>
  </data>
</root>