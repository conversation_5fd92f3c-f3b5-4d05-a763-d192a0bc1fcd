﻿using System.Reflection;
using System.Text;
using MQTTnet;
using MQTTnet.Extensions.TopicTemplate;
using static VehicleManagerSystem.Services.VehicleRealTime;

namespace VehicleManagerSystem.Services
{
    public partial class MQTTService
    { 

        //飞行指令控制
        private  static String topic_vehicle_flight_command_control="/vehicle/control/flight/command/";

        //引导飞行位置控制
        private  static String topic_vehicle_flight_position_control="/vehicle/control/flight/position/";
        //飞机载荷控制
        private  static String topic_vehicle_payload_control="/vehicle/control/payload/";
        //飞机摄像头控制
        private  static String topic_vehicle_camera_control="/vehicle/control/camera/";
        //飞机任务控制/vehicle/control/mission/VDB00ZY2500TW50000AF9000001
        private MqttTopicTemplate PositionControl = new MqttTopicTemplate("/vehicle/control/flight/position/{device_sn}");
        private MqttTopicTemplate MissionControl = new MqttTopicTemplate("/vehicle/control/mission/{device_sn}");
        private MqttTopicTemplate FlightControl = new MqttTopicTemplate("/vehicle/control/flight/command/{device_sn}");
        //控制指令回调
        //
        public async Task SendAsync(string clientId,string vehicleId,string method,string action,object data)
        {
            if (method == "flight")
            { 
                await mqtt_client.PublishAsync(new MqttApplicationMessageBuilder()
                          .WithUserProperty("clientId",clientId)
                          .WithTopic(FlightControl.WithParameter("device_sn", vehicleId).TopicFilter)
                          .WithPayload(Encoding.UTF8.GetBytes(Newtonsoft.Json.JsonConvert.SerializeObject(new
                          {
                              action,
                              data
                          })))
                          .Build(), CancellationToken.None);
            }
            else if (method == "mission")
            {
                  await mqtt_client.PublishAsync(new MqttApplicationMessageBuilder()
                          .WithUserProperty("clientId",clientId)
                          .WithTopic(MissionControl.WithParameter("device_sn", vehicleId).TopicFilter)
                          .WithPayload(Encoding.UTF8.GetBytes(Newtonsoft.Json.JsonConvert.SerializeObject(new
                          {
                              action,
                              data
                          })))
                          .Build(), CancellationToken.None);
            } else if (method == "position")
            {
                  await mqtt_client.PublishAsync(new MqttApplicationMessageBuilder()
                          .WithUserProperty("clientId",clientId)
                          .WithTopic(PositionControl.WithParameter("device_sn", vehicleId).TopicFilter)
                          .WithPayload(Encoding.UTF8.GetBytes(Newtonsoft.Json.JsonConvert.SerializeObject(new
                          {
                              action,
                              data
                          })))
                          .Build(), CancellationToken.None);
            }
             
        }

    }
}
