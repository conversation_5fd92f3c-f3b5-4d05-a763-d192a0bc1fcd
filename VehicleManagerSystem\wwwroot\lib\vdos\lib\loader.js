﻿(function (vui) {

	vui.Loader = function (config) {
		var speed = 4;
		var frontColor = "#333";
		var bkgColor = "#eee";
		var shade = "#00000000";
		var is_root = true;
		if (typeof config != "undefined") {
			if (typeof config.speed != "number" || Number(config.speed < 1) || Number(config.speed) > 4) {
				speed = 4;
			} else {
				speed = config.speed;
			}
			speed = Math.floor(speed);
			frontColor = config.frontColor;
			bkgColor = config.bkgColor;
			if (typeof config.frontColor == "undefined") {
				frontColor = "#333";
			} 
			if (typeof config.bkgColor == "undefined") {
				bkgColor = "#eee";
			}
			if (typeof config.shade != "undefined") {
				shade = config.shade;
			}
			if (typeof config.root != "undefined") {
				is_root = config.root==true;
			}
		}
		
		this.root = $('<div class="vui-loader-container"></div>');
		this.loader = $("<div class='vui-loader vui-loader-speed-" + speed + "'></div>").appendTo(this.root);
		if (!is_root) {
			this.root.addClass("vui-loader-no-root-node"); 
			
		}
		this.root.css("background", shade);
		this.loader.css("--front-color", frontColor);
		this.loader.css("--bkg-color", bkgColor);
		this.appendTo = function (elem) {
			this.root.appendTo(elem);
			return this;
		}
		this.remove = function () {
			this.root.remove();
		}
	};
	vui.loadcss("loader");
	vui.loaded("loader");
})(vui);
