﻿(function (vui) {

	vui.Vehicle = function (t) {
		this.root = $('<div class="vui-vehicle" vid="'+t+'"></div>');
		this.control = $("<div class='vehicle-control'></div>").appendTo(this.root);;
		this.meida = $("<div class='vehicle-media'></div>").appendTo(this.root);;
		this.infos = $("<div class='infos'></div>").appendTo(this.meida);
		this.veh_no = $("<div class='veh-no'>TW50-001</div>").appendTo(this.infos);
		this.rssi = $("<div class='veh-rssi'></div>").appendTo(this.infos);
		this.battery = $("<div class='veh-battery'></div>").appendTo(this.infos);
		
		this.video_box = $("<div class='media-bg'></div>").appendTo(this.meida);
		this.video = $("<div class='media-video'></div>").appendTo(this.video_box);

		this.attitude = $("<div  class='veh-att'></div>").appendTo(this.control);
		this.dir = $("<div class='veh-handing-box'></div>").appendTo(this.attitude);
		this.handing_bor = $("<div class='veh-handing-bor'></div>").appendTo(this.dir);
		this.handing = $("<div class='veh-handing'></div>").appendTo(this.handing_bor);
		this.location = $("<div  class='veh-local'></div>").appendTo(this.attitude);
		this.lat = $("<div  class='veh-lat'>纬度:31.4712345</div>").appendTo(this.location);
		this.lon = $("<div  class='veh-lon'>经度:104.4712345</div>").appendTo(this.location);
		this.alt = $("<div  class='veh-alt'>高度:766.20</div>").appendTo(this.location);


		this.user = $("<div class='veh-user'></div>").appendTo(this.control);
		this.avatarbox = $("<div class='veh-avatar-box'></div>").appendTo(this.user);
		this.avatar = $("<div class='veh-avatar'></div>").appendTo(this.avatarbox);
		this.user_detail = $("<div class='veh-detail'></div>").appendTo(this.user);
		this.user_name = $("<div class='veh-user-name'>刘大玉</div>").appendTo(this.user_detail);
		this.user_no = $("<div  class='veh-user-no'>No.001</div>").appendTo(this.user_detail);
		this.user_inf = $("<div  class='veh-user-info'>极品飞车手</div>").appendTo(this.user_detail);
		this.status = $('<div class="status"></div>').appendTo(this.root);
	
		this.setStatus = function (status) {
			this.status.html(status);
		}
		this.appendTo = function (elem) {
			this.root.appendTo(elem);
			return this;
		}
		this.click = function (cbk) {
			this.click_handler = cbk;
			return this;
		}
		this.root.on("click", () => {
			this.click_handler?.call(this, this.root);
		});
	};
	vui.StatusIsland = function (before) {
		this.root = $("<div class='vui-status-island'></div>");
		this.progress_bg = $('<div class="vui-status-island-progress"></div>').appendTo(this.root);
		this.progress_val = $('<div class="vui-status-island-progress-value"></div>').appendTo(this.progress_bg);


		var speed_box = $("<div class='vui-flight-speed'></div>");
		this.speed = $("<div>0.00</div>");
		var content = $("<div class='vui-flight-content' style='flex:1'></div>");
		var content_top = $("<div class='vui-flight-content-top'>绵阳-永兴-TW50-0002</div>");
		var content_fill = $("<div class='vui-flight-content-fill'></div>");
		var content_bottom = $("<div class='vui-flight-content-bottom'></div>");
		var lat =this.lat= $("<div class='veh-lat'>纬度:0.000000</div>");
		var lon = this.lon = $("<div class='veh-lon'>经度:0.000000</div>");
		var mode = $("<div  class='veh-mode'>自动任务</div>");
		content_bottom.append(lat).append(mode).append(lon);
		var height_box = $("<div class='vui-flight-height'></div>");
		this.height = $("<div>0.00</div>");

		this.root.append(speed_box.append(this.speed)).append(content.append(content_top).append(content_fill).append(content_bottom)).append(height_box.append(this.height));
		before?.call(this, this.root);
		this.setProgress = function (val) {
			this.progress_val.css("width", val);
		}
		this.setSpeed = function (val) {
			this.speed.text( val);
		}
		this.setHeight = function (val) {
			this.height.text(val);
		}
		this.setPosition = function (lon_t,lat_t) {
			this.lat.text("纬度:" + lat_t);
			this.lon.text("经度:" + lon_t);
		}
		this.appendTo = function (elem) {
			this.root.appendTo(elem);
			return this;
		}

	};
	vui.Hexagon = function (before) {
		this.root = $('<div class="vui-hexagon"></div>');
		before?.call(this, this.root);
		this.appendTo = function (elem) {
			this.root.appendTo(elem);
			return this;
		}
		 
		this.click = function (cbk) {
			this.click_handler = cbk;
			return this;
		}
		this.root.on("click",  () =>{
			this.click_handler?.call(this, this.root);
		});
	}

	vui.Form = function (config) {
		this.root = $('<div class="vui-dialog-shade"></div>');
		this.form = $('<form class="vui-dialog-general vui-box-form" action="javascript:;"></form>').appendTo(this.root);
		this.content = $('<div class="vui-box-form-content"></div>').appendTo(this.form);
		this.confirm = $('<button class="confirm" type="submit"></button>').appendTo(this.form);
		this.close = $('<div class="close"></div>').appendTo(this.form);
		$('<div class="end"></div>').appendTo(this.form);
		$('<div class="left"></div>').appendTo(this.form);
		$('<div class="bottom"></div>').appendTo(this.form);
		if (typeof config.before == "function") {
			config.before(this.content);
		}
		this.confirm.attr("data-text", window.constants.BUTTON_CONFIRM); 
		if (typeof config.yes == "function") {
			this.form.on("submit", (new (function (config, root, content) {
				this.handler = function () {
					var field = {};
					content.find("[name]").each(function () {
						field[$(this).attr("name")] = $(this).val();
					});
					if (config.yes(field) != false) {
						root.remove();
					}
				}

			})(config, this.root, this.content)).handler)
		}
		this.close.on("click", (new (function (config, root) {
			this.handler = function () {
					root.remove();
			}

		})(config, this.root)).handler); 
		this.appendTo = function (elem) {
			elem.append(this.root);
		}
		this.close = function () {
			this.root.remove();
		}
	};
	vui.hidePanelGroup = function (container, step) {
		if (typeof step == "undefined") {
			step = 200;
		}
		container.find(".vui-vehicle").each(function (seq) {
			let el = $(this); 
			setTimeout(function () {
				el.animate({ left: "-100%" }, step * 2);
			}, seq * step);
		});
		container.find(".vui-status-island").each(function () {
			$(this).animate({ top: "-100%",marginTop:"13px" },400);
		});
	}
	vui.showPanelGroup = function (container, step) {
		if (typeof step == "undefined") {
			step = 200;
		}
		container.find(".vui-vehicle").each(function (seq) {
			let el = $(this);
			setTimeout(function () {
				el.animate({ left: "0" }, step*2);
			}, seq * step);
		});
		container.find(".vui-status-island").each(function () {
			$(this).animate({ top: "0", marginTop :"0px"}, 400);
		});
	}

	vui.alert = function (msg, title, callback) {

		if (typeof title == "function") {
			callback = title;
		} else if (typeof title == 'undefined') {
			title = "提示";
		}
		var shade=	$('<div class="vui-alert-shade vui-alert-show"></div').appendTo($("body"));
		var _alert = $('<div class="vui-alert"></div').appendTo(shade);
		var _title=$('<div class="vui-alert-title"><span class="vui-alert-title-text">' + title +':</span></div').appendTo(_alert);
		var _content=$('<div class="vui-alert-content"><span class="vui-alert-content-text">' + msg +'</span></div').appendTo(_alert);
		var _button = $('<div class="vui-alert-button"></div').appendTo(_alert);
		var yes = $('<span class="vui-alert-button-text">确定</span>').appendTo(_button);
		yes.on("click", new (function (s, cbk) {
			return {
				handler: function () {
					s.removeClass("vui-alert-show");
					s.addClass("vui-alert-hidden");
					s.one("animationend", function (event) {
						s.remove();
						cbk?.call();
					});

				}
			}
		})(shade, callback).handler);
	}

	vui.message = function (msg, title, type = 0, times = 1000, callback = undefined, inputValue = undefined) {

		var shade = $('<div class="vui-alert-shade vui-alert-show"></div').appendTo($("body"));
		var _alert = $('<div class="vui-alert"></div').appendTo(shade);
		var _title = $('<div class="vui-alert-title"><span class="vui-alert-title-text">' + title + ':</span></div').appendTo(_alert);
		var _content = $('<div class="vui-alert-content"><span class="vui-alert-content-text">' + msg + '</span></div').appendTo(_alert);
		switch (type) {
			case 0: {
				times = times || 1000;
				setTimeout(function () {
					shade.remove();
				}, times);
				break;
			}
			case 1: {
				var _input = $("<input id='text' class='vui-alert-input' type='text' />").appendTo(_alert);
                if (typeof inputValue !== 'undefined') {
                    _input.val(inputValue);
                }
				var _button = $('<div class="vui-alert-button"></div').appendTo(_alert);
				var yes = $('<span class="vui-alert-button-text">确定</span>').appendTo(_button);
				yes.on("click", function () {
					shade.removeClass("vui-alert-show");
					shade.addClass("vui-alert-hidden");
					shade.one("animationend", function (event) {
						shade.remove();
						if (typeof callback === 'function') {
							callback(_input.val());
						}
					});
				});
				break;
			}
		}
	}

	vui.confirm = function (msg, title, yesCallback, noCallback) {
		if (typeof title === 'function') {
			noCallback = yesCallback;
			yesCallback = title;
			title = "确认";
		} else if (typeof title === 'undefined') {
			title = "确认";
		}

		var shade = $('<div class="vui-alert-shade vui-alert-show"></div>').appendTo($("body"));
		var _alert = $('<div class="vui-alert"></div>').appendTo(shade);
		var _title = $('<div class="vui-alert-title"><span class="vui-alert-title-text">' + title + ':</span></div>').appendTo(_alert);
		var _content = $('<div class="vui-alert-content"><span class="vui-alert-content-text">' + msg + '</span></div>').appendTo(_alert);
		var _buttonContainer = $('<div class="vui-alert-button" style="justify-content: space-evenly;"></div>').appendTo(_alert);

		var yesBtn = $('<span class="vui-alert-button-text">确定</span>').appendTo(_buttonContainer);
		var noBtn = $('<span class="vui-alert-button-text">取消</span>').appendTo(_buttonContainer);

		function closeDialog() {
			shade.removeClass("vui-alert-show").addClass("vui-alert-hidden");
			shade.one("animationend", function () {
				shade.remove();
			});
		}

		yesBtn.on("click", function () {
			closeDialog();
			if (typeof yesCallback === 'function') {
				yesCallback();
			}
		});

		noBtn.on("click", function () {
			closeDialog();
			if (typeof noCallback === 'function') {
				noCallback();
			}
		});
	};

	vui.loadcss("panel");
	vui.loaded("panel");
})(vui);
