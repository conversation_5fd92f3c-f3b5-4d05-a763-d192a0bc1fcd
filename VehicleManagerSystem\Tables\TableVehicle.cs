﻿using System.ComponentModel.DataAnnotations;

namespace VehicleManagerSystem.Tables
{ 
   
    public enum VehicleType:byte
    {
        [Display(Name ="GENERAL_ALL")]
        None = 0, 
        [Display(Name = "VEHICLE_TYPE_HELI")]
        Heli = 1,
        [Display(Name = "VEHICLE_TYPE_QUAD")]
        Quad = 2,
        [Display(Name = "VEHICLE_TYPE_HEXA")]
        Hexa = 3, 
        [Display(Name = "VEHICLE_TYPE_QUAD_X2")]
        Quad_X2 = 4,
        [Display(Name = "VEHICLE_TYPE_HEXA_X2")]
        Hexa_X2 = 5, 
        [Display(Name = "VEHICLE_TYPE_UNKNOW")]
        Unknow=254,
        [Display(Name = "VEHICLE_TYPE_OTHER")]
        Other =255,
     
    }
    public enum VehicleStatus
    {
         [Display(Name ="GENERAL_ALL")]
        None,
        [Display(Name = "VEHICLE_STATUS_OFFLINE")]
        Offline,
        [Display(Name = "VEHICLE_STATUS_ONLINE")]
        Online,
         
    }
    public  enum CameraType
    {
         [Display(Name ="GENERAL_ALL")]
        None,
        
         
    }
    public class TableVehicle:ITable
    {
        public TableVehicle()
        {
            
        }
        public TableVehicle(string serial,string name)
        {
            VehicleSerial = serial;
            VehicleName = name;
        }
        [SqlSugar.SugarColumn(IsIdentity =true,ColumnDescription ="ID",IsPrimaryKey =true)]
        public int Id { get; set; }
        [SqlSugar.SugarColumn(ColumnDescription ="飞机名称")]
        public string VehicleName { get; set; } = null!;
        [SqlSugar.SugarColumn(ColumnDescription ="飞机型号",IsNullable =true)]
        public string VehicleModel { get; set; } = null!;
        [SqlSugar.SugarColumn(ColumnDescription ="飞机厂商",IsNullable =true)]
        public string VehicleManufacturer { get; set; } = null!; 
        [SqlSugar.SugarColumn(ColumnDescription = "飞机序列号", UniqueGroupNameList = ["SERIAL"])]
        public string VehicleSerial { get; set; } = null!;
        [SqlSugar.SugarColumn(ColumnDescription ="飞机类型")]
        public VehicleType VehicleType { get; set; } = VehicleType.Unknow;
        [SqlSugar.SugarColumn(ColumnDescription ="飞机状态")]
        public VehicleStatus VehicleStatus { get; set; } = VehicleStatus.Offline;

        [SqlSugar.SugarColumn(ColumnDescription ="纬度")]
        public double Latitude { get; set; } = 0.0;
        [SqlSugar.SugarColumn(ColumnDescription ="经度")]
        public double Longitude { get; set; } = 0.0;

        [SqlSugar.SugarColumn(ColumnDescription ="海拔")]
        public float Altitude { get; set; } = 0.0f;

        [SqlSugar.SugarColumn(ColumnDescription ="信号")]
        public int Rssi { get; set; } =0; 
        [SqlSugar.SugarColumn(ColumnDescription ="电池电量")]
        public float Battery { get; set; } =0;

        [SqlSugar.SugarColumn(ColumnDescription ="相机列表",IsJson =true)]
        public List<int> Camera { get; set; } = [];
        [SqlSugar.SugarColumn(ColumnDescription ="载荷列表",IsJson =true)]
        public List<int> Payload { get; set; } = [];

        [SqlSugar.SugarColumn(ColumnDescription ="所有者ID")]
        public int OwnerUid { get; set; } =0;

        [SqlSugar.SugarColumn(ColumnDescription = "通讯密钥", IsNullable = true)]
        public string CommunSecret { get; set; }
         
    }
}
