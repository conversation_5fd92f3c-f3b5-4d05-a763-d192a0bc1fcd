﻿namespace VehicleManagerSystem.Utils
{
    public class ApiResult
    {
        public static object Success(object? data=null)
        {
            if (data == null)
            {
                return new
                {
                    success = true,
                    code = 0
                };
            }
            else
            {
                return new
                {
                    success = true,
                    code = 0,
                    data
                };
            }
        }
        public static object Failed(string message)
        {
            return new
            {
                success = false,
                message,
            };
        }
    }
}
