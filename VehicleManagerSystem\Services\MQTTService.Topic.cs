﻿using System.Reflection;
using static VehicleManagerSystem.Services.MQTTService;

namespace VehicleManagerSystem.Services
{
    public partial class MQTTService
    {
        public abstract class ITopicDefine
        {
              public  abstract string Topic { get;  set; }

            public abstract ICommandExecuter GetCommand<T>(Func<T,string, Task<string>> mqtt_action) where T : IRemoteCommand;
           
        }
        public class TopicDefine<T>:ITopicDefine where T : IRemoteCommand  
        {
            public override string Topic { get;  set; } 
            private  string Format { get;  set; } 
            public TopicDefine(string topic,string format)
            {
                Topic = topic;
                Format=format;
               
            }
       

            public override ICommandExecuter GetCommand<TCommand>(Func<TCommand,string, Task<string>> mqtt_action)
            {
                if(mqtt_action is Func<T,string,Task<string>> mqtt_action_t)
                {
                     return new CommandExecuter<T>($"{Topic}{Format}", mqtt_action_t!);
                }
                else
                {
                    throw new NotImplementedException();
                }
                
            }
        }
        private static class TOPIC
        {
            public readonly  static  ITopicDefine TOPIC_VEHICLE_HEARTBEAT = new TopicDefine<HeartbeatCommand>("/vehicle/status/message/","{vehicle_sn}");
            public readonly  static  ITopicDefine TOPIC_VEHICLE_RESULT = new TopicDefine<ResultCommand>("/vehicle/control/response/","{vehicle_sn}/{client_sn}");
             
            public static void Each(Action<string> callback)
            {
                FieldInfo[] fields = typeof(TOPIC).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);
                foreach (FieldInfo field in fields)
                {
                    if (field.IsLiteral && !field.IsInitOnly)
                    {
                        var value = field.GetValue(null);
                        if (value != null && value is string val_str)
                        {
                            callback?.Invoke(val_str);
                        }
                    }
                }
            }

        }
    }
}
