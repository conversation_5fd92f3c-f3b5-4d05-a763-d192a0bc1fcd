﻿namespace VehicleManagerSystem.Tables
{
    public class TableVehicleAuthorize:ITable
    {
        [SqlSugar.SugarColumn(ColumnDescription = "ID", IsIdentity = true, IsPrimaryKey =true)]
        public int Id { get; set; }
        [SqlSugar.SugarColumn(ColumnDescription = "飞机ID",UniqueGroupNameList = ["AUTH"])]
        public int VehicleId {  get; set; }
        [SqlSugar.SugarColumn(ColumnDescription = "用户ID",UniqueGroupNameList = ["AUTH"])]
        public int UserUid {  get; set; }
    }
}
