﻿


layui.extend({
    
}).define(['jquery'], function (exports) {
    var $ = layui.$
        , admin = layui.admin
        , view = layui.view
    var ug_loading = null;
    $(window).bind('beforeunload', function () {
        const customEvent = new CustomEvent('begin_load', {
            bubbles: true,
            cancelable: false,
            detail: { message: '' }
        });
        document.dispatchEvent(customEvent);
    });
    $(document).ajaxComplete(function (event, xhr, settings) {
        
        if (settings.loading_index) {
            layer.close(settings.loading_index);
        }
        if (xhr.status === 200) {
            var resp = JSON.parse(xhr.responseText);
            if (resp && resp.success == false && resp.code == 40001) {
                if (typeof (settings.alert) == 'undefined' || settings.alert) {
                    localStorage.clear();
                    top.location.href = "/login";
                }
            } else if (resp.success == false) {
                if (!resp.message) {
                    (typeof (settings.alert) == 'undefined' || settings.alert) &&  layer.msg(get_error(resp.code), { icon: 2, time: 2000 }, function () { });
                } else{
                    (typeof (settings.alert) == 'undefined' || settings.alert) &&  layer.msg(resp.message, { icon: 2, time: 2000 }, function () { });
                }
            }
        } else {
            (typeof( settings.alert)=='undefined' || settings.alert )&& layer.msg("网络错误", { icon: 2, time: 2000 }, function () { });
        }
    }); 
    $.ajaxSetup({ 
        beforeSend: function (xhr, settings) {
            if (settings.loading) {
                settings.loading_index = layer.load(3); 
            }
            xhr.setRequestHeader('http_token', localStorage.getItem("token"));
            xhr.setRequestHeader('uid', localStorage.getItem("uid"));
            if (!settings.headers || settings.headers.file != "yes")
            xhr.setRequestHeader('content-type', "application/json");

        }
    });
    exports('logout', {  });
});
