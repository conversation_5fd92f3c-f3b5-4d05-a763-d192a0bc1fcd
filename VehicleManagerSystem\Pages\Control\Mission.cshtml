@page "/Control/Mission/{id}/{uid?}/{nonce?}/{token?}"
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Control.MissionModel
@{
    Layout = "_LayoutControl";
}

@section Styles {
    <link href="~/lib/cesium/Widgets/widgets.css" rel="stylesheet" />
}

<main class="main-view">
    <div id="map-view"></div>
    <div class="div-border"></div> <!-- 边框 -->
    <!-- 地图操作提示 -->
    <div class="map-tips-box">
        <div class="tips-header">
            <span>地图操作说明</span>
        </div>

        <div class="tips-content">
            <div class="tip-item">
                <span class="tip-key">左键单击</span>
                <span class="tip-value">选择航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键双击</span>
                <span class="tip-value">新增航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键拖动</span>
                <span class="tip-value">调整位置</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">滚轮</span>
                <span class="tip-value">地图缩放</span>
            </div>

        </div>
@* 
        <div class="tips-footer">
            <button class="btn-close-tips">关闭</button>
        </div> *@
    </div>
    <input type="text" id="missionName" style="display: none;" />
    <div id="editDrawer" class="drawer-panel">
        <div class="drawer-container">
            <div class="drawer-section">
                <div class="drawer-section-header">
                    <span>全局</span>
                </div>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label for="speed">速度(m/s)</label>
                        <input id="speed" type="number" value="10" min="0" step="10" />
                    </div>
                    <div class="form-group">
                        <label for="height">高度(m)</label>
                        <input id="height" type="number" min="0" step="0.1" />
                    </div>
                </div>
            </div>

            <div id="locationSection" class="drawer-section">
                <div class="drawer-section-header">
                    <span>位置</span>
                </div>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label>经度</label>
                        <span id="lngDisplay">--</span>
                    </div>
                    <div class="form-group">
                        <label>纬度</label>
                        <span id="latDisplay">--</span>
                    </div>
                </div>
            </div>
            </div>

            <div class="drawer-section">
                <div class="drawer-section-header"><span>操作</span></div>
                <div class="drawer-section-body">
                    <button id="deletePoint" class="layui-btn layui-btn-danger layui-btn-sm">
                        删除选中点
                    </button>
                    <div class="form-group-button">
                        <button id="saveMission" class="full-width-btn">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="drawer-panel" id="upload">
        <div class="drawer-section" >
            <div class="drawer-section-header"><span>操作</span></div>
            <div class="drawer-section-body">
                <div class="form-group-button">
                    <button id="uploadMission" class="full-width-btn">
                        推送任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="floating-mission-list">
        <div class="mission-list-header">
            <div class="mission-list-title">航线列表</div>
        </div>
        <ul id="floatingMissionList"></ul>
        <div class="mission-list-footer">
            <div class="addNewMissionBtnDiv">
                <button id="addNewMissionBtn">添加航线</button>
            </div>
        </div>
        <div class="mission-list-border-left-top"></div>
        <div class="mission-list-border-right-bottom"></div>
        <div class="end"></div>
    </div>
</main>

<div id="close" class="close-button" title="退出当前规划">
    <i class="iconfont icon-fanhui1"></i>
</div>

<script src="~/lib/cesium/Cesium.js"></script>
<script src="~/js/signalr.min.js"></script>
<script src="~/js/vui.js"></script>
<script src="~/lib/layuiadmin/layui/layui.js"></script>
@section Scripts {
    <script type="text/javascript">
        layui.use(['layer', 'form', 'jquery'], function () {
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.jquery;

            vui.use(["panel", "aim"], function () {

                // ========== 常量定义 ==========
                const POINT_PREFIX = 'point';           // 航点ID前缀
                const POINT_TYPE = 'custom-point';      // 航点类型标识
                const API_BASE = '/api/Mission';       // API基础路径
                const DEFAULT_SPEED = 10;               // 默认速度(m/s)
                const MAX_DRAG_DISTANCE = 50000;        // 最大拖拽距离限制

                // ========== 工具函数 ==========
                // 获取航点索引 - 从航点ID中提取数字索引
                function getPointIndex(id) {
                    return parseInt(id.replace(POINT_PREFIX, ''), 10);
                }
                // 显示消息提示
                function showMessage(message, type = 'info', duration = 1000) {
                    vui.message(message, type === 'error' ? '错误' : '提示', 0, duration);
                }

                // 计算航线总距离 - 使用球面距离公式
                function calculateDistance(points) {
                    if (!points || points.length < 2) return '0.00';
                    let totalDistance = 0;
                    for (let i = 1; i < points.length; i++) {
                        const p1 = points[i - 1];
                        const p2 = points[i];
                        const R = 6371e3; // 地球半径(米)
                        const φ1 = Cesium.Math.toRadians(p1.latitude);
                        const φ2 = Cesium.Math.toRadians(p2.latitude);
                        const Δφ = Cesium.Math.toRadians(p2.latitude - p1.latitude);
                        const Δλ = Cesium.Math.toRadians(p2.longitude - p1.longitude);
                        const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                                Math.cos(φ1) * Math.cos(φ2) *
                                Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                        totalDistance += R * c;
                    }
                    return (totalDistance / 1000).toFixed(2); // 返回公里数
                }

                // 获取作用域标识 - 用于动态绑定
                function getScoped($element) {
                    const attrs = Array.from($element[0].getAttributeNames());
                    return attrs.find(attr => attr.startsWith('b-')) || null;
                }

                // 更新航线列表最小高度 - 确保列表显示完整
               

                //  主对象 - 任务图层管理器
                const missionLayer = {
                    viewer: null,                    // Cesium视图对象
                    pointCounter: 0,                 // 航点计数器
                    pointData: [],                   // 航点数据数组
                    lineEntity: null,                // 航线实体
                    selectedEntity: null,            // 当前选中的航点实体
                    editingMissionId: null,          // 正在编辑的任务ID
                    isEditing: false,                // 是否处于编辑模式
                    loaded: false,                   // 地图是否已加载完成

                    // 渲染地图 - 初始化Cesium视图
                    render: function (e) {
                        try {
                            Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1ODJhMjA5Yy02OTU2LTQ4ZmUtYmU2Yi1hMDRlMDNlZjljZmEiLCJpZCI6ODk4NjksImlhdCI6MTY2MjY5NTEwOX0.Z7GZMmZ9sXH740jtl059KaViFXC1Nn0GIjYWyWLKTrg';

                            this.viewer = new Cesium.Viewer('map-view', {
                                terrain: Cesium.Terrain.fromWorldTerrain(),    // 使用世界地形
                                sceneMode: Cesium.SceneMode.SCENE2D,           // 2D模式
                                animation: false,                              // 禁用动画控件
                                timeline: false,                               // 禁用时间轴
                                fullscreenButton: false,                       // 禁用全屏按钮
                                infoBox: false,                                // 禁用信息框
                                geocoder: false,                               // 禁用地理编码器
                                homeButton: false,                             // 禁用主页按钮
                                sceneModePicker: false,                        // 禁用场景模式选择器
                                baseLayerPicker: false,                        // 禁用底图选择器
                                navigationHelpButton: false,                   // 禁用导航帮助按钮
                                selectionIndicator: false,                     // 禁用选择指示器
                                shouldAnimate: true,                           // 启用动画
                                enableRotate: false,                           // 禁用旋转
                                enableTilt: false,                             // 禁用倾斜
                                orderIndependentTranslucency: true,            // 启用独立透明度排序
                                contextOptions: { webgl: { alpha: true } }     // WebGL配置
                            });

                            e.viewer = this.viewer;
                            this.viewer.cesiumWidget.creditContainer.style.display = 'none';

                            // 相机heading重置 - 确保2D模式下相机朝向始终为正北
                            let headingResetTimeout;
                            this.viewer.scene.postRender.addEventListener(() => {
                                if (this.viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                                    const heading = this.viewer.camera.heading;
                                    if (Math.abs(heading) > 0.001) {
                                        clearTimeout(headingResetTimeout);
                                        headingResetTimeout = setTimeout(() => {
                                            this.viewer.camera.setView({ orientation: { heading: 0 } });
                                        }, 100);
                                    }
                                }
                            });
                            //抗锯齿
                            this.viewer.scene.postProcessStages.fxaa.enabled = true;

                            this.dragModule.init(this.viewer, this);
                            this.loaded = true;
                            e.loaded = true;

                        } catch (error) {
                            showMessage("地图初始化失败，请刷新页面重试。", 'error', 3000);
                        }
                    },

                    // 拖动模块 - 处理地图交互
                    dragModule: {
                        viewer: null,                    // Cesium视图引用
                        missionLayer: null,              // 任务图层引用
                        handler: null,                   // 事件处理器
                        isDragging: false,               // 是否正在拖拽
                        draggingEntity: null,            // 正在拖拽的实体
                        originalDragPosition: null,      // 拖拽开始时的原始位置

                        // 初始化拖拽模块
                        init: function (viewer, missionLayer) {
                            this.viewer = viewer;
                            this.missionLayer = missionLayer;
                            this.handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
                            this.setupEvents();

                            // 适配移动端长按新增航点
                            let touchTimer = null;
                            let lastTouchPos = null;

                            viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    lastTouchPos = {
                                        x: e.touches[0].clientX,
                                        y: e.touches[0].clientY
                                    };
                                    touchTimer = setTimeout(function () {
                                        // 长按1000ms触发新增航点
                                        const rect = viewer.canvas.getBoundingClientRect();
                                        const pos = {
                                            x: lastTouchPos.x - rect.left,
                                            y: lastTouchPos.y - rect.top
                                        };
                                        const cartesian = viewer.scene.pickPosition(pos);
                                        if (Cesium.defined(cartesian)) {
                                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                                            const newPosition = Cesium.Cartesian3.fromRadians(
                                                cartographic.longitude,
                                                cartographic.latitude,
                                                cartographic.height
                                            );
                                            const newId = 'point' + (++missionLayer.pointCounter);
                                            const newEntity = viewer.entities.add({
                                                id: newId,
                                                position: new Cesium.ConstantPositionProperty(newPosition),
                                                type: 'custom-point',
                                                point: {
                                                    color: Cesium.Color.fromCssColorString('#1976D2'),
                                                    pixelSize: 24,
                                                    outlineColor: Cesium.Color.fromCssColorString('#FF5722'),
                                                    outlineWidth: 3
                                                },
                                                label: {
                                                    text: String(missionLayer.pointData.length + 1),
                                                    font: '30px sans-serif', // 字体大小32px
                                                    scale: 0.5, // 缩小一半
                                                    fillColor: Cesium.Color.WHITE,
                                                    style: Cesium.LabelStyle.FILL,
                                                    verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                                    disableDepthTestDistance: Number.POSITIVE_INFINITY
                                                }
                                            });
                                            missionLayer.pointData.push({ id: newId, position: newPosition });
                                            missionLayer.updateLine();
                                            missionLayer.selectedEntity = newEntity;
                                            newEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');
                                            missionLayer.showPointDrawer(newEntity);
                                        }
                                    }, 1000); // 长按600ms
                                }
                            });

                            viewer.canvas.addEventListener('touchend', function (e) {
                                if (touchTimer) {
                                    clearTimeout(touchTimer);
                                    touchTimer = null;
                                    if (missionLayer.selectedEntity) {
                                        missionLayer.selectedEntity.point.color = Cesium.Color.fromCssColorString('#1976D2');
                                        missionLayer.selectedEntity = null;
                                        missionLayer.hidePointDrawer();
                                    }
                                }
                            });

                            // 适配触摸拖动航点
                            let draggingTouchId = null;
                            let draggingEntity = null;

                            viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    const touch = e.touches[0];
                                    const pos = { x: touch.clientX, y: touch.clientY };
                                    const rect = viewer.canvas.getBoundingClientRect();
                                    const pickPos = {
                                        x: pos.x - rect.left,
                                        y: pos.y - rect.top
                                    };
                                    const pickedObject = viewer.scene.pick(pickPos);
                                    if (pickedObject && pickedObject.id && pickedObject.id.type === 'custom-point') {
                                        draggingTouchId = touch.identifier;
                                        draggingEntity = pickedObject.id;
                                        viewer.scene.screenSpaceCameraController.enableTranslate = false;
                                        viewer.scene.screenSpaceCameraController.enableTilt = false;
                                    }
                                }
                            });

                            viewer.canvas.addEventListener('touchmove', function (e) {  
                                if (draggingTouchId !== null && draggingEntity) {
                                    for (let i = 0; i < e.touches.length; i++) {
                                        if (e.touches[i].identifier === draggingTouchId) {
                                            const touch = e.touches[i];
                                            const rect = viewer.canvas.getBoundingClientRect();
                                            const pos = {
                                                x: touch.clientX - rect.left,
                                                y: touch.clientY - rect.top
                                            };
                                            const cartesian = viewer.scene.pickPosition(pos);
                                            if (Cesium.defined(cartesian)) {
                                                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                                                const newPosition = Cesium.Cartesian3.fromRadians(
                                                    cartographic.longitude,
                                                    cartographic.latitude,
                                                    cartographic.height
                                                );
                                                draggingEntity.position = new Cesium.ConstantPositionProperty(newPosition);
                                                const index = missionLayer.pointData.findIndex(p => p.id === draggingEntity.id);
                                                if (index !== -1) {
                                                    missionLayer.pointData[index].position = newPosition;
                                                }
                                                $('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));
                                                $('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
                                                missionLayer.updateLine();
                                            }
                                            break;
                                        }
                                    }
                                }
                            });

                            viewer.canvas.addEventListener('touchend', function (e) {
                                if (draggingTouchId !== null) {
                                    viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                    viewer.scene.screenSpaceCameraController.enableTilt = true;
                                    draggingTouchId = null;
                                    draggingEntity = null;
                                }
                            });
                        },
                        // 设置事件监听
                        setupEvents() {
                            const self = this;

                            // 点选功能 - 左键单击选择航点
                            this.handler.setInputAction((click) => {
                                if (!self.missionLayer.isEditing) return;
                                const pickedObject = self.viewer.scene.pick(click.position);

                                if (pickedObject && pickedObject.id && pickedObject.id.type === POINT_TYPE) {
                                    const entity = pickedObject.id;
                                    if (self.missionLayer.selectedEntity !== entity) {
                                        // 重置之前选中航点的颜色
                                        if (self.missionLayer.selectedEntity) {
                                            self.missionLayer.selectedEntity.point.color = Cesium.Color.fromCssColorString('#1976D2');
                                        }
                                        // 设置新选中的航点
                                        self.missionLayer.selectedEntity = entity;
                                        entity.point.color = Cesium.Color.fromCssColorString('#2E7D32');
                                        self.missionLayer.showPointDrawer(entity);
                                    }
                                } else {
                                    // 点击空白区域，取消选择
                                    if (self.missionLayer.selectedEntity) {
                                        self.missionLayer.selectedEntity.point.color = Cesium.Color.fromCssColorString('#1976D2');
                                        self.missionLayer.selectedEntity = null;
                                        self.missionLayer.hidePointDrawer();
                                    }

                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

                            // 新增点功能 - 左键双击添加航点
                            this.handler.setInputAction((click) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.loaded) return;
                                const cartesian = self.viewer.scene.pickPosition(click.position);
                                if (!Cesium.defined(cartesian)) return;

                                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

                                const newPosition = Cesium.Cartesian3.fromRadians(
                                    cartographic.longitude,
                                    cartographic.latitude,
                                    cartographic.height.toFixed(2)
                                );

                                const newId = POINT_PREFIX + ++self.missionLayer.pointCounter;
                                const newEntity = self.viewer.entities.add({
                                    id: newId,
                                    position: new Cesium.ConstantPositionProperty(newPosition),
                                    type: POINT_TYPE,
                                    point: {
                                        color: Cesium.Color.fromCssColorString('#1976D2'),  // 深蓝色航点
                                        pixelSize: 24,
                                        outlineColor: Cesium.Color.fromCssColorString('#FF5722'),
                                        outlineWidth: 3
                                    },
                                    label: {
                                        text: String(self.missionLayer.pointData.length + 1),
                                        font: '30px sans-serif', // 字体大小32px
                                        scale: 0.5, // 缩小一半
                                        fillColor: Cesium.Color.WHITE,
                                        style: Cesium.LabelStyle.FILL,
                                        verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                        disableDepthTestDistance: Number.POSITIVE_INFINITY
                                    }
                                });

                                // 添加到数据数组并更新连线
                                self.missionLayer.pointData.push({ id: newId, position: newPosition });
                                self.missionLayer.updateLine();

                                // 自动选中新创建的航点
                                self.missionLayer.selectedEntity = newEntity;
                                newEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');
                                self.missionLayer.showPointDrawer(newEntity);
                            }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

                            // 开始拖拽 - 左键按下时触发
                            this.handler.setInputAction((movement) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.selectedEntity) return;
                                const pickedObject = self.viewer.scene.pick(movement.position);
                                if (pickedObject && pickedObject.id === self.missionLayer.selectedEntity) {
                                    self.startDrag(self.missionLayer.selectedEntity);
                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

                            // 拖拽过程 - 鼠标移动
                            this.handler.setInputAction((movement) => {
                                if (!this.isDragging || !this.draggingEntity) return;

                                const cartesian = this.viewer.scene.pickPosition(movement.endPosition);
                                if (!Cesium.defined(cartesian)) return;

                                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                                // 验证坐标有效性
                                if (isNaN(cartographic.longitude) || isNaN(cartographic.latitude)) return;

                                this.missionLayer.selectedEntity.position.getValue();
                                const newPosition = Cesium.Cartesian3.fromRadians(
                                    cartographic.longitude,
                                    cartographic.latitude,
                                    cartographic.height.toFixed(2)
                                );

                                // 检查拖拽距离限制
                                if (this.originalDragPosition) {
                                    const distance = Cesium.Cartesian3.distance(this.originalDragPosition, newPosition);
                                    if (distance > MAX_DRAG_DISTANCE) return;
                                }

                                // 更新航点位置
                                this.draggingEntity.position = new Cesium.ConstantPositionProperty(newPosition);
                                const index = this.missionLayer.pointData.findIndex(p => p.id === this.draggingEntity.id);
                                if (index !== -1) {
                                    this.missionLayer.pointData[index].position = newPosition;
                                }

                                // 实时更新UI
                                $('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));
                                $('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
                                this.missionLayer.updateLine();
                            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

                            // 结束拖拽 - 左键释放时触发
                            this.handler.setInputAction(() => {
                                this.endDrag();
                            }, Cesium.ScreenSpaceEventType.LEFT_UP);
                        },

                        // 开始拖拽 - 左键按下时触发
                        startDrag(entity) {
                            if (!entity) return;
                            this.draggingEntity = entity;
                            this.isDragging = true;
                            this.originalDragPosition = entity.position.getValue(this.viewer.clock.currentTime);
                            // 禁用相机平移和倾斜，防止拖拽时意外移动地图
                            this.viewer.scene.screenSpaceCameraController.enableTranslate = false;
                            this.viewer.scene.screenSpaceCameraController.enableTilt = false;
                            entity.point.color = Cesium.Color.fromCssColorString('#7B1FA2');
                        },

                        // 结束拖拽 - 左键释放时触发
                        endDrag() {
                            if (this.isDragging && this.draggingEntity) {
                                // 恢复相机控制
                                this.viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                this.viewer.scene.screenSpaceCameraController.enableTilt = true;
                                this.draggingEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');

                                // 简化：直接更新高度显示
                                const cartographic = Cesium.Cartographic.fromCartesian(this.draggingEntity.position.getValue());
                            }
                            this.isDragging = false;
                            this.draggingEntity = null;
                            this.originalDragPosition = null;
                        }
                    },

                    // 显示航点抽屉 - 显示选中航点的详细信息
                    showPointDrawer: function(entity) {
                        if (!entity || !entity.position) return;
                        const cartographic = Cesium.Cartographic.fromCartesian(entity.position.getValue());
                        $('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));
                        $('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));

                        $('#editDrawer').addClass('active').addClass('point-is-selected');
                        $('#upload').removeClass('active');
                    },

                    // 隐藏航点抽屉 - 清除航点详细信息显示
                    hidePointDrawer: function() {
                        $('#latDisplay').text('--');
                        $('#lngDisplay').text('--');
                        $('#editDrawer').removeClass('point-is-selected');
                        if (!this.isEditing) {
                            $('#editDrawer').removeClass('active');
                            $('#upload').addClass('active');
                        }
                    },

                    // 更新航线 - 根据航点数据绘制连线
                    updateLine: function () {
                        if (this.pointData.length < 2) {
                            // 航点少于2个时移除连线
                            if (this.lineEntity) {
                                this.viewer.entities.remove(this.lineEntity);
                                this.lineEntity = null;
                            }
                            return;
                        }

                        const positions = this.pointData.filter(p => p.position).map(p => p.position);
                        if (!this.lineEntity) {
                            // 创建新的连线实体
                            this.lineEntity = this.viewer.entities.add({
                                polyline: {
                                    positions: [...positions],
                                    width: 2,
                                    material: Cesium.Color.fromCssColorString('#FF5722'),
                                    clampToGround: true
                                }
                            });
                        } else {
                            // 更新现有连线位置
                            this.lineEntity.polyline.positions = [...positions];
                        }
                    },
                    // 更新航点标签 - 重新编号航点
                    updatePointLabels: function () {
                        const entities = this.viewer.entities.values;
                        const points = entities.filter(entity => entity.id && entity.id.startsWith(POINT_PREFIX));
                        points.sort((a, b) => getPointIndex(a.id) - getPointIndex(b.id));
                        points.forEach((entity, index) => {
                            entity.label.text = String(index + 1);
                        });
                    },

                    // 删除航点 - 移除选中的航点
                    removePoint: function () {
                        if (!this.selectedEntity) {
                            showMessage("请先点击地图上的点进行删除", 'error');
                            return;
                        }

                        vui.confirm("确定要删除这个点吗？", "删除确认", () => {
                            const entityId = this.selectedEntity.id;
                            this.viewer.entities.remove(this.selectedEntity);
                            const index = this.pointData.findIndex(p => p.id === entityId);
                            if (index !== -1) {
                                this.pointData.splice(index, 1);
                            }
                            this.selectedEntity = null;
                            this.hidePointDrawer();
                            this.updateLine();
                            this.updatePointLabels();
                            showMessage("航点已删除");
                        });
                    },

                    // 编辑任务 - 加载任务数据到地图
                    editMission: function(mission) {
                        this.editingMission = {
                            Id: mission.id,
                            speed: mission.speed,
                            pointData: mission.pointData
                        };

                        // 设置UI值
                        $('#speed').val(mission.speed || DEFAULT_SPEED);
                        $('#height').val(mission.height || 20);
                        $('#missionName').val(mission.missionName || '');

                        // 清理现有数据
                        this.pointData = [];
                        if (this.lineEntity) {
                            this.viewer.entities.remove(this.lineEntity);
                            this.lineEntity = null;
                        }
                        this.viewer.entities.removeAll();

                        // 创建航点实体
                        mission.pointData.forEach(p => {
                            const position = Cesium.Cartesian3.fromDegrees(p.longitude, p.latitude, p.altitude);
                            const newId = POINT_PREFIX + ++this.pointCounter;

                            this.viewer.entities.add({
                                id: newId,
                                position: new Cesium.ConstantPositionProperty(position),
                                type: POINT_TYPE,
                                point: {
                                    color: Cesium.Color.fromCssColorString('#1976D2'),  // 深蓝色航点
                                    pixelSize: 24,
                                    outlineColor: Cesium.Color.WHITE,
                                    outlineWidth: 3
                                },
                                label: {
                                    text: String(this.pointData.length + 1),
                                    font: '30px sans-serif', // 字体大小32px
                                    scale: 0.5, // 缩小一半
                                    fillColor: Cesium.Color.WHITE,
                                    style: Cesium.LabelStyle.FILL,
                                    verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                    disableDepthTestDistance: Number.POSITIVE_INFINITY
                                }
                            });

                            this.pointData.push({ id: newId, position });
                        });

                        this.updateLine();
                        this.updatePointLabels();

                        // 自动定位到航点区域
                        if (this.pointData.length > 0) {
                            this.flyToPoints(this.viewer, mission.pointData);
                        }
                    },

                    // 定位显示 - 自动调整相机视角以显示所有航点
                    flyToPoints: function(viewer, pointData) {
                        if (!pointData || pointData.length === 0) return;

                        const lons = pointData.map(p => p.longitude);
                        const lats = pointData.map(p => p.latitude);
                        const lonRange = Math.max(...lons) - Math.min(...lons);
                        const latRange = Math.max(...lats) - Math.min(...lats);

                        // 根据航点分布范围调整视角参数
                        let padding, zoomAmount, pitch;
                        if (lonRange < 0.005 && latRange < 0.005) {
                            // 小范围 - 近距离显示
                            padding = 0.001;
                            zoomAmount = 100;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.1;
                        } else if (lonRange < 0.1 && latRange < 0.1) {
                            // 中等范围 - 中距离显示
                            padding = 0.01;
                            zoomAmount = 500;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.2;
                        } else {
                            // 大范围 - 远距离显示
                            padding = 0.05;
                            zoomAmount = 1000;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.3;
                        }

                        viewer.camera.flyTo({
                            destination: Cesium.Rectangle.fromDegrees(
                                Math.min(...lons) - padding,
                                Math.min(...lats) - padding,
                                Math.max(...lons) + padding,
                                Math.max(...lats) + padding
                            ),
                            zoom: { amount: zoomAmount },
                            orientation: { heading: 0, pitch: pitch, roll: 0 },
                            duration: 2
                        });
                    },

                    // 加载任务详情 - 从服务器获取任务数据
                    loadMissionForEdit: function(missionId) {
                        $.ajax({
                            url: `${API_BASE}/detail`,
                            method: 'GET',
                            data: { id: missionId },
                            success: function (res) {
                                if (res.success && res.data) {
                                    const wasEditing = missionLayer.isEditing;
                                    const editingMissionId = missionLayer.editingMissionId;

                                    missionLayer.editMission(res.data);

                                    // 如果之前正在编辑此任务，保持编辑状态
                                    if (wasEditing && editingMissionId === missionId) {
                                        missionLayer.isEditing = true;
                                        missionLayer.editingMissionId = missionId;
                                        $('#editDrawer').addClass('active').removeClass('point-is-selected');

                                        const $editingLi = $('#floatingMissionList').find(`li[data-id="${missionId}"]`);
                                        if ($editingLi.length > 0) {
                                            $editingLi.attr('data-editing', 'true');
                                            $editingLi.find('.edit-btn').text('退出编辑');
                                        }
                                    }
                                } else {
                                    showMessage('加载任务失败', 'error');
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error('加载任务详情失败:', {xhr, status, error});
                                showMessage('网络错误，请重试', 'error');
                            }
                        });
                    },

                    // 加载任务列表 - 从服务器获取所有任务
                    loadMissionList: function() {
                        $.ajax({
                            url: `${API_BASE}/list`,
                            method: 'GET',
                            data: {
                                page: 1,
                                limit: 50,
                                'biz_context.VehicleId': @Model.id
                            },
                            success: function(res) {
                                if (res.success && res.data?.list?.length > 0) {
                                    const missionList = $('#floatingMissionList');
                                    missionList.empty();
                                    var scoped_symbol = getScoped($("#floatingMissionList"));
                                    res.data.list.forEach(m => {
                                        /*data-obj="${m.pointDataJson.replaceAll("\"","\'")}" */
                                        const li = `<li ${scoped_symbol} data-id="${m.id}" class="mission-item">
                                                 <div ${scoped_symbol} class="mission-header">
                                                    <span ${scoped_symbol} class="mission-name">${m.missionName}</span>
                                                    <span ${scoped_symbol} class="point-count">航点数: ${m.pointData.length}</span>
                                                </div>
                                                <div ${scoped_symbol} class="mission-footer">
                                                    <span ${scoped_symbol} class="distance">${calculateDistance(m.pointData)} km</span>
                                                    <span ${scoped_symbol} class="create-time">${new Date(m.createTime).toLocaleString()}</span>
                                                </div>
                                                <div ${scoped_symbol} class="drawer-buttons">
                                                    <button ${scoped_symbol} class="search-btn">查看</button>
                                                    <button ${scoped_symbol} class="edit-btn">编辑</button>
                                                    <button ${scoped_symbol} class="delete-btn">删除</button>
                                                </div>
                                            </li>`;
                                        missionList.append(li);
                                    });
                                } else {
                                    const scoped_symbol = getScoped($("#floatingMissionList")) || '';
                                    $('#floatingMissionList').html(`<li ${scoped_symbol} class="mission-item-empty">暂无任务</li>`);
                                }
                            },
                            error: function(xhr, status, error) {
                                showMessage("加载任务列表失败", 'error');
                            }
                        });
                    }
                };

                missionLayer.render(missionLayer);

                // ========== 事件绑定 ==========
                function bindMissionEvents() {
                    const $listContainer = $('#floatingMissionList');

                    // 任务项悬停效果 - 鼠标悬停时高亮显示
                    $listContainer.on('mouseenter', 'li', function () {
                        const $li = $(this);
                        if (!$li.hasClass('selected')) {
                            $li.addClass('hovered');
                        }
                    }).on('mouseleave', 'li', function () {
                        const $li = $(this);
                        if (!$li.hasClass('selected')) {
                            $li.removeClass('hovered');
                        }
                    });

                    // 切换任务函数 - 加载并显示指定任务
                    function switchToMission($li) {
                        // 设置当前选中状态
                        $('.mission-item').removeClass('selected').removeClass('hovered');
                        $li.addClass('selected').addClass('hovered');

                        // 加载任务数据
                        const missionId = $li.data('id');
                        $('#missionName').val($li.find('.mission-name').text().trim());
                        missionLayer.loadMissionForEdit(missionId);
                    }

                    $listContainer.off('click', '.search-btn').on('click', '.search-btn', function (e) {
                        e.stopPropagation();
                        const $clickedLi = $(this).closest("li");
                        const missionId = $clickedLi.data('id');
                        

                        missionLayer.isEditing = false;
                        $('#editDrawer').removeClass('active');
                        // 正常切换任务（无编辑状态）
                        switchToMission($clickedLi);
                        $('#upload').addClass('active');
                    })

                    // 任务项点击事件 - 处理任务选择和编辑状态切换
                    $listContainer.off('click', '.mission-item').on('click', '.mission-item', function (e) {
                        if (!$(e.target).closest('.drawer-buttons').length) {
                            const $clickedLi = $(this);
                            const $editingLi = $listContainer.find('li[data-editing="true"]');

                            // 如果正在编辑一个已存在的任务
                            if ($editingLi.length > 0) {
                                // 如果点击的是正在编辑的任务，则退出编辑
                                if ($editingLi.is($clickedLi)) {
                                    exitEditMode($editingLi, $editingLi.find('.edit-btn'));
                                    return;
                                }

                                // 如果点击的是其他任务，需要确认
                                vui.confirm("当前正在编辑任务，切换任务将放弃更改，确定要继续吗？", "退出编辑确认", function() {
                                    exitEditMode($editingLi, $editingLi.find('.edit-btn'), false); // 退出但不重载，因为马上要切换
                                    switchToMission($clickedLi);
                                });
                                return;
                            }

                            // 如果正在新建一个任务
                            if(missionLayer.isEditing) {
                                vui.confirm("当前正在新建任务，切换将放弃新建，确定吗？", "确认切换", function() {
                                    missionLayer.isEditing = false;
                                    $('#editDrawer').removeClass('active');
                                    switchToMission($clickedLi);
                                });
                                return;
                            }

                            // 正常切换任务（无编辑状态）
                            switchToMission($clickedLi);
                            $('#upload').addClass('active');
                        }
                    });

                    // 编辑按钮点击事件 - 进入或退出编辑模式
                    $listContainer.off('click', '.edit-btn').on('click', '.edit-btn', function (e) {
                        e.stopPropagation();

                        const $btn = $(this);
                        const $li = $btn.closest('li');
                        const missionId = $li.data('id');
                        $('.mission-item').removeClass('selected').removeClass('hovered');
                        $li.addClass('selected').addClass('hovered');

                        if ($li.attr('data-editing') === 'true') {
                            showMessage("已退出编辑任务模式");
                            exitEditMode($li, $btn);
                        } else {
                            enterEditMode($li, $btn, missionId);
                            showMessage("已进入编辑任务模式");
                        }
                    });

                    // 删除按钮点击事件 - 删除指定任务
                    $listContainer.off('click', '.delete-btn').on('click', '.delete-btn', function (e) {
                        e.stopPropagation();

                        const $btn = $(this);
                        const $li = $btn.closest('li');
                        const missionId = $li.data('id');
                        const missionName = $li.find('.mission-name').text().trim();

                        vui.confirm(`确定要删除任务 "${missionName}" 吗？`, '删除确认', function () {
                            $.ajax({
                                url: `${API_BASE}/delete`,
                                method: 'DELETE',
                                contentType: 'application/json',
                                data: JSON.stringify(missionId),
                                success: function (res) {
                                    if (res.success) {
                                        showMessage("删除成功");
                                        missionLayer.loadMissionList();

                                        // 如果删除的是正在编辑的任务，退出编辑模式
                                        if (missionLayer.editingMissionId === missionId) {
                                            missionLayer.isEditing = false;
                                            missionLayer.editingMissionId = null;
                                            $('#editDrawer').removeClass('active');
                                        }
                                        missionLayer.viewer.entities.removeAll();
                                        missionLayer.pointData = [];
                                        missionLayer.editingMissionId = null;
                                        missionLayer.selectedEntity = null;
                                        missionLayer.lineEntity = null;
                                    } else {
                                        showMessage(res.message || "删除失败", 'error');
                                    }
                                },
                                error: function (xhr, status, error) {
                                    showMessage('网络错误，请重试', 'error');
                                }
                            });
                        });
                    });
                }

                // 进入编辑模式 - 设置任务为可编辑状态
                function enterEditMode($li, $btn, missionId) {
                    const $listContainer = $('#floatingMissionList');
                    // 清除其他编辑状态 - 确保同时只有一个任务处于编辑状态
                    $listContainer.find('.mission-item').each(function() {
                        const $item = $(this);
                        if (!$item.is($li) && $item.attr('data-editing') === 'true') {
                            exitEditMode($item, $item.find('.edit-btn'),false);
                        }
                    });

                    // 设置当前任务编辑状态
                    $li.attr('data-editing', 'true');
                    $btn.text('退出编辑');

                    missionLayer.editingMissionId = missionId;
                    missionLayer.loadMissionForEdit(missionId);
                    missionLayer.isEditing = true;
                    $('#editDrawer').addClass('active').removeClass('point-is-selected');
                    $('#upload').removeClass('active');
                }

                // 退出编辑模式 - 恢复任务为只读状态
                function exitEditMode($li, $btn, isexit = true) {
                    if (!$li || !$btn) return;
                    $li.removeAttr('data-editing');
                    $btn.text('编辑');
                    missionLayer.isEditing = false;
                    $('#editDrawer').removeClass('active');
                    $('#upload').addClass('active');
                    var missionId = $li.data('id');
                    if (missionId && isexit) {
                        missionLayer.loadMissionForEdit(missionId);
                    }
                    missionLayer.hidePointDrawer(); // 该函数会清空高度等信息
                }

                // ========== 其他事件处理 ==========
                // 添加新任务 - 清空地图并进入新建模式
                $("#addNewMissionBtn").on("click", function () {
                    // 1. 如果当前在编辑一个任务，先退出
                    const $editingLi = $('#floatingMissionList').find('li[data-editing="true"]');
                    if ($editingLi.length > 0) {
                        exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                    }

                    // 2. 清理地图和数据
                    missionLayer.viewer.entities.removeAll();
                    missionLayer.pointData = [];
                    missionLayer.editingMissionId = null;
                    missionLayer.selectedEntity = null;
                    missionLayer.lineEntity = null;

                    // 3. 设置UI为新建模式
                    missionLayer.isEditing = true;
                    $('.mission-item').removeClass('selected').removeClass('hovered');
                    $('#editDrawer').addClass('active').removeClass('point-is-selected');
                    $('#speed').val(DEFAULT_SPEED);
                    $('#height').val(20);
                    $('#latDisplay').text('--');
                    $('#lngDisplay').text('--');
                    $('#missionName').val("");
                    $('#upload').removeClass('active');
                    showMessage("已进入新建任务模式，请在地图上添加航点");
                });

                $("#uploadMission").on("click", function () { 
                    var id = $("li.selected").data("id");
                    $.ajax({
                            url: `${API_BASE}/upload`,
                            method: 'GET',
                            data: { id: id },
                            success: function (res) {
                                if (res.success ) {
                                    showMessage("推送成功");
                                } else {
                                    showMessage(res.message || "推送失败", 'error');
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error('加载任务详情失败:', {xhr, status, error});
                                showMessage('网络错误，请重试', 'error');
                            }
                        });
                    
                });

                // 删除航点 - 移除当前选中的航点
                $("#deletePoint").on("click", function () {
                    missionLayer.removePoint();
                });

                // 关闭窗口 - 退出任务规划页面
                $("#close").on("click", function () {
                    vui.confirm("确定要退出规划吗？", "退出确认", () => {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                });

                // 保存任务 - 保存当前编辑的任务数据
                $("#saveMission").on("click", function () {
                    const vehicleId = @Model.id;
                    let missionName = $('#missionName').val()?.trim();
                    const description = 'Auto-generated mission';
                    const speed = parseFloat($('#speed').val()) || DEFAULT_SPEED;
                    const height = parseFloat($('#height').val()) || 20;
                    function doSave(missionName) {
                        if (!missionName) {
                            showMessage("任务名称不能为空", 'error');
                            return;
                        }
                        if (missionLayer.pointData.length === 0) {
                            showMessage("请添加至少一个航点", 'error');
                            return;
                        }

                        const missionId = missionLayer.editingMissionId;
                        // 转换航点数据格式 - 从Cesium坐标转换为经纬度
                        const points = missionLayer.pointData.map((p, index) => {
                            const cartographic = Cesium.Cartographic.fromCartesian(p.position);
                            return {
                                id: getPointIndex(p.id),
                                longitude: Cesium.Math.toDegrees(cartographic.longitude),
                                latitude: Cesium.Math.toDegrees(cartographic.latitude),
                                altitude: cartographic.height
                            };
                        });

                        // 构建保存数据
                        const payload = {
                            missionName,
                            vehicleId,
                            missionDescription: description,
                            pointData: points,
                            speed,
                            height
                        };
                        if (missionId) {
                            payload.Id = missionId;
                        }

                        const url = missionId ? `${API_BASE}/update` : `${API_BASE}/add`;

                        $.ajax({
                            url: url,
                            method: missionId ? 'PUT' : 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(payload),
                            success: function (res) {
                                if (res.success) {
                                    showMessage("保存成功");
                                    missionLayer.loadMissionList();

                                    if (!missionId) {
                                        // 成功添加新任务 - 退出新建模式
                                        missionLayer.editingMissionId = null;
                                        missionLayer.isEditing = false;
                                        $('#editDrawer').removeClass('active');
                                    } else {
                                        // 成功更新现有任务 - 自动退出编辑模式（修复后的关键逻辑）
                                        const $savedLi = $('#floatingMissionList').find(`li[data-id="${missionId}"]`);
                                        if ($savedLi.length > 0) {
                                            // 调用exitEditMode函数，确保UI状态正确更新
                                            exitEditMode($savedLi, $savedLi.find('.edit-btn'), true);
                                        } else {
                                            // 如果列表项未找到，重置状态
                                            missionLayer.isEditing = false;
                                            $('#editDrawer').removeClass('active');
                                        }
                                    }
                                } else {
                                    showMessage(res.message || "保存失败", 'error');
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error('保存任务 - 错误:', {xhr, status, error});
                                showMessage('网络错误，请重试', 'error');
                            }
                        });
                    }

                    // 如果任务名称为空，弹出输入框
                    if (!missionName) {
                        vui.message("","请填写任务名",1,0,function(s){
                            doSave(s && s.trim());
                        },missionName);
                        return;
                    }
                    doSave(missionName);
                });

                // ========== 初始化 ==========
                $(document).ready(function () {
                    missionLayer.loadMissionList();    // 加载任务列表
                    bindMissionEvents();               // 绑定任务相关事件
                    if (isMobileDevice()) {
                        // 修改地图操作说明为移动端
                        var scoped_symbol = getScoped($(".map-tips-box"));
                        $('.map-tips-box .tips-content').html(`
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">单指点击</span>
                                <span ${scoped_symbol} class="tip-value">选择航点</span>
                            </div>
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">长按</span>
                                <span ${scoped_symbol} class="tip-value">新增航点</span>
                            </div>
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">拖动航点</span>
                                <span ${scoped_symbol} class="tip-value">调整位置</span>
                            </div>
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">双指缩放</span>
                                <span ${scoped_symbol} class="tip-value">地图缩放</span>
                            </div>
                        `);
                    }
                    // 窗口大小变化时自动全屏
                    $(window).on('resize', function() {
                        try {
                            var index = parent.layer.getFrameIndex(window.name);
                            if (index) {
                                parent.layer.full(index);
                            }
                        } catch (e) {
                            console.error("Layer full screen failed on resize:", e);
                        }
                    });
                });
                function isMobileDevice() {
                    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
                        || ('ontouchstart' in window && navigator.maxTouchPoints > 0);
                }


                // 车辆面板相关变量（预留）
                var vehicle_panel = vui.Vehicle;
                var home_position = { lat: NaN, lng: NaN, alt: NaN };
                var home_position_raw = { lat: 0, lng: 0, alt: 0 };
                var home_valid = false;

            });
        });
    </script>
}