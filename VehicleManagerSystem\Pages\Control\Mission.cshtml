@page "/Control/Mission/{id}/{uid?}/{nonce?}/{token?}"
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Control.MissionModel
@{
    Layout = "_LayoutControl";
}

@section Styles {
    <link href="~/lib/cesium/Widgets/widgets.css" rel="stylesheet" />
}

<main class="main-view">
    <div id="map-view"></div>
    <div class="div-border"></div> <!-- 边框 -->
    <!-- 地图操作提示 -->
    <div class="map-tips-box">
        <div class="tips-header">
            <span>地图操作说明</span>
        </div>

        <div class="tips-content">
            <div class="tip-item">
                <span class="tip-key">左键单击</span>
                <span class="tip-value">选择航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键双击</span>
                <span class="tip-value">新增航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键拖动</span>
                <span class="tip-value">调整位置</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">滚轮</span>
                <span class="tip-value">地图缩放</span>
            </div>

        </div>
    </div>
    <input type="text" id="missionName" style="display: none;" />
    <div id="editDrawer" class="drawer-panel">
        <div class="drawer-container">
            <div class="drawer-section">
                <div class="drawer-section-header">
                    <span>全局</span>
                </div>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label for="speed">速度(m/s)</label>
                        <input id="speed" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" />
                    </div>
                    <div class="form-group">
                        <label for="height">高度(m)</label>
                        <input id="height" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" />
                    </div>
                </div>
            </div>

            <div id="locationSection" class="drawer-section">
                <div class="drawer-section-header">
                    <span>位置</span>
                </div>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label>经度</label>
                        <span id="lngDisplay">--</span>
                    </div>
                    <div class="form-group">
                        <label>纬度</label>
                        <span id="latDisplay">--</span>
                    </div>
                </div>
            </div>
            <div id="locationSectionOther" class="drawer-section">
                <div class="drawer-section-header">
                    <span>其他设置</span>
                </div>
                <div class="drawer-section-bodyother">
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther" class="custom-radio" />
                            <label for="isOther" class="custom-radio-label">速度</label>
                        </div>
                        <input id="speedOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther2" class="custom-radio" />
                            <label for="isOther2" class="custom-radio-label">高度</label>
                        </div>
                        <input id="heightOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther3" class="custom-radio" />
                            <label for="isOther3" class="custom-radio-label">航向</label>
                        </div>
                        <input id="headingOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                </div>
            </div>

            <div class="drawer-section">
                <div class="drawer-section-header"><span>操作</span></div>
                <div class="drawer-section-body">
                    <button id="deletePoint" class="layui-btn layui-btn-danger layui-btn-sm">
                        删除选中点
                    </button>
                    <div class="form-group-button">
                        <button id="saveMission" class="full-width-btn">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="drawer-panel" id="upload">
        <div class="drawer-section" >
            <div class="drawer-section-header"><span>操作</span></div>
            <div class="drawer-section-body">
                <div class="form-group-button">
                    <button id="uploadMission" class="full-width-btn">
                        推送任务
                    </button>
                </div>
                <div class="form-group-button">
                    <button id="uploadMissionStart" class="full-width-btn">
                        推送并执行任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="floating-mission-list">
        <div class="mission-list-header">
            <div class="mission-list-title">航线列表</div>
        </div>
        <ul id="floatingMissionList"></ul>
        <div class="mission-list-footer">
            <div class="addNewMissionBtnDiv">
                <button id="addNewMissionBtn">添加航线</button>
            </div>
        </div>
        <div class="mission-list-border-left-top"></div>
        <div class="mission-list-border-right-bottom"></div>
        <div class="end"></div>
    </div>
</main>

<div id="close" class="close-button" title="退出当前规划">
    <i class="iconfont icon-fanhui1"></i>
</div>

<script src="~/lib/cesium/Cesium.js"></script>
<script src="~/js/signalr.min.js"></script>
<script src="~/js/vui.js"></script>
<script src="~/lib/layuiadmin/layui/layui.js"></script>
@section Scripts {
    <script type="text/javascript">
        layui.use(['layer', 'form', 'jquery'], function () {
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.jquery;

            vui.use(["panel", "aim"], function () {

                // ========== 配置常量模块 ==========
                const CONFIG = {
                    WAYPOINT: {
                        PREFIX: 'point',
                        TYPE: 'custom-point',
                        DEFAULT_SPEED: 10,
                        DEFAULT_HEIGHT: 20,
                        MAX_DRAG_DISTANCE: 50000,
                        COLORS: {
                            NORMAL: '#1976D2',
                            SELECTED: '#2E7D32',
                            OUTLINE: '#FF5722'
                        }
                    },
                    API: {
                        BASE: '/api/Mission',
                        TIMEOUT: 30000
                    },
                    UI: {
                        MESSAGE_DURATION: 1000,
                        LONG_PRESS_DURATION: 1000,
                        DEBOUNCE_DELAY: 300
                    },
                    MAP: {
                        TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1ODJhMjA5Yy02OTU2LTQ4ZmUtYmU2Yi1hMDRlMDNlZjljZmEiLCJpZCI6ODk4NjksImlhdCI6MTY2MjY5NTEwOX0.Z7GZMmZ9sXH740jtl059KaViFXC1Nn0GIjYWyWLKTrg'
                    }
                };

                // ========== 工具函数模块 ==========
                const Utils = {
                    // 获取航点索引
                    getPointIndex(id) {
                        return parseInt(id.replace(CONFIG.WAYPOINT.PREFIX, ''), 10);
                    },

                    // 显示消息提示
                    showMessage(message, type = 'info', duration = CONFIG.UI.MESSAGE_DURATION) {
                        vui.message(message, type === 'error' ? '错误' : '提示', 0, duration);
                    },

                    // 计算航线总距离
                    calculateDistance(points) {
                        if (!points || points.length < 2) return '0.00';
                        let totalDistance = 0;
                        for (let i = 1; i < points.length; i++) {
                            const p1 = points[i - 1];
                            const p2 = points[i];
                            const R = 6371e3; // 地球半径(米)
                            const φ1 = Cesium.Math.toRadians(p1.latitude);
                            const φ2 = Cesium.Math.toRadians(p2.latitude);
                            const Δφ = Cesium.Math.toRadians(p2.latitude - p1.latitude);
                            const Δλ = Cesium.Math.toRadians(p2.longitude - p1.longitude);
                            const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                                    Math.cos(φ1) * Math.cos(φ2) *
                                    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                            totalDistance += R * c;
                        }
                        return (totalDistance / 1000).toFixed(2); // 返回公里数
                    },

                    // 获取作用域标识
                    getScoped($element) {
                        const attrs = Array.from($element[0].getAttributeNames());
                        return attrs.find(attr => attr.startsWith('b-')) || null;
                    },

                    // 防抖函数
                    debounce(func, wait) {
                        let timeout;
                        return function executedFunction(...args) {
                            const later = () => {
                                clearTimeout(timeout);
                                func(...args);
                            };
                            clearTimeout(timeout);
                            timeout = setTimeout(later, wait);
                        };
                    },

                    // 节流函数
                    throttle(func, limit) {
                        let inThrottle;
                        return function() {
                            const args = arguments;
                            const context = this;
                            if (!inThrottle) {
                                func.apply(context, args);
                                inThrottle = true;
                                setTimeout(() => inThrottle = false, limit);
                            }
                        }
                    }
                };

                // ========== 认证管理模块 ==========
                const AuthManager = {
                    // 获取认证头信息
                    getAuthHeaders() {
                        const uid = localStorage.getItem('uid');
                        const token = localStorage.getItem('token');

                        if (!uid || !token) {
                            return null;
                        }

                        return {
                            'uid': uid,
                            'token': token
                        };
                    },

                    // 创建带认证的AJAX请求
                    createAuthenticatedAjax(options) {
                        const authHeaders = this.getAuthHeaders();
                        if (!authHeaders) {
                            Utils.showMessage('用户未登录，请重新登录', 'error');
                            return Promise.reject('Authentication failed');
                        }

                        // 合并认证头到现有headers
                        const headers = Object.assign({}, options.headers || {}, authHeaders);

                        // 创建新的options对象，包含认证头和超时设置
                        const authenticatedOptions = Object.assign({}, options, {
                            headers: headers,
                            timeout: CONFIG.API.TIMEOUT
                        });

                        return $.ajax(authenticatedOptions);
                    }
                };

                // ========== DOM管理模块 ==========
                const DOMManager = {
                    cache: new Map(),

                    // 获取DOM元素（带缓存）
                    get(selector) {
                        if (!this.cache.has(selector)) {
                            this.cache.set(selector, $(selector));
                        }
                        return this.cache.get(selector);
                    },

                    // 批量更新DOM
                    batchUpdate(updates) {
                        const fragment = document.createDocumentFragment();
                        updates.forEach(update => {
                            if (update.element && update.content) {
                                update.element.html(update.content);
                            }
                        });
                    },

                    // 预加载关键元素
                    preloadElements() {
                        const selectors = [
                            '#editDrawer', '#upload', '#floatingMissionList',
                            '#addNewMissionBtn', '#saveMission', '#deletePoint',
                            '#uploadMission', '#uploadMissionStart',
                            '#latDisplay', '#lngDisplay', '#speedOther',
                            '#heightOther', '#headingOther', '#missionName'
                        ];

                        selectors.forEach(selector => this.get(selector));
                    }
                };

                // ========== UI管理模块 ==========
                const UIManager = {
                    drawer: {
                        // 显示编辑抽屉
                        showEditDrawer() {
                            DOMManager.get('#editDrawer').addClass('active');
                            DOMManager.get('#upload').removeClass('active');
                        },

                        // 隐藏编辑抽屉
                        hideEditDrawer() {
                            DOMManager.get('#editDrawer').removeClass('active');
                            DOMManager.get('#upload').addClass('active');
                        },

                        // 显示上传抽屉
                        showUploadDrawer() {
                            DOMManager.get('#upload').addClass('active');
                            DOMManager.get('#editDrawer').removeClass('active');
                        },

                        // 更新位置显示
                        updateLocationDisplay(lat, lng) {
                            DOMManager.get('#latDisplay').text(lat.toFixed(7));
                            DOMManager.get('#lngDisplay').text(lng.toFixed(7));
                        }
                    },

                    missionList: {
                        // 渲染任务列表
                        render(missions) {
                            const $container = DOMManager.get('#floatingMissionList');
                            if (!missions || missions.length === 0) {
                                $container.html('<li class="no-missions">暂无任务</li>');
                                return;
                            }

                            const html = missions.map(mission => `<li ${scoped_symbol} data-id="${m.id}" class="mission-item">
                                                 <div ${scoped_symbol} class="mission-header">
                                                    <span ${scoped_symbol} class="mission-name">${m.missionName}</span>
                                                    <span ${scoped_symbol} class="point-count">航点数: ${m.pointData.length}</span>
                                                </div>
                                                <div ${scoped_symbol} class="mission-footer">
                                                    <span ${scoped_symbol} class="distance">${calculateDistance(m.pointData)} km</span>
                                                    <span ${scoped_symbol} class="create-time">${new Date(m.createTime).toLocaleString()}</span>
                                                </div>
                                                <div ${scoped_symbol} class="drawer-buttons">
                                                    <button ${scoped_symbol} class="search-btn">查看</button>
                                                    <button ${scoped_symbol} class="edit-btn">编辑</button>
                                                    <button ${scoped_symbol} class="delete-btn">删除</button>
                                                </div>
                                            </li>`).join('');

                            $container.html(html);
                        },

                        // 选中任务项
                        selectMissionItem($item) {
                            DOMManager.get('.mission-item').removeClass('selected');
                            $item.addClass('selected');
                        },

                        // 清除所有编辑状态
                        clearAllEditingStates() {
                            DOMManager.get('.mission-item').removeAttr('data-editing');
                            DOMManager.get('.edit-btn').text('编辑');
                        }
                    },

                    form: {
                        // 重置任务表单
                        resetMissionForm() {
                            DOMManager.get('#missionName').val('');
                            DOMManager.get('#speed').val(CONFIG.WAYPOINT.DEFAULT_SPEED);
                            DOMManager.get('#height').val(CONFIG.WAYPOINT.DEFAULT_HEIGHT);
                            DOMManager.get('#speedOther').val('').prop('disabled', true);
                            DOMManager.get('#heightOther').val('').prop('disabled', true);
                            DOMManager.get('#headingOther').val('').prop('disabled', true);
                            DOMManager.get('#isOther, #isOther2, #isOther3').prop('checked', false);
                        },

                        // 更新航点表单
                        updateWaypointForm(waypoint) {
                            if (waypoint) {
                                DOMManager.get('#speedOther').val(waypoint.speed || '');
                                DOMManager.get('#heightOther').val(waypoint.height || '');
                                DOMManager.get('#headingOther').val(waypoint.heading || '');
                            }
                        }
                    }
                };

                // ========== 事件管理模块 ==========
                const EventManager = {
                    events: new Map(),

                    // 注册事件监听
                    on(eventName, callback) {
                        if (!this.events.has(eventName)) {
                            this.events.set(eventName, []);
                        }
                        this.events.get(eventName).push(callback);
                    },

                    // 移除事件监听
                    off(eventName, callback) {
                        if (this.events.has(eventName)) {
                            const callbacks = this.events.get(eventName);
                            const index = callbacks.indexOf(callback);
                            if (index > -1) {
                                callbacks.splice(index, 1);
                            }
                        }
                    },

                    // 触发事件
                    emit(eventName, data) {
                        if (this.events.has(eventName)) {
                            this.events.get(eventName).forEach(callback => {
                                try {
                                    callback(data);
                                } catch (error) {
                                    console.error(`Event callback error for ${eventName}:`, error);
                                }
                            });
                        }
                    },

                    // 防抖包装器
                    debounce: Utils.debounce,

                    // 节流包装器
                    throttle: Utils.throttle
                };

                // ========== 状态管理模块 ==========
                const StateManager = {
                    state: {
                        isEditing: false,
                        selectedWaypoint: null,
                        editingMissionId: null,
                        isDragging: false
                    },

                    // 设置状态
                    setState(key, value) {
                        const oldValue = this.state[key];
                        this.state[key] = value;

                        // 触发状态变化事件
                        EventManager.emit('stateChange', {
                            key,
                            oldValue,
                            newValue: value,
                            state: { ...this.state }
                        });
                    },

                    // 获取状态
                    getState(key) {
                        return key ? this.state[key] : { ...this.state };
                    },

                    // 重置状态
                    resetState() {
                        Object.keys(this.state).forEach(key => {
                            this.setState(key, false);
                        });
                    }
                };

                // ========== 地图管理模块 ==========
                const MapManager = {
                    // 初始化地图
                    async initialize(containerId) {
                        try {
                            Cesium.Ion.defaultAccessToken = CONFIG.MAP.TOKEN;

                            const viewer = new Cesium.Viewer(containerId, {
                                terrain: Cesium.Terrain.fromWorldTerrain(),
                                sceneMode: Cesium.SceneMode.SCENE2D,
                                animation: false,
                                timeline: false,
                                fullscreenButton: false,
                                infoBox: false,
                                geocoder: false,
                                homeButton: false,
                                sceneModePicker: false,
                                baseLayerPicker: false,
                                navigationHelpButton: false,
                                selectionIndicator: false,
                                shouldAnimate: true,
                                enableRotate: false,
                                enableTilt: false,
                                orderIndependentTranslucency: true,
                                contextOptions: { webgl: { alpha: true } }
                            });

                            // 隐藏版权信息
                            viewer.cesiumWidget.creditContainer.style.display = 'none';

                            // 设置相机heading重置
                            this.setupCameraHeadingReset(viewer);

                            // 启用抗锯齿
                            viewer.scene.postProcessStages.fxaa.enabled = true;

                            return viewer;
                        } catch (error) {
                            Utils.showMessage("地图初始化失败，请刷新页面重试。", 'error', 3000);
                            throw error;
                        }
                    },

                    // 设置相机heading重置
                    setupCameraHeadingReset(viewer) {
                        let headingResetTimeout;
                        viewer.scene.postRender.addEventListener(() => {
                            if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                                const heading = viewer.camera.heading;
                                if (Math.abs(heading) > 0.001) {
                                    clearTimeout(headingResetTimeout);
                                    headingResetTimeout = setTimeout(() => {
                                        viewer.camera.setView({ orientation: { heading: 0 } });
                                    }, 100);
                                }
                            }
                        });
                    }
                };

                // ========== 任务图层主模块 ==========
                const missionLayer = {
                    viewer: null,
                    pointCounter: 0,
                    pointData: [],
                    lineEntity: null,
                    selectedEntity: null,
                    editingMissionId: null,
                    isEditing: false,
                    loaded: false,

                    // 渲染地图
                    async render(container) {
                        try {
                            this.viewer = await MapManager.initialize('map-view');
                            container.viewer = this.viewer;

                            // 初始化拖拽模块
                            this.dragModule.init(this.viewer, this);

                            this.loaded = true;
                            container.loaded = true;

                            EventManager.emit('mapLoaded', { viewer: this.viewer });
                        } catch (error) {
                            console.error('地图渲染失败:', error);
                            throw error;
                        }
                    },

                    // 更新航线
                    updateLine() {
                        if (this.lineEntity) {
                            this.viewer.entities.remove(this.lineEntity);
                        }

                        if (this.pointData.length > 1) {
                            const positions = this.pointData.map(point => point.position);
                            this.lineEntity = this.viewer.entities.add({
                                polyline: {
                                    positions: positions,
                                    width: 3,
                                    material: Cesium.Color.fromCssColorString('#FF5722'),
                                    clampToGround: true
                                }
                            });
                        }
                    },

                    // 显示航点抽屉
                    showPointDrawer(entity) {
                        if (!entity) return;

                        const cartographic = Cesium.Cartographic.fromCartesian(entity.position.getValue());
                        UIManager.drawer.updateLocationDisplay(
                            Cesium.Math.toDegrees(cartographic.latitude),
                            Cesium.Math.toDegrees(cartographic.longitude)
                        );

                        UIManager.drawer.showEditDrawer();
                        DOMManager.get('#editDrawer').addClass('point-is-selected');

                        // 更新表单数据
                        const pointIndex = this.pointData.findIndex(p => p.id === entity.id);
                        if (pointIndex !== -1) {
                            UIManager.form.updateWaypointForm(this.pointData[pointIndex]);
                        }

                        StateManager.setState('selectedWaypoint', entity);
                    },

                    // 隐藏航点抽屉
                    hidePointDrawer() {
                        DOMManager.get('#editDrawer').removeClass('point-is-selected');
                        UIManager.drawer.updateLocationDisplay(0, 0);
                        StateManager.setState('selectedWaypoint', null);
                    },

                    // 创建航点实体
                    createWaypointEntity(id, position, index) {
                        return this.viewer.entities.add({
                            id: id,
                            position: new Cesium.ConstantPositionProperty(position),
                            type: CONFIG.WAYPOINT.TYPE,
                            point: {
                                color: Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL),
                                pixelSize: 24,
                                outlineColor: Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.OUTLINE),
                                outlineWidth: 3
                            },
                            label: {
                                text: String(index),
                                font: '30px sans-serif',
                                scale: 0.5,
                                fillColor: Cesium.Color.WHITE,
                                style: Cesium.LabelStyle.FILL,
                                verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                disableDepthTestDistance: Number.POSITIVE_INFINITY
                            }
                        });
                    },

                    // 选择航点
                    selectWaypoint(entity) {
                        // 重置之前选中的航点
                        if (this.selectedEntity && this.selectedEntity !== entity) {
                            this.selectedEntity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL);
                        }

                        // 设置新选中的航点
                        this.selectedEntity = entity;
                        if (entity) {
                            entity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.SELECTED);
                            this.showPointDrawer(entity);
                        } else {
                            this.hidePointDrawer();
                        }
                    },

                    // 清除所有航点
                    clearAllPoints() {
                        this.viewer.entities.removeAll();
                        this.pointData = [];
                        this.lineEntity = null;
                        this.selectedEntity = null;
                        this.pointCounter = 0;
                        this.hidePointDrawer();
                    },

                    // 拖拽模块
                    dragModule: {
                        viewer: null,
                        missionLayer: null,
                        handler: null,
                        isDragging: false,
                        draggingEntity: null,
                        originalDragPosition: null,

                        // 初始化拖拽模块
                        init(viewer, missionLayer) {
                            this.viewer = viewer;
                            this.missionLayer = missionLayer;
                            this.handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
                            this.setupEvents();
                            this.setupTouchEvents();
                        },

                        // 设置鼠标事件
                        setupEvents() {
                            const self = this;

                            // 点选功能 - 左键单击选择航点
                            this.handler.setInputAction((click) => {
                                if (!self.missionLayer.isEditing) return;
                                const pickedObject = self.viewer.scene.pick(click.position);

                                if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                                    const entity = pickedObject.id;
                                    self.missionLayer.selectWaypoint(entity);
                                } else {
                                    // 点击空白区域，取消选择
                                    self.missionLayer.selectWaypoint(null);
                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

                            // 新增点功能 - 左键双击添加航点
                            this.handler.setInputAction((click) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.loaded) return;
                                const cartesian = self.viewer.scene.pickPosition(click.position);
                                if (!Cesium.defined(cartesian)) return;

                                self.addWaypoint(cartesian);
                            }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

                            // 开始拖拽 - 左键按下时触发
                            this.handler.setInputAction((movement) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.selectedEntity) return;
                                const pickedObject = self.viewer.scene.pick(movement.position);
                                if (pickedObject && pickedObject.id === self.missionLayer.selectedEntity) {
                                    self.startDrag(self.missionLayer.selectedEntity);
                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

                            // 拖拽过程 - 鼠标移动
                            this.handler.setInputAction((movement) => {
                                if (!this.isDragging || !this.draggingEntity) return;

                                const cartesian = this.viewer.scene.pickPosition(movement.endPosition);
                                if (!Cesium.defined(cartesian)) return;

                                this.updateDragPosition(cartesian);
                            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

                            // 结束拖拽 - 左键释放时触发
                            this.handler.setInputAction(() => {
                                this.endDrag();
                            }, Cesium.ScreenSpaceEventType.LEFT_UP);
                        },

                        // 设置触摸事件
                        setupTouchEvents() {
                            const self = this;
                            let touchTimer = null;
                            let lastTouchPos = null;
                            let draggingTouchId = null;
                            let draggingEntity = null;

                            // 长按新增航点
                            this.viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!self.missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    lastTouchPos = {
                                        x: e.touches[0].clientX,
                                        y: e.touches[0].clientY
                                    };
                                    touchTimer = setTimeout(function () {
                                        const rect = self.viewer.canvas.getBoundingClientRect();
                                        const pos = {
                                            x: lastTouchPos.x - rect.left,
                                            y: lastTouchPos.y - rect.top
                                        };
                                        const cartesian = self.viewer.scene.pickPosition(pos);
                                        if (Cesium.defined(cartesian)) {
                                            self.addWaypoint(cartesian);
                                        }
                                    }, CONFIG.UI.LONG_PRESS_DURATION);
                                }
                            });

                            this.viewer.canvas.addEventListener('touchend', function (e) {
                                if (touchTimer) {
                                    clearTimeout(touchTimer);
                                    touchTimer = null;
                                }
                            });

                            // 触摸拖动航点
                            this.viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!self.missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    const touch = e.touches[0];
                                    const rect = self.viewer.canvas.getBoundingClientRect();
                                    const pickPos = {
                                        x: touch.clientX - rect.left,
                                        y: touch.clientY - rect.top
                                    };
                                    const pickedObject = self.viewer.scene.pick(pickPos);
                                    if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                                        draggingTouchId = touch.identifier;
                                        draggingEntity = pickedObject.id;
                                        self.viewer.scene.screenSpaceCameraController.enableTranslate = false;
                                        self.viewer.scene.screenSpaceCameraController.enableTilt = false;
                                    }
                                }
                            });

                            this.viewer.canvas.addEventListener('touchmove', function (e) {
                                if (draggingTouchId !== null && draggingEntity) {
                                    for (let i = 0; i < e.touches.length; i++) {
                                        if (e.touches[i].identifier === draggingTouchId) {
                                            const touch = e.touches[i];
                                            const rect = self.viewer.canvas.getBoundingClientRect();
                                            const pos = {
                                                x: touch.clientX - rect.left,
                                                y: touch.clientY - rect.top
                                            };
                                            const cartesian = self.viewer.scene.pickPosition(pos);
                                            if (Cesium.defined(cartesian)) {
                                                self.updateEntityPosition(draggingEntity, cartesian);
                                            }
                                            break;
                                        }
                                    }
                                }
                            });

                            this.viewer.canvas.addEventListener('touchend', function (e) {
                                if (draggingTouchId !== null) {
                                    self.viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                    self.viewer.scene.screenSpaceCameraController.enableTilt = true;
                                    draggingTouchId = null;
                                    draggingEntity = null;
                                }
                            });
                        },

                        // 添加航点
                        addWaypoint(cartesian) {
                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                            const newPosition = Cesium.Cartesian3.fromRadians(
                                cartographic.longitude,
                                cartographic.latitude,
                                parseFloat(cartographic.height.toFixed(2))
                            );

                            const newId = CONFIG.WAYPOINT.PREFIX + (++this.missionLayer.pointCounter);
                            const newEntity = this.missionLayer.createWaypointEntity(
                                newId,
                                newPosition,
                                this.missionLayer.pointData.length + 1
                            );

                            // 添加到数据数组并更新连线
                            this.missionLayer.pointData.push({
                                id: newId,
                                position: newPosition,
                                speed: CONFIG.WAYPOINT.DEFAULT_SPEED,
                                height: CONFIG.WAYPOINT.DEFAULT_HEIGHT,
                                heading: 0
                            });
                            this.missionLayer.updateLine();

                            // 自动选中新创建的航点
                            this.missionLayer.selectWaypoint(newEntity);
                        },

                        // 开始拖拽
                        startDrag(entity) {
                            this.isDragging = true;
                            this.draggingEntity = entity;
                            this.originalDragPosition = entity.position.getValue();
                            StateManager.setState('isDragging', true);
                        },

                        // 更新拖拽位置
                        updateDragPosition(cartesian) {
                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

                            // 验证坐标有效性
                            if (isNaN(cartographic.longitude) || isNaN(cartographic.latitude)) return;

                            const newPosition = Cesium.Cartesian3.fromRadians(
                                cartographic.longitude,
                                cartographic.latitude,
                                parseFloat(cartographic.height.toFixed(2))
                            );

                            // 检查拖拽距离限制
                            if (this.originalDragPosition) {
                                const distance = Cesium.Cartesian3.distance(this.originalDragPosition, newPosition);
                                if (distance > CONFIG.WAYPOINT.MAX_DRAG_DISTANCE) return;
                            }

                            this.updateEntityPosition(this.draggingEntity, newPosition);
                        },

                        // 更新实体位置
                        updateEntityPosition(entity, position) {
                            // 更新实体位置
                            entity.position = new Cesium.ConstantPositionProperty(position);

                            // 更新数据数组
                            const index = this.missionLayer.pointData.findIndex(p => p.id === entity.id);
                            if (index !== -1) {
                                this.missionLayer.pointData[index].position = position;
                            }

                            // 实时更新UI
                            const cartographic = Cesium.Cartographic.fromCartesian(position);
                            UIManager.drawer.updateLocationDisplay(
                                Cesium.Math.toDegrees(cartographic.latitude),
                                Cesium.Math.toDegrees(cartographic.longitude)
                            );

                            this.missionLayer.updateLine();
                        },

                        // 结束拖拽
                        endDrag() {
                            this.isDragging = false;
                            this.draggingEntity = null;
                            this.originalDragPosition = null;
                            StateManager.setState('isDragging', false);
                        }
                    },

                    // ========== 任务管理功能 ==========

                    // 开始编辑模式
                    startEditing(missionId = null) {
                        this.isEditing = true;
                        this.editingMissionId = missionId;
                        StateManager.setState('isEditing', true);
                        StateManager.setState('editingMissionId', missionId);
                        UIManager.drawer.showEditDrawer();
                    },

                    // 停止编辑模式
                    stopEditing() {
                        this.isEditing = false;
                        this.editingMissionId = null;
                        StateManager.setState('isEditing', false);
                        StateManager.setState('editingMissionId', null);
                        UIManager.drawer.hideEditDrawer();
                        this.selectWaypoint(null);
                    },

                    // 保存任务
                    async saveMission() {
                        try {
                            if (this.pointData.length === 0) {
                                Utils.showMessage('请添加至少一个航点', 'error');
                                return false;
                            }

                            let missionName = DOMManager.get('#missionName').val()?.trim();

                            // 如果任务名称为空，弹出输入框
                            if (!missionName) {
                                return new Promise((resolve) => {
                                    vui.message("", "请填写任务名", 1, 0, (inputName) => {
                                        if (inputName && inputName.trim()) {
                                            this.doSaveMission(inputName.trim()).then(resolve);
                                        } else {
                                            Utils.showMessage("任务名称不能为空", 'error');
                                            resolve(false);
                                        }
                                    }, missionName);
                                });
                            }

                            return await this.doSaveMission(missionName);
                        } catch (error) {
                            console.error('保存任务失败:', error);
                            Utils.showMessage('保存任务失败，请重试', 'error');
                            return false;
                        }
                    },

                    // 执行保存任务的具体逻辑
                    async doSaveMission(missionName) {
                        try {
                            const globalSpeed = parseInt(DOMManager.get('#speed').val()) || CONFIG.WAYPOINT.DEFAULT_SPEED;
                            const globalHeight = parseInt(DOMManager.get('#height').val()) || CONFIG.WAYPOINT.DEFAULT_HEIGHT;

                            // 构建任务数据
                            const missionData = {
                                missionId: this.editingMissionId,
                                missionName: missionName,
                                waypoints: this.pointData.map((point, index) => {
                                    const cartographic = Cesium.Cartographic.fromCartesian(point.position);
                                    return {
                                        sequence: index + 1,
                                        latitude: Cesium.Math.toDegrees(cartographic.latitude),
                                        longitude: Cesium.Math.toDegrees(cartographic.longitude),
                                        altitude: point.height || globalHeight,
                                        speed: point.speed || globalSpeed,
                                        heading: point.heading || 0
                                    };
                                })
                            };

                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/SaveMission`,
                                method: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify(missionData)
                            });

                            if (response.success) {
                                Utils.showMessage('任务保存成功');
                                EventManager.emit('missionSaved', { missionId: response.data?.missionId });
                                return true;
                            } else {
                                Utils.showMessage(response.message || '保存失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('保存任务失败:', error);
                            Utils.showMessage('保存任务失败，请重试', 'error');
                            return false;
                        }
                    },

                    // 加载任务
                    async loadMission(missionId) {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/GetMissionDetail`,
                                method: 'GET',
                                data: { missionId: missionId }
                            });

                            if (response.success && response.data) {
                                this.loadMissionData(response.data);
                                this.startEditing(missionId);
                                return response.data;
                            } else {
                                Utils.showMessage(response.message || '加载任务失败', 'error');
                                return null;
                            }
                        } catch (error) {
                            console.error('加载任务失败:', error);
                            Utils.showMessage('加载任务失败，请重试', 'error');
                            return null;
                        }
                    },

                    // 加载任务数据到地图
                    loadMissionData(missionData) {
                        // 清除现有数据
                        this.clearAllPoints();

                        // 设置任务信息
                        DOMManager.get('#missionName').val(missionData.missionName || '');
                        DOMManager.get('#speed').val(missionData.defaultSpeed || CONFIG.WAYPOINT.DEFAULT_SPEED);
                        DOMManager.get('#height').val(missionData.defaultHeight || CONFIG.WAYPOINT.DEFAULT_HEIGHT);

                        // 加载航点
                        if (missionData.waypoints && missionData.waypoints.length > 0) {
                            missionData.waypoints.forEach((waypoint, index) => {
                                const position = Cesium.Cartesian3.fromDegrees(
                                    waypoint.longitude,
                                    waypoint.latitude,
                                    waypoint.altitude || CONFIG.WAYPOINT.DEFAULT_HEIGHT
                                );

                                const waypointId = CONFIG.WAYPOINT.PREFIX + (++this.pointCounter);
                                const entity = this.createWaypointEntity(waypointId, position, index + 1);

                                this.pointData.push({
                                    id: waypointId,
                                    position: position,
                                    speed: waypoint.speed || CONFIG.WAYPOINT.DEFAULT_SPEED,
                                    height: waypoint.altitude || CONFIG.WAYPOINT.DEFAULT_HEIGHT,
                                    heading: waypoint.heading || 0
                                });
                            });

                            this.updateLine();
                        }
                    },

                    // 删除任务
                    async deleteMission(missionId, missionName = '') {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/DeleteMission`,
                                method: 'POST',
                                data: { missionId: missionId }
                            });

                            if (response.success) {
                                const message = missionName ? `任务 "${missionName}" 删除成功` : '任务删除成功';
                                Utils.showMessage(message);
                                EventManager.emit('missionDeleted', { missionId });

                                // 如果删除的是当前编辑的任务，清除编辑状态
                                if (this.editingMissionId === missionId) {
                                    this.clearAllPoints();
                                    this.stopEditing();
                                }
                                return true;
                            } else {
                                Utils.showMessage(response.message || '删除失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('删除任务失败:', error);
                            Utils.showMessage('删除任务失败，请重试', 'error');
                            return false;
                        }
                    },

                    // 删除选中的航点
                    removeSelectedPoint() {
                        if (!this.selectedEntity) {
                            Utils.showMessage('请先选择要删除的航点', 'error');
                            return;
                        }

                        // 添加确认对话框，与 Mission copy.cshtml 保持一致
                        vui.confirm("确定要删除这个点吗？", "删除确认", () => {
                            const entityId = this.selectedEntity.id;
                            const index = this.pointData.findIndex(p => p.id === entityId);

                            if (index !== -1) {
                                // 从数据数组中移除
                                this.pointData.splice(index, 1);

                                // 从地图中移除实体
                                this.viewer.entities.remove(this.selectedEntity);

                                // 重新编号剩余航点
                                this.renumberWaypoints();

                                // 更新连线
                                this.updateLine();

                                // 清除选择状态
                                this.selectWaypoint(null);

                                Utils.showMessage('航点删除成功');
                            }
                        });
                    },

                    // 重新编号航点
                    renumberWaypoints() {
                        this.pointData.forEach((point, index) => {
                            const entity = this.viewer.entities.getById(point.id);
                            if (entity && entity.label) {
                                entity.label.text = String(index + 1);
                            }
                        });
                    },

                    // 上传任务
                    async uploadMission(executeImmediately = false) {
                        try {
                            if (!this.editingMissionId) {
                                Utils.showMessage('请先选择一个任务', 'error');
                                return false;
                            }

                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/UploadMission`,
                                method: 'POST',
                                data: {
                                    missionId: this.editingMissionId,
                                    executeImmediately: executeImmediately
                                }
                            });

                            if (response.success) {
                                const message = executeImmediately ? '任务推送并执行成功' : '任务推送成功';
                                Utils.showMessage(message);
                                return true;
                            } else {
                                Utils.showMessage(response.message || '推送失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('上传任务失败:', error);
                            Utils.showMessage('上传任务失败，请重试', 'error');
                            return false;
                        }
                    }
                };

                // ========== 任务列表管理 ==========
                const MissionListManager = {
                    missions: [],

                    // 加载任务列表
                    async loadMissionList() {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/GetMissionList`,
                                method: 'GET'
                            });

                            if (response.success && response.data) {
                                this.missions = response.data;
                                UIManager.missionList.render(this.missions);
                                return this.missions;
                            } else {
                                Utils.showMessage(response.message || '加载任务列表失败', 'error');
                                return [];
                            }
                        } catch (error) {
                            console.error('加载任务列表失败:', error);
                            Utils.showMessage('加载任务列表失败，请重试', 'error');
                            return [];
                        }
                    },

                    // 刷新任务列表
                    async refreshMissionList() {
                        return await this.loadMissionList();
                    },

                    // 获取任务详情
                    getMissionById(missionId) {
                        return this.missions.find(m => m.missionId === missionId);
                    }
                };

                // ========== 事件绑定和初始化 ==========
                const AppController = {
                    // 初始化应用
                    async init() {
                        try {
                            // 预加载DOM元素
                            DOMManager.preloadElements();

                            // 初始化地图
                            await missionLayer.render(this);

                            // 绑定事件
                            this.bindEvents();

                            // 加载任务列表
                            await MissionListManager.loadMissionList();

                            // 设置状态监听
                            this.setupStateListeners();

                            Utils.showMessage('系统初始化完成');
                        } catch (error) {
                            console.error('应用初始化失败:', error);
                            Utils.showMessage('系统初始化失败，请刷新页面重试', 'error');
                        }
                    },

                    // 绑定事件
                    bindEvents() {
                        // 新建任务按钮
                        DOMManager.get('#addNewMissionBtn').on('click', () => {
                            missionLayer.clearAllPoints();
                            UIManager.form.resetMissionForm();
                            missionLayer.startEditing();
                            Utils.showMessage("已进入新建任务模式，请在地图上添加航点");
                        });

                        // 保存任务按钮
                        DOMManager.get('#saveMission').on('click', async () => {
                            const success = await missionLayer.saveMission();
                            if (success) {
                                await MissionListManager.refreshMissionList();
                            }
                        });

                        // 删除航点按钮
                        DOMManager.get('#deletePoint').on('click', () => {
                            missionLayer.removeSelectedPoint();
                        });

                        // 上传任务按钮
                        DOMManager.get('#uploadMission').on('click', async () => {
                            await missionLayer.uploadMission(false);
                        });

                        // 上传并执行任务按钮
                        DOMManager.get('#uploadMissionStart').on('click', async () => {
                            await missionLayer.uploadMission(true);
                        });

                        // 任务列表事件委托
                        DOMManager.get('#floatingMissionList').on('click', '.edit-btn', async (e) => {
                            e.stopPropagation();
                            const $item = $(e.target).closest('.mission-item');
                            const missionId = $item.data('id');

                            if ($item.attr('data-editing') === 'true') {
                                // 停止编辑
                                missionLayer.stopEditing();
                                UIManager.missionList.clearAllEditingStates();
                            } else {
                                // 开始编辑
                                UIManager.missionList.clearAllEditingStates();
                                $item.attr('data-editing', 'true');
                                $(e.target).text('停止编辑');

                                await missionLayer.loadMission(missionId);
                                UIManager.missionList.selectMissionItem($item);
                            }
                        });

                        // 删除任务按钮
                        DOMManager.get('#floatingMissionList').on('click', '.delete-btn', async (e) => {
                            e.stopPropagation();
                            const $item = $(e.target).closest('.mission-item');
                            const missionId = $item.data('id');
                            const missionName = $item.find('h4').text();

                            vui.confirm(`确定要删除任务"${missionName}"吗？`, '确认删除', async () => {
                                const success = await missionLayer.deleteMission(missionId, missionName);
                                if (success) {
                                    await MissionListManager.refreshMissionList();
                                }
                            });
                        });

                        // 表单变化事件
                        DOMManager.get('#isOther').on('change', function() {
                            DOMManager.get('#speedOther').prop('disabled', !this.checked);
                        });

                        DOMManager.get('#isOther2').on('change', function() {
                            DOMManager.get('#heightOther').prop('disabled', !this.checked);
                        });

                        DOMManager.get('#isOther3').on('change', function() {
                            DOMManager.get('#headingOther').prop('disabled', !this.checked);
                        });

                        // 航点参数更新事件（防抖处理）
                        const updateWaypointParams = Utils.debounce(() => {
                            if (missionLayer.selectedEntity) {
                                const entityId = missionLayer.selectedEntity.id;
                                const index = missionLayer.pointData.findIndex(p => p.id === entityId);
                                if (index !== -1) {
                                    const speedOther = DOMManager.get('#speedOther').val();
                                    const heightOther = DOMManager.get('#heightOther').val();
                                    const headingOther = DOMManager.get('#headingOther').val();

                                    if (speedOther) missionLayer.pointData[index].speed = parseInt(speedOther);
                                    if (heightOther) missionLayer.pointData[index].height = parseInt(heightOther);
                                    if (headingOther) missionLayer.pointData[index].heading = parseInt(headingOther);
                                }
                            }
                        }, CONFIG.UI.DEBOUNCE_DELAY);

                        DOMManager.get('#speedOther, #heightOther, #headingOther').on('input', updateWaypointParams);
                    },

                    // 设置状态监听
                    setupStateListeners() {
                        EventManager.on('stateChange', (data) => {
                            console.log('State changed:', data);
                        });

                        EventManager.on('mapLoaded', (data) => {
                            console.log('Map loaded successfully');
                        });

                        EventManager.on('missionSaved', async (data) => {
                            await MissionListManager.refreshMissionList();
                        });

                        EventManager.on('missionDeleted', async (data) => {
                            await MissionListManager.refreshMissionList();
                        });
                    }
                };

                // ========== 应用启动 ==========
                $(document).ready(async function() {
                    try {
                        await AppController.init();
                    } catch (error) {
                        console.error('应用启动失败:', error);
                        Utils.showMessage('应用启动失败，请刷新页面重试', 'error');
                    }
                });

            });
        });
    </script>
}