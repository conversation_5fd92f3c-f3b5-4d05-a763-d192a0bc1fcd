@page "/Control/Mission/{id}/{uid?}/{nonce?}/{token?}"
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Control.MissionModel
@{
    Layout = "_LayoutControl";
}

@section Styles {
    <link href="~/lib/cesium/Widgets/widgets.css" rel="stylesheet" />
}

<main class="main-view">
    <div id="map-view"></div>
    <div class="div-border"></div> <!-- 边框 -->
    <!-- 地图操作提示 -->
    <div class="map-tips-box">
        <div class="tips-header">
            <span>地图操作说明</span>
        </div>

        <div class="tips-content">
            <div class="tip-item">
                <span class="tip-key">左键单击</span>
                <span class="tip-value">选择航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键双击</span>
                <span class="tip-value">新增航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键拖动</span>
                <span class="tip-value">调整位置</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">滚轮</span>
                <span class="tip-value">地图缩放</span>
            </div>

        </div>
    </div>
    <input type="text" id="missionName" style="display: none;" />
    <!-- 编辑抽屉面板 -->
    <aside id="editDrawer" class="edit-drawer-panel" role="complementary" aria-labelledby="edit-drawer-title">
        <div class="drawer-container">
            <!-- 全局设置区域 -->
            <section class="drawer-section global-settings" aria-labelledby="global-settings-title">
                <header class="drawer-section-header">
                    <h4 id="global-settings-title">全局设置</h4>
                </header>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label for="speed" class="form-label">速度 (m/s)</label>
                        <input id="speed" type="number" class="form-input" min="0" step="0.1"
                               placeholder="请输入速度" aria-describedby="speed-help" />
                        <small id="speed-help" class="form-help">设置任务执行的默认速度</small>
                    </div>
                    <div class="form-group">
                        <label for="height" class="form-label">高度 (m)</label>
                        <input id="height" type="number" class="form-input" min="0" step="0.1"
                               placeholder="请输入高度" aria-describedby="height-help" />
                        <small id="height-help" class="form-help">设置任务执行的默认高度</small>
                    </div>
                </div>
            </section>

            <!-- 位置信息区域 -->
            <section id="locationSection" class="drawer-section location-info" aria-labelledby="location-title">
                <header class="drawer-section-header">
                    <h4 id="location-title">位置信息</h4>
                </header>
                <div class="drawer-section-body">
                    <div class="form-group readonly-group">
                        <label class="form-label">经度</label>
                        <span id="lngDisplay" class="coordinate-display" aria-label="当前选中航点经度">--</span>
                    </div>
                    <div class="form-group readonly-group">
                        <label class="form-label">纬度</label>
                        <span id="latDisplay" class="coordinate-display" aria-label="当前选中航点纬度">--</span>
                    </div>
                </div>
            </section>
            <div id="locationSectionOther" class="drawer-section">
                <div class="drawer-section-header">
                    <span>其他设置</span>
                </div>
                <div class="drawer-section-bodyother">
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther" class="custom-radio" />
                            <label for="isOther" class="custom-radio-label">速度</label>
                        </div>
                        <input id="speedOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther2" class="custom-radio" />
                            <label for="isOther2" class="custom-radio-label">高度</label>
                        </div>
                        <input id="heightOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther3" class="custom-radio" />
                            <label for="isOther3" class="custom-radio-label">航向</label>
                        </div>
                        <input id="headingOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                </div>
            </div>

            <div class="drawer-section">
                <div class="drawer-section-header"><span>操作</span></div>
                <div class="drawer-section-body">
                    <button id="deletePoint" class="layui-btn layui-btn-danger layui-btn-sm">
                        删除选中点
                    </button>
                    <div class="form-group-button">
                        <button id="saveMission" class="full-width-btn">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="drawer-panel" id="upload">
        <div class="drawer-section" >
            <div class="drawer-section-header"><span>操作</span></div>
            <div class="drawer-section-body">
                <div class="form-group-button">
                    <button id="uploadMission" class="full-width-btn">
                        推送任务
                    </button>
                </div>
                <div class="form-group-button">
                    <button id="uploadMissionStart" class="full-width-btn">
                        推送并执行任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="floating-mission-list">
        <div class="mission-list-header">
            <div class="mission-list-title">航线列表</div>
        </div>
        <ul id="floatingMissionList"></ul>
        <div class="mission-list-footer">
            <div class="addNewMissionBtnDiv">
                <button id="addNewMissionBtn">添加航线</button>
            </div>
        </div>
        <div class="mission-list-border-left-top"></div>
        <div class="mission-list-border-right-bottom"></div>
        <div class="end"></div>
    </div>
</main>

<div id="close" class="close-button" title="退出当前规划">
    <i class="iconfont icon-fanhui1"></i>
</div>

<script src="~/lib/cesium/Cesium.js"></script>
<script src="~/js/signalr.min.js"></script>
<script src="~/js/vui.js"></script>
<script src="~/lib/layuiadmin/layui/layui.js"></script>
@section Scripts {
    <script type="text/javascript">
        /*
         * ========== 任务管理系统 - 模块化架构版本 ==========
         *
         * 架构特性：
         * 1. 模块化设计 - 功能模块分离，职责清晰
         * 2. 状态管理 - 统一的应用状态管理
         * 3. 事件驱动 - 模块间通过事件通信
         * 4. 性能优化 - 防抖节流、批量操作、内存管理
         * 5. 调试支持 - 完善的日志和性能监控
         * 6. 向后兼容 - 保持原有API接口
         *
         * 模块结构：
         * - CONFIG: 全局配置管理
         * - Utils: 通用工具函数集合
         * - PerformanceHelper: 性能优化工具
         * - DebugHelper: 调试和日志工具
         * - UIManager: UI界面管理模块
         * - StateManager: 应用状态管理模块
         * - MissionDataManager: 任务数据管理模块
         * - EventManager: 事件处理模块
         * - WaypointManager: 航点管理模块
         * - ModuleManager: 模块统一管理器
         * - missionLayer: 任务图层管理器（集成所有模块）
         */

        layui.use(['layer', 'form', 'jquery'], function () {
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.jquery;

            vui.use(["panel", "aim"], function () {

                // ========== 应用配置 ==========
                const CONFIG = {
                    // API配置
                    API: {
                        BASE_URL: '/api/Mission',
                        ENDPOINTS: {
                            LIST: '/list',
                            DETAIL: '/detail',
                            ADD: '/add',
                            UPDATE: '/update',
                            DELETE: '/delete',
                            UPLOAD: '/upload'
                        }
                    },

                    // 航点配置
                    WAYPOINT: {
                        ID_PREFIX: 'point',
                        TYPE: 'custom-point',
                        DEFAULT_SPEED: 10,          // m/s
                        DEFAULT_HEIGHT: 20,         // m
                        DEFAULT_HEADING: 0xFFFF,    // 默认航向
                        MAX_DRAG_DISTANCE: 50000,   // 最大拖拽距离(m)
                        COLORS: {
                            NORMAL: '#1976D2',      // 普通航点颜色
                            SELECTED: '#2E7D32',    // 选中航点颜色
                            HOVER: '#FF9800'        // 悬停航点颜色
                        }
                    },

                    // 地图配置
                    MAP: {
                        CESIUM_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1ODJhMjA5Yy02OTU2LTQ4ZmUtYmU2Yi1hMDRlMDNlZjljZmEiLCJpZCI6ODk4NjksImlhdCI6MTY2MjY5NTEwOX0.Z7GZMmZ9sXH740jtl059KaViFXC1Nn0GIjYWyWLKTrg',
                        CONTAINER_ID: 'map-view',
                        LINE_COLOR: '#FF6B35',      // 航线颜色
                        LINE_WIDTH: 3,              // 航线宽度
                        CAMERA_RESET_DELAY: 100     // 相机重置延迟(ms)
                    },

                    // UI配置
                    UI: {
                        MESSAGE_DURATION: 1000,     // 消息显示时长(ms)
                        TOUCH_HOLD_DURATION: 1000,  // 长按触发时长(ms)
                        ANIMATION_DURATION: 2000    // 动画持续时间(ms)
                    }
                };

                // 向后兼容的常量别名
                const POINT_PREFIX = CONFIG.WAYPOINT.ID_PREFIX;
                const POINT_TYPE = CONFIG.WAYPOINT.TYPE;
                const API_BASE = CONFIG.API.BASE_URL;
                const DEFAULT_SPEED = CONFIG.WAYPOINT.DEFAULT_SPEED;
                const MAX_DRAG_DISTANCE = CONFIG.WAYPOINT.MAX_DRAG_DISTANCE;

                // ========== 核心工具函数 ==========
                const Utils = {
                    // 航点相关工具
                    waypoint: {
                        // 从航点ID提取索引
                        getIndex(id) {
                            if (!id || typeof id !== 'string') return -1;
                            return parseInt(id.replace(CONFIG.WAYPOINT.ID_PREFIX, ''), 10) || -1;
                        },

                        // 生成新的航点ID
                        generateId(counter) {
                            return `${CONFIG.WAYPOINT.ID_PREFIX}${counter}`;
                        },

                        // 验证航点数据
                        validate(pointData) {
                            return pointData &&
                                   typeof pointData.longitude === 'number' &&
                                   typeof pointData.latitude === 'number' &&
                                   !isNaN(pointData.longitude) &&
                                   !isNaN(pointData.latitude);
                        }
                    },

                    // UI相关工具
                    ui: {
                        // 显示消息提示
                        showMessage(message, type = 'info', duration = CONFIG.UI.MESSAGE_DURATION) {
                            if (!message) return;
                            const title = type === 'error' ? '错误' : '提示';
                            vui.message(message, title, 0, duration);
                        },

                        // 显示确认对话框
                        confirm(message, title = '确认', callback) {
                            if (!message || typeof callback !== 'function') return;
                            vui.confirm(message, title, callback);
                        },

                        // 获取作用域标识
                        getScoped($element) {
                            if (!$element || !$element[0]) return null;
                            const attrs = Array.from($element[0].getAttributeNames());
                            return attrs.find(attr => attr.startsWith('b-')) || null;
                        }
                    },

                    // 认证相关工具
                    auth: {
                        // 获取认证头信息
                        getHeaders() {
                            const uid = localStorage.getItem('uid');
                            const token = localStorage.getItem('token');

                            if (!uid || !token) {
                                console.warn('用户认证信息缺失');
                                return null;
                            }

                            return { uid, token };
                        },

                        // 检查是否已登录
                        isAuthenticated() {
                            return this.getHeaders() !== null;
                        }
                    },

                    // 网络请求工具
                    http: {
                        // 创建带认证的AJAX请求
                        request(options) {
                            const authHeaders = Utils.auth.getHeaders();
                            if (!authHeaders) {
                                Utils.ui.showMessage('用户未登录，请重新登录', 'error');
                                return Promise.reject(new Error('Authentication required'));
                            }

                            // 合并认证头
                            const headers = Object.assign({}, options.headers || {}, authHeaders);
                            const requestOptions = Object.assign({}, options, { headers });

                            return $.ajax(requestOptions);
                        },

                        // 处理HTTP错误
                        handleError(xhr, operation = '操作') {
                            const status = xhr.status;
                            let message = `${operation}失败`;

                            switch (status) {
                                case 400:
                                    message = '请求参数错误';
                                    break;
                                case 401:
                                    message = '用户未登录，请重新登录';
                                    break;
                                case 403:
                                    message = '权限不足，无法执行该操作';
                                    break;
                                case 404:
                                    message = '请求的资源不存在';
                                    break;
                                case 500:
                                    message = '服务器内部错误';
                                    break;
                                default:
                                    message = status >= 500 ? '服务器错误，请稍后重试' : '网络错误，请重试';
                            }

                            Utils.ui.showMessage(message, 'error');
                            console.error(`HTTP Error ${status}:`, xhr.responseText);
                        }
                    }
                };

                // ========== 调试工具 ==========
                const DebugHelper = {
                    // 是否启用调试模式
                    enabled: false,

                    // 调试日志
                    log(message, data = null) {
                        if (this.enabled) {
                            console.log(`[Mission Debug] ${message}`, data || '');
                        }
                    },

                    // 错误日志
                    error(message, error = null) {
                        console.error(`[Mission Error] ${message}`, error || '');
                    },

                    // 性能监控
                    time(label) {
                        if (this.enabled) {
                            console.time(`[Mission] ${label}`);
                        }
                    },

                    timeEnd(label) {
                        if (this.enabled) {
                            console.timeEnd(`[Mission] ${label}`);
                        }
                    }
                };

                // 向后兼容的函数别名
                const getPointIndex = Utils.waypoint.getIndex;
                const showMessage = Utils.ui.showMessage;
                const getAuthHeaders = Utils.auth.getHeaders;
                const createAuthenticatedAjax = Utils.http.request;
                const getScoped = Utils.ui.getScoped;

                // ========== 距离计算工具 ==========
                const DistanceCalculator = {
                    // 地球半径常量
                    EARTH_RADIUS: 6371e3, // 米

                    // 计算航线总距离 - 使用球面距离公式
                    calculateDistance(points) {
                        if (!points || points.length < 2) return '0.00';

                        let totalDistance = 0;
                        for (let i = 1; i < points.length; i++) {
                            totalDistance += this.calculateSegmentDistance(points[i - 1], points[i]);
                        }

                        return (totalDistance / 1000).toFixed(2); // 转换为公里并保留两位小数
                    },

                    // 计算两点间距离
                    calculateSegmentDistance(p1, p2) {
                        const φ1 = Cesium.Math.toRadians(p1.latitude);
                        const φ2 = Cesium.Math.toRadians(p2.latitude);
                        const Δφ = Cesium.Math.toRadians(p2.latitude - p1.latitude);
                        const Δλ = Cesium.Math.toRadians(p2.longitude - p1.longitude);

                        const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                                Math.cos(φ1) * Math.cos(φ2) *
                                Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

                        return this.EARTH_RADIUS * c;
                    }
                };

                // 向后兼容的函数别名
                const calculateDistance = DistanceCalculator.calculateDistance.bind(DistanceCalculator);

                // ========== 作用域工具 ==========
                const ScopeHelper = {
                    // 获取作用域标识 - 用于动态绑定
                    getScoped($element) {
                        if (!$element || !$element[0]) return null;

                        const attrs = Array.from($element[0].getAttributeNames());
                        return attrs.find(attr => attr.startsWith('b-')) || null;
                    }
                };

                // 向后兼容的函数别名
                const getScoped = ScopeHelper.getScoped;

                // ========== 性能优化工具 ==========
                const PerformanceHelper = {
                    // 防抖函数 - 防止频繁触发
                    debounce(func, wait) {
                        let timeout;
                        return function executedFunction(...args) {
                            const later = () => {
                                clearTimeout(timeout);
                                func(...args);
                            };
                            clearTimeout(timeout);
                            timeout = setTimeout(later, wait);
                        };
                    },

                    // 节流函数 - 限制执行频率
                    throttle(func, limit) {
                        let inThrottle;
                        return function(...args) {
                            if (!inThrottle) {
                                func.apply(this, args);
                                inThrottle = true;
                                setTimeout(() => inThrottle = false, limit);
                            }
                        };
                    },

                    // 批量DOM操作优化
                    batchDOMUpdate(callback) {
                        requestAnimationFrame(() => {
                            callback();
                        });
                    }
                };

                // ========== UI管理模块 ==========
                const UIManager = {
                    // 抽屉面板管理
                    drawer: {
                        // 显示编辑抽屉
                        showEditDrawer() {
                            $('#editDrawer').addClass('active').removeClass('point-is-selected');
                        },

                        // 隐藏编辑抽屉
                        hideEditDrawer() {
                            $('#editDrawer').removeClass('active');
                        },

                        // 显示航点详情抽屉
                        showPointDrawer() {
                            $('#editDrawer').addClass('point-is-selected');
                        },

                        // 隐藏航点详情抽屉
                        hidePointDrawer() {
                            $('#editDrawer').removeClass('point-is-selected');
                        },

                        // 显示上传抽屉
                        showUploadDrawer() {
                            $('#upload').addClass('active');
                        },

                        // 隐藏上传抽屉
                        hideUploadDrawer() {
                            $('#upload').removeClass('active');
                        }
                    },

                    // 任务列表UI管理
                    missionList: {
                        // 设置任务项编辑状态
                        setEditingState($li, isEditing) {
                            if (isEditing) {
                                $li.attr('data-editing', 'true');
                                $li.find('.edit-btn').text('退出编辑');
                            } else {
                                $li.removeAttr('data-editing');
                                $li.find('.edit-btn').text('编辑');
                            }
                        },

                        // 清除所有编辑状态
                        clearAllEditingStates() {
                            $('#floatingMissionList .mission-item').each(function() {
                                const $item = $(this);
                                $item.removeAttr('data-editing');
                                $item.find('.edit-btn').text('编辑');
                            });
                        },

                        // 选择任务项
                        selectMissionItem($li) {
                            $('#floatingMissionList .mission-item').removeClass('selected');
                            $li.addClass('selected');
                        }
                    },

                    // 表单管理
                    form: {
                        // 重置任务表单
                        resetMissionForm() {
                            $('#missionName').val('');
                            $('#speed').val(CONFIG.WAYPOINT.DEFAULT_SPEED);
                            $('#height').val(CONFIG.WAYPOINT.DEFAULT_HEIGHT);
                        },

                        // 填充任务表单
                        fillMissionForm(missionData) {
                            $('#missionName').val(missionData.missionName || '');
                            $('#speed').val(missionData.speed || CONFIG.WAYPOINT.DEFAULT_SPEED);
                            $('#height').val(missionData.height || CONFIG.WAYPOINT.DEFAULT_HEIGHT);
                        }
                    }
                };

                // ========== 状态管理模块 ==========
                const StateManager = {
                    // 应用状态
                    state: {
                        isEditing: false,
                        editingMissionId: null,
                        selectedWaypoint: null,
                        isMapLoaded: false,
                        isDragging: false
                    },

                    // 状态更新方法
                    setState(newState) {
                        Object.assign(this.state, newState);
                        this.notifyStateChange();
                        DebugHelper.log('状态更新', this.state);
                    },

                    // 获取状态
                    getState(key) {
                        return key ? this.state[key] : this.state;
                    },

                    // 重置状态
                    resetState() {
                        this.state = {
                            isEditing: false,
                            editingMissionId: null,
                            selectedWaypoint: null,
                            isMapLoaded: false,
                            isDragging: false
                        };
                        this.notifyStateChange();
                        DebugHelper.log('状态已重置');
                    },

                    // 状态变化通知
                    notifyStateChange() {
                        // 可以在这里添加状态变化的监听器
                        // 例如更新UI、触发事件等
                    },

                    // 进入编辑模式
                    enterEditMode(missionId = null) {
                        this.setState({
                            isEditing: true,
                            editingMissionId: missionId
                        });

                        UIManager.drawer.showEditDrawer();
                        UIManager.drawer.showUploadDrawer();

                        Utils.ui.showMessage(missionId ? '进入编辑模式' : '进入新建模式');
                    },

                    // 退出编辑模式
                    exitEditMode() {
                        this.setState({
                            isEditing: false,
                            editingMissionId: null,
                            selectedWaypoint: null
                        });

                        UIManager.drawer.hideEditDrawer();
                        UIManager.drawer.hideUploadDrawer();
                        UIManager.drawer.hidePointDrawer();
                        UIManager.form.resetMissionForm();
                        UIManager.missionList.clearAllEditingStates();

                        Utils.ui.showMessage('已退出编辑模式');
                    }
                };

                // ========== 任务数据管理模块 ==========
                const MissionDataManager = {
                    // 验证任务数据
                    validateMissionData(missionData) {
                        if (!missionData) {
                            Utils.ui.showMessage('任务数据无效', 'error');
                            return false;
                        }

                        if (!missionData.missionName || !missionData.missionName.trim()) {
                            Utils.ui.showMessage('任务名称不能为空', 'error');
                            return false;
                        }

                        if (!missionData.pointData || missionData.pointData.length === 0) {
                            Utils.ui.showMessage('请添加至少一个航点', 'error');
                            return false;
                        }

                        return true;
                    },

                    // 转换航点数据格式
                    convertWaypointData(pointData) {
                        const globalSpeed = parseFloat($('#speed').val()) || CONFIG.WAYPOINT.DEFAULT_SPEED;
                        const globalHeight = parseFloat($('#height').val()) || CONFIG.WAYPOINT.DEFAULT_HEIGHT;

                        return pointData.map((p, index) => {
                            const cartographic = Cesium.Cartographic.fromCartesian(p.position);

                            return {
                                id: Utils.waypoint.getIndex(p.id),
                                longitude: Cesium.Math.toDegrees(cartographic.longitude),
                                latitude: Cesium.Math.toDegrees(cartographic.latitude),
                                altitude: p.height !== undefined ? p.height : globalHeight,
                                speed: p.speed !== undefined ? p.speed : globalSpeed,
                                heading: p.heading !== undefined ? p.heading : CONFIG.WAYPOINT.DEFAULT_HEADING
                            };
                        });
                    },

                    // 构建保存载荷
                    buildSavePayload(missionName, pointData, missionId = null) {
                        const vehicleId = @Model.id;
                        const speed = parseFloat($('#speed').val()) || CONFIG.WAYPOINT.DEFAULT_SPEED;
                        const height = parseFloat($('#height').val()) || CONFIG.WAYPOINT.DEFAULT_HEIGHT;

                        const payload = {
                            missionName: missionName.trim(),
                            vehicleId,
                            missionDescription: 'Auto-generated mission',
                            pointData: this.convertWaypointData(pointData),
                            speed,
                            height
                        };

                        if (missionId) {
                            payload.Id = missionId;
                        }

                        return payload;
                    },

                    // 处理任务保存
                    async saveMission(missionName, pointData, missionId = null) {
                        try {
                            const payload = this.buildSavePayload(missionName, pointData, missionId);
                            const url = missionId ?
                                `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.UPDATE}` :
                                `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.ADD}`;

                            const response = await Utils.http.request({
                                url: url,
                                method: missionId ? 'PUT' : 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify(payload)
                            });

                            if (response.success) {
                                Utils.ui.showMessage("任务保存成功");
                                return { success: true, data: response.data };
                            } else {
                                Utils.ui.showMessage(response.message || "保存失败", 'error');
                                return { success: false, message: response.message };
                            }
                        } catch (error) {
                            Utils.http.handleError(error, '保存任务');
                            return { success: false, error };
                        }
                    }
                };

                // ========== 事件处理模块 ==========
                const EventManager = {
                    // 初始化事件监听器
                    initializeEventListeners(viewer, missionLayer) {
                        const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

                        // 左键单击事件 - 选择航点
                        handler.setInputAction((click) => {
                            this.handleWaypointSelection(click, viewer, missionLayer);
                        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

                        // 右键单击事件 - 创建航点
                        handler.setInputAction((click) => {
                            this.handleWaypointCreation(click, viewer, missionLayer);
                        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

                        // 双击事件 - 删除航点
                        handler.setInputAction((click) => {
                            this.handleWaypointDeletion(click, viewer, missionLayer);
                        }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

                        return handler;
                    },

                    // 处理航点选择
                    handleWaypointSelection(click, viewer, missionLayer) {
                        if (!missionLayer.isEditing) return;

                        const pickedObject = viewer.scene.pick(click.position);

                        if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                            WaypointManager.selectWaypoint(pickedObject.id, missionLayer);
                        } else {
                            WaypointManager.deselectWaypoint(missionLayer);
                        }
                    },

                    // 处理航点创建
                    handleWaypointCreation(click, viewer, missionLayer) {
                        if (!missionLayer.validateEditState()) return;

                        const cartesian = viewer.scene.pickPosition(click.position);
                        if (!Cesium.defined(cartesian)) return;

                        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                        const newPosition = Cesium.Cartesian3.fromRadians(
                            cartographic.longitude,
                            cartographic.latitude,
                            cartographic.height
                        );

                        WaypointManager.createWaypoint(newPosition, viewer, missionLayer);
                    },

                    // 处理航点删除
                    handleWaypointDeletion(click, viewer, missionLayer) {
                        if (!missionLayer.isEditing) return;

                        const pickedObject = viewer.scene.pick(click.position);
                        if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                            WaypointManager.deleteWaypoint(pickedObject.id, viewer, missionLayer);
                        }
                    },

                    // 清理事件监听器
                    cleanup(handler) {
                        if (handler && !handler.isDestroyed()) {
                            handler.destroy();
                        }
                    }
                };

                // ========== 航点管理模块 ==========
                const WaypointManager = {
                    // 创建新航点
                    createWaypoint(position, viewer, missionLayer) {
                        const newId = Utils.waypoint.generateId(++missionLayer.pointCounter);
                        const waypointNumber = missionLayer.pointData.length + 1;

                        const newEntity = viewer.entities.add({
                            id: newId,
                            position: new Cesium.ConstantPositionProperty(position),
                            type: CONFIG.WAYPOINT.TYPE,
                            point: {
                                color: Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL),
                                pixelSize: 24,
                                outlineColor: Cesium.Color.fromCssColorString('#FF5722'),
                                outlineWidth: 3
                            },
                            label: {
                                text: String(waypointNumber),
                                font: '14pt sans-serif',
                                fillColor: Cesium.Color.WHITE,
                                outlineColor: Cesium.Color.BLACK,
                                outlineWidth: 2,
                                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                                pixelOffset: new Cesium.Cartesian2(0, -40)
                            }
                        });

                        // 添加到航点数据数组
                        missionLayer.pointData.push({
                            id: newId,
                            position: position,
                            entity: newEntity
                        });

                        // 更新航线
                        this.updateFlightPath(viewer, missionLayer);

                        DebugHelper.log(`创建航点: ${newId}`, { position, waypointNumber });
                    },

                    // 选择航点
                    selectWaypoint(entity, missionLayer) {
                        if (missionLayer.selectedEntity === entity) return;

                        // 重置之前选中航点的颜色
                        this.deselectWaypoint(missionLayer);

                        // 设置新选中的航点
                        missionLayer.selectedEntity = entity;
                        entity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.SELECTED);
                        missionLayer.showPointDrawer(entity);

                        DebugHelper.log(`选择航点: ${entity.id}`);
                    },

                    // 取消选择航点
                    deselectWaypoint(missionLayer) {
                        if (missionLayer.selectedEntity) {
                            missionLayer.selectedEntity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL);
                            missionLayer.selectedEntity = null;
                            missionLayer.hidePointDrawer();

                            DebugHelper.log('取消选择航点');
                        }
                    },

                    // 删除航点
                    deleteWaypoint(entity, viewer, missionLayer) {
                        const pointIndex = missionLayer.pointData.findIndex(p => p.id === entity.id);
                        if (pointIndex === -1) return;

                        // 从数据数组中移除
                        missionLayer.pointData.splice(pointIndex, 1);

                        // 从地图中移除
                        viewer.entities.remove(entity);

                        // 如果删除的是选中的航点，清除选中状态
                        if (missionLayer.selectedEntity === entity) {
                            missionLayer.selectedEntity = null;
                            missionLayer.hidePointDrawer();
                        }

                        // 重新编号剩余航点
                        this.renumberWaypoints(missionLayer);

                        // 更新航线
                        this.updateFlightPath(viewer, missionLayer);

                        DebugHelper.log(`删除航点: ${entity.id}`, { pointIndex });
                    },

                    // 重新编号航点
                    renumberWaypoints(missionLayer) {
                        missionLayer.pointData.forEach((point, index) => {
                            if (point.entity && point.entity.label) {
                                point.entity.label.text = String(index + 1);
                            }
                        });
                    },

                    // 更新飞行路径
                    updateFlightPath(viewer, missionLayer) {
                        // 移除现有航线
                        if (missionLayer.lineEntity) {
                            viewer.entities.remove(missionLayer.lineEntity);
                            missionLayer.lineEntity = null;
                        }

                        // 如果航点少于2个，不绘制航线
                        if (missionLayer.pointData.length < 2) return;

                        // 创建新航线
                        const positions = missionLayer.pointData.map(p => p.position);
                        missionLayer.lineEntity = viewer.entities.add({
                            polyline: {
                                positions: positions,
                                width: 3,
                                material: Cesium.Color.fromCssColorString('#2196F3'),
                                clampToGround: true
                            }
                        });

                        // 更新距离显示
                        const distance = DistanceCalculator.calculateDistance(
                            missionLayer.pointData.map(p => {
                                const cartographic = Cesium.Cartographic.fromCartesian(p.position);
                                return {
                                    longitude: Cesium.Math.toDegrees(cartographic.longitude),
                                    latitude: Cesium.Math.toDegrees(cartographic.latitude)
                                };
                            })
                        );

                        $('#distance').text(`${distance} km`);
                    }
                };

                // ========== 模块管理器 ==========
                const ModuleManager = {
                    // 所有模块的引用
                    modules: {
                        ui: UIManager,
                        state: StateManager,
                        data: MissionDataManager,
                        event: EventManager,
                        waypoint: WaypointManager,
                        distance: DistanceCalculator,
                        performance: PerformanceHelper,
                        debug: DebugHelper
                    },

                    // 初始化所有模块
                    initialize() {
                        DebugHelper.log('开始初始化模块管理器');

                        // 可以在这里添加模块间的依赖关系设置
                        // 例如：模块A依赖模块B，需要先初始化模块B

                        DebugHelper.log('模块管理器初始化完成');
                    },

                    // 获取模块
                    getModule(name) {
                        return this.modules[name];
                    },

                    // 检查模块是否存在
                    hasModule(name) {
                        return name in this.modules;
                    },

                    // 清理所有模块
                    cleanup() {
                        DebugHelper.log('开始清理模块');

                        // 清理状态
                        StateManager.resetState();

                        // 可以在这里添加其他模块的清理逻辑

                        DebugHelper.log('模块清理完成');
                    }
                };

                // ========== 任务图层管理器 ==========
                const missionLayer = {
                    // ========== 状态属性 ==========
                    viewer: null,                    // Cesium视图对象
                    handler: null,                   // 事件处理器
                    pointCounter: 0,                 // 航点计数器
                    pointData: [],                   // 航点数据数组
                    lineEntity: null,                // 航线实体
                    selectedEntity: null,            // 当前选中的航点实体

                    // 使用状态管理器的属性
                    get editingMissionId() { return StateManager.getState('editingMissionId'); },
                    set editingMissionId(value) { StateManager.setState({ editingMissionId: value }); },

                    get isEditing() { return StateManager.getState('isEditing'); },
                    set isEditing(value) { StateManager.setState({ isEditing: value }); },

                    get loaded() { return StateManager.getState('isMapLoaded'); },
                    set loaded(value) { StateManager.setState({ isMapLoaded: value }); },

                    // ========== 状态管理方法 ==========
                    // 重置所有状态
                    resetState() {
                        this.pointCounter = 0;
                        this.pointData = [];
                        this.lineEntity = null;
                        this.selectedEntity = null;

                        // 使用状态管理器重置状态
                        StateManager.exitEditMode();

                        // 清理地图上的所有航点和航线
                        if (this.viewer) {
                            this.viewer.entities.removeAll();
                        }

                        DebugHelper.log('任务图层状态已重置');
                    },

                    // 进入编辑模式
                    enterEditMode(missionId = null) {
                        StateManager.enterEditMode(missionId);
                        DebugHelper.log(`任务图层进入编辑模式`, { missionId });
                    },

                    // 退出编辑模式
                    exitEditMode() {
                        this.resetState();
                        DebugHelper.log('任务图层退出编辑模式');
                    },

                    // 验证编辑状态
                    validateEditState() {
                        if (!this.isEditing) {
                            Utils.ui.showMessage('请先进入编辑模式', 'error');
                            return false;
                        }
                        if (!this.loaded) {
                            Utils.ui.showMessage('地图未加载完成', 'error');
                            return false;
                        }
                        return true;
                    },

                    // ========== 地图初始化方法 ==========
                    // 渲染地图 - 初始化Cesium视图
                    render(e) {
                        try {
                            // 设置Cesium访问令牌
                            Cesium.Ion.defaultAccessToken = CONFIG.MAP.CESIUM_TOKEN;

                            // 创建Cesium视图器
                            this.viewer = new Cesium.Viewer(CONFIG.MAP.CONTAINER_ID, {
                                terrain: Cesium.Terrain.fromWorldTerrain(),
                                sceneMode: Cesium.SceneMode.SCENE2D,
                                animation: false,
                                timeline: false,
                                fullscreenButton: false,
                                infoBox: false,
                                geocoder: false,
                                homeButton: false,
                                sceneModePicker: false,
                                baseLayerPicker: false,
                                navigationHelpButton: false,
                                selectionIndicator: false,
                                shouldAnimate: true,
                                enableRotate: false,
                                enableTilt: false,
                                orderIndependentTranslucency: true,
                                contextOptions: { webgl: { alpha: true } }
                            });

                            // 设置视图器引用和隐藏版权信息
                            e.viewer = this.viewer;
                            this.viewer.cesiumWidget.creditContainer.style.display = 'none';

                            // 相机heading重置 - 确保2D模式下相机朝向始终为正北
                            this.setupCameraHeadingReset();

                            // 启用抗锯齿
                            this.viewer.scene.postProcessStages.fxaa.enabled = true;

                            // 初始化事件管理器
                            this.handler = EventManager.initializeEventListeners(this.viewer, this);

                            // 初始化拖拽模块
                            this.dragModule.init(this.viewer, this);

                            // 设置加载完成状态
                            this.loaded = true;
                            e.loaded = true;

                            console.log('地图初始化完成');
                            Utils.ui.showMessage('地图加载完成');

                        } catch (error) {
                            console.error('地图初始化失败:', error);
                            Utils.ui.showMessage('地图初始化失败，请刷新页面重试', 'error', 3000);
                            this.loaded = false;
                        }
                    },

                    // 设置相机朝向重置
                    setupCameraHeadingReset() {
                        let headingResetTimeout;
                        this.viewer.scene.postRender.addEventListener(() => {
                            if (this.viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                                const heading = this.viewer.camera.heading;
                                if (Math.abs(heading) > 0.001) {
                                    clearTimeout(headingResetTimeout);
                                    headingResetTimeout = setTimeout(() => {
                                        this.viewer.camera.setView({ orientation: { heading: 0 } });
                                    }, CONFIG.MAP.CAMERA_RESET_DELAY);
                                }
                            }
                        });
                    },

                    // 拖动模块 - 处理地图交互
                    dragModule: {
                        viewer: null,                    // Cesium视图引用
                        missionLayer: null,              // 任务图层引用
                        handler: null,                   // 事件处理器
                        isDragging: false,               // 是否正在拖拽
                        draggingEntity: null,            // 正在拖拽的实体
                        originalDragPosition: null,      // 拖拽开始时的原始位置

                        // 初始化拖拽模块
                        init: function (viewer, missionLayer) {
                            this.viewer = viewer;
                            this.missionLayer = missionLayer;
                            this.handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
                            this.setupEvents();

                            // 适配移动端长按新增航点
                            let touchTimer = null;
                            let lastTouchPos = null;

                            viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    lastTouchPos = {
                                        x: e.touches[0].clientX,
                                        y: e.touches[0].clientY
                                    };
                                    touchTimer = setTimeout(function () {
                                        // 长按1000ms触发新增航点
                                        const rect = viewer.canvas.getBoundingClientRect();
                                        const pos = {
                                            x: lastTouchPos.x - rect.left,
                                            y: lastTouchPos.y - rect.top
                                        };
                                        const cartesian = viewer.scene.pickPosition(pos);
                                        if (Cesium.defined(cartesian)) {
                                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                                            const newPosition = Cesium.Cartesian3.fromRadians(
                                                cartographic.longitude,
                                                cartographic.latitude,
                                                cartographic.height
                                            );
                                            const newId = 'point' + (++missionLayer.pointCounter);
                                            const newEntity = viewer.entities.add({
                                                id: newId,
                                                position: new Cesium.ConstantPositionProperty(newPosition),
                                                type: 'custom-point',
                                                point: {
                                                    color: Cesium.Color.fromCssColorString('#1976D2'),
                                                    pixelSize: 24,
                                                    outlineColor: Cesium.Color.fromCssColorString('#FF5722'),
                                                    outlineWidth: 3
                                                },
                                                label: {
                                                    text: String(missionLayer.pointData.length + 1),
                                                    font: '30px sans-serif', // 字体大小32px
                                                    scale: 0.5, // 缩小一半
                                                    fillColor: Cesium.Color.WHITE,
                                                    style: Cesium.LabelStyle.FILL,
                                                    verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                                    disableDepthTestDistance: Number.POSITIVE_INFINITY
                                                }
                                            });
                                            missionLayer.pointData.push({ id: newId, position: newPosition });
                                            missionLayer.updateLine();
                                            missionLayer.selectedEntity = newEntity;
                                            newEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');
                                            missionLayer.showPointDrawer(newEntity);
                                        }
                                    }, 1000); // 长按600ms
                                }
                            });

                            viewer.canvas.addEventListener('touchend', function (e) {
                                if (touchTimer) {
                                    clearTimeout(touchTimer);
                                    touchTimer = null;
                                    if (missionLayer.selectedEntity) {
                                        missionLayer.selectedEntity.point.color = Cesium.Color.fromCssColorString('#1976D2');
                                        missionLayer.selectedEntity = null;
                                        missionLayer.hidePointDrawer();
                                    }
                                }
                            });

                            // 适配触摸拖动航点
                            let draggingTouchId = null;
                            let draggingEntity = null;

                            viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    const touch = e.touches[0];
                                    const pos = { x: touch.clientX, y: touch.clientY };
                                    const rect = viewer.canvas.getBoundingClientRect();
                                    const pickPos = {
                                        x: pos.x - rect.left,
                                        y: pos.y - rect.top
                                    };
                                    const pickedObject = viewer.scene.pick(pickPos);
                                    if (pickedObject && pickedObject.id && pickedObject.id.type === 'custom-point') {
                                        draggingTouchId = touch.identifier;
                                        draggingEntity = pickedObject.id;
                                        viewer.scene.screenSpaceCameraController.enableTranslate = false;
                                        viewer.scene.screenSpaceCameraController.enableTilt = false;
                                    }
                                }
                            });

                            viewer.canvas.addEventListener('touchmove', function (e) {  
                                if (draggingTouchId !== null && draggingEntity) {
                                    for (let i = 0; i < e.touches.length; i++) {
                                        if (e.touches[i].identifier === draggingTouchId) {
                                            const touch = e.touches[i];
                                            const rect = viewer.canvas.getBoundingClientRect();
                                            const pos = {
                                                x: touch.clientX - rect.left,
                                                y: touch.clientY - rect.top
                                            };
                                            const cartesian = viewer.scene.pickPosition(pos);
                                            if (Cesium.defined(cartesian)) {
                                                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                                                const newPosition = Cesium.Cartesian3.fromRadians(
                                                    cartographic.longitude,
                                                    cartographic.latitude,
                                                    cartographic.height
                                                );
                                                draggingEntity.position = new Cesium.ConstantPositionProperty(newPosition);
                                                const index = missionLayer.pointData.findIndex(p => p.id === draggingEntity.id);
                                                if (index !== -1) {
                                                    missionLayer.pointData[index].position = newPosition;
                                                }
                                                $('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(7));
                                                $('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(7));
                                                missionLayer.updateLine();
                                            }
                                            break;
                                        }
                                    }
                                }
                            });

                            viewer.canvas.addEventListener('touchend', function (e) {
                                if (draggingTouchId !== null) {
                                    viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                    viewer.scene.screenSpaceCameraController.enableTilt = true;
                                    draggingTouchId = null;
                                    draggingEntity = null;
                                }
                            });
                        },
                        // ========== 事件处理设置 ==========
                        setupEvents() {
                            const self = this;

                            // 点选功能 - 左键单击选择航点
                            this.handler.setInputAction((click) => {
                                self.handleWaypointSelection(click);
                            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

                            // 新增点功能 - 左键双击添加航点
                            this.handler.setInputAction((click) => {
                                self.handleWaypointCreation(click);
                            }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
                        },

                        // ========== 事件处理方法 ==========
                        // 处理航点选择
                        handleWaypointSelection(click) {
                            if (!this.missionLayer.isEditing) return;

                            const pickedObject = this.viewer.scene.pick(click.position);

                            if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                                this.selectWaypoint(pickedObject.id);
                            } else {
                                this.deselectWaypoint();
                            }
                        },

                        // 选择航点
                        selectWaypoint(entity) {
                            if (this.missionLayer.selectedEntity === entity) return;

                            // 重置之前选中航点的颜色
                            this.deselectWaypoint();

                            // 设置新选中的航点
                            this.missionLayer.selectedEntity = entity;
                            entity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.SELECTED);
                            this.missionLayer.showPointDrawer(entity);
                        },

                        // 取消选择航点
                        deselectWaypoint() {
                            if (this.missionLayer.selectedEntity) {
                                this.missionLayer.selectedEntity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL);
                                this.missionLayer.selectedEntity = null;
                                this.missionLayer.hidePointDrawer();
                            }
                        },

                        // 处理航点创建
                        handleWaypointCreation(click) {
                            if (!this.missionLayer.validateEditState()) return;

                            const cartesian = this.viewer.scene.pickPosition(click.position);
                            if (!Cesium.defined(cartesian)) return;

                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                            const newPosition = Cesium.Cartesian3.fromRadians(
                                cartographic.longitude,
                                cartographic.latitude,
                                cartographic.height
                            );

                            this.createWaypoint(newPosition);
                        },

                        // 创建新航点
                        createWaypoint(position) {
                            const newId = Utils.waypoint.generateId(++this.missionLayer.pointCounter);
                            const waypointNumber = this.missionLayer.pointData.length + 1;

                            const newEntity = this.viewer.entities.add({
                                id: newId,
                                position: new Cesium.ConstantPositionProperty(position),
                                type: CONFIG.WAYPOINT.TYPE,
                                point: {
                                    color: Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL),
                                    pixelSize: 24,
                                    outlineColor: Cesium.Color.fromCssColorString('#FF5722'),
                                    outlineWidth: 3
                                },
                                label: {
                                    text: String(waypointNumber),
                                    font: '30px sans-serif',
                                    scale: 0.5,
                                    fillColor: Cesium.Color.WHITE,
                                    style: Cesium.LabelStyle.FILL,
                                    verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                    disableDepthTestDistance: Number.POSITIVE_INFINITY
                                }
                            });

                            // 添加到数据数组并更新连线
                            this.missionLayer.pointData.push({ id: newId, position: position });
                            this.missionLayer.updateLine();

                            Utils.ui.showMessage(`已添加航点 ${waypointNumber}`);

                                // 自动选中新创建的航点
                                self.missionLayer.selectedEntity = newEntity;
                                newEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');
                                self.missionLayer.showPointDrawer(newEntity);
                            }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

                            // 开始拖拽 - 左键按下时触发
                            this.handler.setInputAction((movement) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.selectedEntity) return;
                                const pickedObject = self.viewer.scene.pick(movement.position);
                                if (pickedObject && pickedObject.id === self.missionLayer.selectedEntity) {
                                    self.startDrag(self.missionLayer.selectedEntity);
                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

                            // 拖拽过程 - 鼠标移动
                            this.handler.setInputAction((movement) => {
                                if (!this.isDragging || !this.draggingEntity) return;

                                const cartesian = this.viewer.scene.pickPosition(movement.endPosition);
                                if (!Cesium.defined(cartesian)) return;

                                const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                                // 验证坐标有效性
                                if (isNaN(cartographic.longitude) || isNaN(cartographic.latitude)) return;

                                this.missionLayer.selectedEntity.position.getValue();
                                const newPosition = Cesium.Cartesian3.fromRadians(
                                    cartographic.longitude,
                                    cartographic.latitude,
                                    cartographic.height.toFixed(2)
                                );

                                // 检查拖拽距离限制
                                if (this.originalDragPosition) {
                                    const distance = Cesium.Cartesian3.distance(this.originalDragPosition, newPosition);
                                    if (distance > MAX_DRAG_DISTANCE) return;
                                }

                                // 更新航点位置
                                this.draggingEntity.position = new Cesium.ConstantPositionProperty(newPosition);
                                const index = this.missionLayer.pointData.findIndex(p => p.id === this.draggingEntity.id);
                                if (index !== -1) {
                                    this.missionLayer.pointData[index].position = newPosition;
                                }

                                // 实时更新UI
                                $('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(7));
                                $('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(7));
                                this.missionLayer.updateLine();
                            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

                            // 结束拖拽 - 左键释放时触发
                            this.handler.setInputAction(() => {
                                this.endDrag();
                            }, Cesium.ScreenSpaceEventType.LEFT_UP);
                        },

                        // 开始拖拽 - 左键按下时触发
                        startDrag(entity) {
                            if (!entity) return;
                            this.draggingEntity = entity;
                            this.isDragging = true;
                            this.originalDragPosition = entity.position.getValue(this.viewer.clock.currentTime);
                            // 禁用相机平移和倾斜，防止拖拽时意外移动地图
                            this.viewer.scene.screenSpaceCameraController.enableTranslate = false;
                            this.viewer.scene.screenSpaceCameraController.enableTilt = false;
                            entity.point.color = Cesium.Color.fromCssColorString('#7B1FA2');
                        },

                        // 结束拖拽 - 左键释放时触发
                        endDrag() {
                            if (this.isDragging && this.draggingEntity) {
                                // 恢复相机控制
                                this.viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                this.viewer.scene.screenSpaceCameraController.enableTilt = true;
                                this.draggingEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');

                                // 简化：直接更新高度显示
                                const cartographic = Cesium.Cartographic.fromCartesian(this.draggingEntity.position.getValue());
                            }
                            this.isDragging = false;
                            this.draggingEntity = null;
                            this.originalDragPosition = null;
                        }
                    },

                    // 显示航点抽屉 - 显示选中航点的详细信息
                    showPointDrawer: function(entity) {
                        if (!entity || !entity.position) return;
                        const cartographic = Cesium.Cartographic.fromCartesian(entity.position.getValue());
                        $('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(7));
                        $('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(7));
                        
                        // 获取选中航点的数据
                        const index = this.pointData.findIndex(p => p.id === entity.id);
                        if (index !== -1) {
                            const pointData = this.pointData[index];
                            const globalSpeed = parseFloat($('#speed').val()) || DEFAULT_SPEED;
                            const globalHeight = parseFloat($('#height').val()) || 20;
                            
                            // 处理速度设置
                            if (pointData.speed !== undefined) {
                                // 有特定速度
                                $('#isOther').prop('checked', false);
                                $('#speedOther').prop('disabled', false);
                                $('#speedOther').val(pointData.speed);
                                $('#speedOther').removeClass('disabled-input-hint');
                            } else {
                                // 使用全局速度
                                $('#isOther').prop('checked', true);
                                $('#speedOther').prop('disabled', true);
                                $('#speedOther').val('使用全局速度');
                                $('#speedOther').addClass('disabled-input-hint');
                            }
                            
                            // 处理高度设置
                            if (pointData.height !== undefined) {
                                // 有特定高度
                                $('#isOther2').prop('checked', false);
                                $('#heightOther').prop('disabled', false);
                                $('#heightOther').val(pointData.height);
                                $('#heightOther').removeClass('disabled-input-hint');
                            } else {
                                // 使用全局高度
                                $('#isOther2').prop('checked', true);
                                $('#heightOther').prop('disabled', true);
                                $('#heightOther').val('使用全局高度');
                                $('#heightOther').addClass('disabled-input-hint');
                            }
                            
                            // 处理航向设置
                            if (pointData.heading !== undefined) {
                                // 有特定航向
                                $('#isOther3').prop('checked', false);
                                $('#headingOther').prop('disabled', false);
                                $('#headingOther').val(pointData.heading);
                                $('#headingOther').removeClass('disabled-input-hint');
                            } else {
                                // 使用默认航向
                                $('#isOther3').prop('checked', true);
                                $('#headingOther').prop('disabled', true);
                                $('#headingOther').val('使用默认航向');
                                $('#headingOther').addClass('disabled-input-hint');
                            }
                        } else {
                            // 如果没有找到点数据，设置默认值
                            $('#isOther').prop('checked', true);
                            $('#speedOther').prop('disabled', true);
                            $('#speedOther').val('使用全局速度');
                            $('#speedOther').addClass('disabled-input-hint');
                            
                            $('#isOther2').prop('checked', true);
                            $('#heightOther').prop('disabled', true);
                            $('#heightOther').val('使用全局高度');
                            $('#heightOther').addClass('disabled-input-hint');
                            
                            $('#isOther3').prop('checked', true);
                            $('#headingOther').prop('disabled', true);
                            $('#headingOther').val('使用默认航向');
                            $('#headingOther').addClass('disabled-input-hint');
                        }
                        
                        $('#editDrawer').addClass('active').addClass('point-is-selected');
                        $('#upload').removeClass('active');
                    },

                    // 隐藏航点抽屉 - 清除航点详细信息显示
                    hidePointDrawer: function() {
                        $('#latDisplay').text('--');
                        $('#lngDisplay').text('--');
                        $('#editDrawer').removeClass('point-is-selected');
                        if (!this.isEditing) {
                            $('#editDrawer').removeClass('active');
                            $('#upload').addClass('active');
                        }
                         // 重置所有输入框状态
                        $('#isOther').prop('checked', true);
                        $('#speedOther').prop('disabled', true);
                        $('#speedOther').val('使用全局速度');
                        $('#speedOther').addClass('disabled-input-hint');
                        
                        $('#isOther2').prop('checked', true);
                        $('#heightOther').prop('disabled', true);
                        $('#heightOther').val('使用全局高度');
                        $('#heightOther').addClass('disabled-input-hint');
                        
                        $('#isOther3').prop('checked', true);
                        $('#headingOther').prop('disabled', true);
                        $('#headingOther').val('使用默认航向');
                        $('#headingOther').addClass('disabled-input-hint');
                        
                        
                    },

                    // 更新航线 - 根据航点数据绘制连线
                    updateLine: function () {
                        if (this.pointData.length < 2) {
                            // 航点少于2个时移除连线
                            if (this.lineEntity) {
                                this.viewer.entities.remove(this.lineEntity);
                                this.lineEntity = null;
                            }
                            return;
                        }

                        const positions = this.pointData.filter(p => p.position).map(p => p.position);
                        if (!this.lineEntity) {
                            // 创建新的连线实体
                            this.lineEntity = this.viewer.entities.add({
                                polyline: {
                                    positions: [...positions],
                                    width: 2,
                                    material: Cesium.Color.fromCssColorString('#FF5722'),
                                    clampToGround: true
                                }
                            });
                        } else {
                            // 更新现有连线位置
                            this.lineEntity.polyline.positions = [...positions];
                        }
                    },
                    // 更新航点标签 - 重新编号航点
                    updatePointLabels: function () {
                        const entities = this.viewer.entities.values;
                        const points = entities.filter(entity => entity.id && entity.id.startsWith(POINT_PREFIX));
                        points.sort((a, b) => getPointIndex(a.id) - getPointIndex(b.id));
                        points.forEach((entity, index) => {
                            entity.label.text = String(index + 1);
                        });
                    },

                    // 删除航点 - 移除选中的航点
                    removePoint: function () {
                        if (!this.selectedEntity) {
                            showMessage("请先点击地图上的点进行删除", 'error');
                            return;
                        }

                        vui.confirm("确定要删除这个点吗？", "删除确认", () => {
                            const entityId = this.selectedEntity.id;
                            this.viewer.entities.remove(this.selectedEntity);
                            const index = this.pointData.findIndex(p => p.id === entityId);
                            if (index !== -1) {
                                this.pointData.splice(index, 1);
                            }
                            this.selectedEntity = null;
                            this.hidePointDrawer();
                            this.updateLine();
                            this.updatePointLabels();
                            showMessage("航点已删除");
                        });
                    },

                    // 编辑任务 - 加载任务数据到地图
                    editMission: function(mission) {
                        this.editingMission = {
                            Id: mission.id,
                            speed: mission.speed,
                            pointData: mission.pointData
                        };

                        // 设置UI值
                        $('#speed').val(mission.speed || DEFAULT_SPEED);
                        $('#height').val(mission.height || 20);
                        $('#missionName').val(mission.missionName || '');

                        // 清理现有数据
                        this.pointData = [];
                        if (this.lineEntity) {
                            this.viewer.entities.remove(this.lineEntity);
                            this.lineEntity = null;
                        }
                        this.viewer.entities.removeAll();

                        // 创建航点实体
                        mission.pointData.forEach(p => {
                            const position = Cesium.Cartesian3.fromDegrees(p.longitude, p.latitude, p.altitude);
                            const newId = POINT_PREFIX + ++this.pointCounter;

                            // 添加航点实体到地图
                            this.viewer.entities.add({
                                id: newId,
                                position: new Cesium.ConstantPositionProperty(position),
                                type: POINT_TYPE,
                                point: {
                                    color: Cesium.Color.fromCssColorString('#1976D2'),
                                    pixelSize: 24,
                                    outlineColor: Cesium.Color.WHITE,
                                    outlineWidth: 3
                                },
                                label: {
                                    text: String(this.pointData.length + 1),
                                    font: '30px sans-serif',
                                    scale: 0.5,
                                    fillColor: Cesium.Color.WHITE,
                                    style: Cesium.LabelStyle.FILL,
                                    verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                    disableDepthTestDistance: Number.POSITIVE_INFINITY
                                }
                            });

                            // 创建点数据对象
                            const pointData = { id: newId, position };
                            
                            // 只保存与全局设置不同的特定值
                            const specificSettings = [
                                { property: 'speed', pointValue: p.speed, globalValue: mission.speed },
                                { property: 'height', pointValue: p.altitude, globalValue: mission.height },
                                { property: 'heading', pointValue: p.heading, globalValue: 0xFFFF }
                            ];
                            
                            specificSettings.forEach(setting => {
                                if (setting.pointValue !== undefined && setting.pointValue !== setting.globalValue) {
                                    pointData[setting.property] = setting.pointValue;
                                }
                            });
                            
                            this.pointData.push(pointData);
                        });

                        this.updateLine();
                        this.updatePointLabels();

                        // 自动定位到航点区域
                        if (this.pointData.length > 0) {
                            this.flyToPoints(this.viewer, mission.pointData);
                        }
                    },

                    // 定位显示 - 自动调整相机视角以显示所有航点
                    flyToPoints: function(viewer, pointData) {
                        if (!pointData || pointData.length === 0) return;

                        const lons = pointData.map(p => p.longitude);
                        const lats = pointData.map(p => p.latitude);
                        const lonRange = Math.max(...lons) - Math.min(...lons);
                        const latRange = Math.max(...lats) - Math.min(...lats);

                        // 根据航点分布范围调整视角参数
                        let padding, zoomAmount, pitch;
                        if (lonRange < 0.005 && latRange < 0.005) {
                            // 小范围 - 近距离显示
                            padding = 0.001;
                            zoomAmount = 100;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.1;
                        } else if (lonRange < 0.1 && latRange < 0.1) {
                            // 中等范围 - 中距离显示
                            padding = 0.01;
                            zoomAmount = 500;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.2;
                        } else {
                            // 大范围 - 远距离显示
                            padding = 0.05;
                            zoomAmount = 1000;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.3;
                        }

                        viewer.camera.flyTo({
                            destination: Cesium.Rectangle.fromDegrees(
                                Math.min(...lons) - padding,
                                Math.min(...lats) - padding,
                                Math.max(...lons) + padding,
                                Math.max(...lats) + padding
                            ),
                            zoom: { amount: zoomAmount },
                            orientation: { heading: 0, pitch: pitch, roll: 0 },
                            duration: 2
                        });
                    },

                    // ========== 数据加载方法 ==========
                    // 加载任务详情 - 从服务器获取任务数据
                    loadMissionForEdit(missionId) {
                        if (!missionId) {
                            Utils.ui.showMessage('任务ID无效', 'error');
                            return;
                        }

                        Utils.http.request({
                            url: `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.DETAIL}`,
                            method: 'GET',
                            data: { id: missionId }
                        }).then(res => {
                            if (res.success && res.data) {
                                this.handleMissionLoadSuccess(res.data, missionId);
                            } else {
                                Utils.ui.showMessage(res.message || '加载任务失败', 'error');
                            }
                        }).catch(xhr => {
                            Utils.http.handleError(xhr, '加载任务详情');
                        });
                    },

                    // 处理任务加载成功
                    handleMissionLoadSuccess(missionData, missionId) {
                        const wasEditing = this.isEditing;
                        const editingMissionId = this.editingMissionId;

                        this.editMission(missionData);

                        // 如果之前正在编辑此任务，保持编辑状态
                        if (wasEditing && editingMissionId === missionId) {
                            this.enterEditMode(missionId);

                            const $editingLi = $('#floatingMissionList').find(`li[data-id="${missionId}"]`);
                            if ($editingLi.length > 0) {
                                $editingLi.attr('data-editing', 'true');
                                $editingLi.find('.edit-btn').text('退出编辑');
                            }
                        }
                    },

                    // 加载任务列表 - 从服务器获取所有任务
                    loadMissionList() {
                        Utils.http.request({
                            url: `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.LIST}`,
                            method: 'GET',
                            data: {
                                page: 1,
                                limit: 50,
                                'biz_context.VehicleId': @Model.id
                            }
                        }).then(res => {
                            if (res.success && res.data?.list?.length > 0) {
                                this.renderMissionList(res.data.list);
                            } else {
                                this.renderEmptyMissionList();
                            }
                        }).catch(xhr => {
                            Utils.http.handleError(xhr, '加载任务列表');
                            this.renderErrorMissionList();
                        });
                    },

                    // 渲染任务列表
                    renderMissionList(missions) {
                        const missionList = $('#floatingMissionList');
                        missionList.empty();
                        const scoped_symbol = Utils.ui.getScoped($("#floatingMissionList"));

                        missions.forEach(mission => {
                            const li = this.createMissionListItem(mission, scoped_symbol);
                            missionList.append(li);
                        });
                    },

                    // 创建任务列表项
                    createMissionListItem(mission, scoped_symbol) {
                        return `<li ${scoped_symbol} data-id="${mission.id}" class="mission-item">
                            <div ${scoped_symbol} class="mission-header">
                                <span ${scoped_symbol} class="mission-name">${mission.missionName}</span>
                                <span ${scoped_symbol} class="point-count">航点数: ${mission.pointData.length}</span>
                            </div>
                            <div ${scoped_symbol} class="mission-footer">
                                <span ${scoped_symbol} class="distance">${calculateDistance(mission.pointData)} km</span>
                                <span ${scoped_symbol} class="create-time">${new Date(mission.createTime).toLocaleString()}</span>
                            </div>
                            <div ${scoped_symbol} class="drawer-buttons">
                                <button ${scoped_symbol} class="search-btn">查看</button>
                                <button ${scoped_symbol} class="edit-btn">编辑</button>
                                <button ${scoped_symbol} class="delete-btn">删除</button>
                            </div>
                        </li>`;
                    },

                    // 渲染空任务列表
                    renderEmptyMissionList() {
                        const scoped_symbol = Utils.ui.getScoped($("#floatingMissionList")) || '';
                        $('#floatingMissionList').html(`<li ${scoped_symbol} class="mission-item-empty">暂无任务</li>`);
                    },

                    // 渲染错误状态的任务列表
                    renderErrorMissionList() {
                        const scoped_symbol = Utils.ui.getScoped($("#floatingMissionList")) || '';
                        $('#floatingMissionList').html(`<li ${scoped_symbol} class="mission-item-empty">加载失败，请重试</li>`);
                    }
                };

                missionLayer.render(missionLayer);

                // ========== 事件绑定 ==========
                function bindMissionEvents() {
                    const $listContainer = $('#floatingMissionList');

                    // 任务项悬停效果 - 鼠标悬停时高亮显示
                    $listContainer.on('mouseenter', 'li', function () {
                        const $li = $(this);
                        if (!$li.hasClass('selected')) {
                            $li.addClass('hovered');
                        }
                    }).on('mouseleave', 'li', function () {
                        const $li = $(this);
                        if (!$li.hasClass('selected')) {
                            $li.removeClass('hovered');
                        }
                    });

                    // 切换任务函数 - 加载并显示指定任务
                    function switchToMission($li) {
                        // 设置当前选中状态
                        $('.mission-item').removeClass('selected').removeClass('hovered');
                        $li.addClass('selected').addClass('hovered');

                        // 加载任务数据
                        const missionId = $li.data('id');
                        $('#missionName').val($li.find('.mission-name').text().trim());
                        missionLayer.loadMissionForEdit(missionId);
                    }

                    // 查看按钮点击事件 - 查看任务详情（只读模式）
                    $listContainer.off('click', '.search-btn').on('click', '.search-btn', function (e) {
                        e.stopPropagation();
                        const $clickedLi = $(this).closest("li");
                        const missionId = $clickedLi.data('id');
                        const $editingLi = $listContainer.find('li[data-editing="true"]');

                        // 检查是否有任务正在编辑
                        if ($editingLi.length > 0) {
                            // 如果点击的是正在编辑的任务，直接退出编辑模式切换到查看
                            if ($editingLi.is($clickedLi)) {
                                exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                                switchToViewMode($clickedLi);
                                return;
                            }

                            // 如果点击的是其他任务，询问是否放弃当前编辑
                            vui.confirm("当前正在编辑其他任务，切换将放弃更改，确定要继续吗？", "放弃编辑确认", function() {
                                exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                                switchToViewMode($clickedLi);
                            });
                            return;
                        }

                        // 检查是否正在新建任务
                        if (missionLayer.isEditing && !missionLayer.editingMissionId) {
                            vui.confirm("当前正在新建任务，切换将放弃新建，确定吗？", "放弃新建确认", function() {
                                switchToViewMode($clickedLi);
                            });
                            return;
                        }

                        // 正常切换到查看模式
                        switchToViewMode($clickedLi);
                    });

                    // 切换到查看模式的函数
                    function switchToViewMode($li) {
                        // 确保退出编辑状态
                        missionLayer.isEditing = false;
                        missionLayer.editingMissionId = null;

                        // 隐藏编辑面板，显示推送面板
                        $('#editDrawer').removeClass('active');
                        $('#upload').addClass('active');

                        // 切换任务并设置为只读模式
                        switchToMission($li);
                    }

                    // 任务项点击事件 - 处理任务选择和编辑状态切换
                    $listContainer.off('click', '.mission-item').on('click', '.mission-item', function (e) {
                        if (!$(e.target).closest('.drawer-buttons').length) {
                            const $clickedLi = $(this);
                            const $editingLi = $listContainer.find('li[data-editing="true"]');

                            // 如果正在编辑一个已存在的任务
                            if ($editingLi.length > 0) {
                                // 如果点击的是正在编辑的任务，则退出编辑
                                if ($editingLi.is($clickedLi)) {
                                    exitEditMode($editingLi, $editingLi.find('.edit-btn'));
                                    return;
                                }

                                // 如果点击的是其他任务，需要确认
                                vui.confirm("当前正在编辑任务，切换任务将放弃更改，确定要继续吗？", "退出编辑确认", function() {
                                    exitEditMode($editingLi, $editingLi.find('.edit-btn'), false); // 退出但不重载，因为马上要切换
                                    switchToMission($clickedLi);
                                });
                                return;
                            }

                            // 如果正在新建一个任务
                            if(missionLayer.isEditing) {
                                vui.confirm("当前正在新建任务，切换将放弃新建，确定吗？", "确认切换", function() {
                                    missionLayer.isEditing = false;
                                    $('#editDrawer').removeClass('active');
                                    switchToMission($clickedLi);
                                });
                                return;
                            }

                            // 正常切换任务（无编辑状态）
                            switchToMission($clickedLi);
                            $('#upload').addClass('active');
                        }
                    });

                    // 编辑按钮点击事件 - 进入或退出编辑模式
                    $listContainer.off('click', '.edit-btn').on('click', '.edit-btn', function (e) {
                        e.stopPropagation();

                        const $btn = $(this);
                        const $li = $btn.closest('li');
                        const missionId = $li.data('id');
                        const missionName = $li.find('.mission-name').text().trim();
                        const $editingLi = $listContainer.find('li[data-editing="true"]');

                        // 更新选中状态
                        $('.mission-item').removeClass('selected').removeClass('hovered');
                        $li.addClass('selected').addClass('hovered');

                        // 如果当前任务正在编辑，则退出编辑模式
                        if ($li.attr('data-editing') === 'true') {
                            vui.confirm(`确定要退出任务 "${missionName}" 的编辑模式吗？未保存的更改将丢失。`, "退出编辑确认", function() {
                                exitEditMode($li, $btn);
                                showMessage("已退出编辑任务模式");
                            });
                            return;
                        }

                        // 检查是否有其他任务正在编辑
                        if ($editingLi.length > 0 && !$editingLi.is($li)) {
                            const editingMissionName = $editingLi.find('.mission-name').text().trim();
                            vui.confirm(`当前正在编辑任务 "${editingMissionName}"，切换将放弃更改。确定要编辑任务 "${missionName}" 吗？`, "切换编辑确认", function() {
                                exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                                enterEditMode($li, $btn, missionId);
                                showMessage(`已切换到编辑任务 "${missionName}"`);
                            });
                            return;
                        }

                        // 检查是否正在新建任务
                        if (missionLayer.isEditing && !missionLayer.editingMissionId) {
                            vui.confirm(`当前正在新建任务，切换将放弃新建。确定要编辑任务 "${missionName}" 吗？`, "放弃新建确认", function() {
                                // 清理新建状态
                                missionLayer.isEditing = false;
                                missionLayer.viewer.entities.removeAll();
                                missionLayer.pointData = [];
                                missionLayer.selectedEntity = null;
                                missionLayer.lineEntity = null;

                                enterEditMode($li, $btn, missionId);
                                showMessage(`已切换到编辑任务 "${missionName}"`);
                            });
                            return;
                        }

                        // 正常进入编辑模式
                        enterEditMode($li, $btn, missionId);
                        showMessage(`已进入编辑任务 "${missionName}" 模式`);
                    });

                    // 删除按钮点击事件 - 删除指定任务
                    $listContainer.off('click', '.delete-btn').on('click', '.delete-btn', function (e) {
                        e.stopPropagation();

                        const $btn = $(this);
                        const $li = $btn.closest('li');
                        const missionId = $li.data('id');
                        const missionName = $li.find('.mission-name').text().trim();
                        const isCurrentlyEditing = $li.attr('data-editing') === 'true';
                        const $editingLi = $listContainer.find('li[data-editing="true"]');

                        // 构建确认消息
                        let confirmMessage = `确定要删除任务 "${missionName}" 吗？`;
                        if (isCurrentlyEditing) {
                            confirmMessage = `任务 "${missionName}" 正在编辑中，删除将丢失所有未保存的更改。确定要删除吗？`;
                        } else if ($editingLi.length > 0 && !$editingLi.is($li)) {
                            confirmMessage = `当前有其他任务正在编辑，确定要删除任务 "${missionName}" 吗？`;
                        }

                        vui.confirm(confirmMessage, '删除确认', function () {
                            // 执行删除操作
                            performDeleteMission(missionId, missionName, $li, isCurrentlyEditing);
                        });
                    });

                    // ========== 任务删除处理 ==========
                    function performDeleteMission(missionId, missionName, $li, isCurrentlyEditing) {
                        Utils.http.request({
                            url: `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.DELETE}`,
                            method: 'DELETE',
                            contentType: 'application/json',
                            data: JSON.stringify(missionId)
                        }).then(res => {
                            if (res.success) {
                                Utils.ui.showMessage(`任务 "${missionName}" 删除成功`);
                                handleDeleteSuccess(missionId, isCurrentlyEditing);
                            } else {
                                Utils.ui.showMessage(res.message || "删除失败", 'error');
                            }
                        }).catch(xhr => {
                            Utils.http.handleError(xhr, '删除任务');
                        });
                    }

                    // 处理删除成功后的清理工作
                    function handleDeleteSuccess(missionId, isCurrentlyEditing) {
                        // 如果删除的是正在编辑的任务，清理编辑状态
                        if (isCurrentlyEditing || missionLayer.editingMissionId === missionId) {
                            missionLayer.resetState();
                        }

                        // 重新加载任务列表
                        missionLayer.loadMissionList();
                    }
                }

                // 进入编辑模式 - 设置任务为可编辑状态
                function enterEditMode($li, $btn, missionId) {
                    const $listContainer = $('#floatingMissionList');
                    // 清除其他编辑状态 - 确保同时只有一个任务处于编辑状态
                    $listContainer.find('.mission-item').each(function() {
                        const $item = $(this);
                        if (!$item.is($li) && $item.attr('data-editing') === 'true') {
                            exitEditMode($item, $item.find('.edit-btn'),false);
                        }
                    });

                    // 设置当前任务编辑状态
                    $li.attr('data-editing', 'true');
                    $btn.text('退出编辑');

                    missionLayer.editingMissionId = missionId;
                    missionLayer.loadMissionForEdit(missionId);
                    missionLayer.isEditing = true;
                    $('#editDrawer').addClass('active').removeClass('point-is-selected');
                    $('#upload').removeClass('active');
                }

                // 退出编辑模式 - 恢复任务为只读状态
                function exitEditMode($li, $btn, isexit = true) {
                    if (!$li || !$btn) return;
                    $li.removeAttr('data-editing');
                    $btn.text('编辑');
                    missionLayer.isEditing = false;
                    $('#editDrawer').removeClass('active');
                    $('#upload').addClass('active');
                    var missionId = $li.data('id');
                    if (missionId && isexit) {
                        missionLayer.loadMissionForEdit(missionId);
                    }
                    missionLayer.hidePointDrawer(); // 该函数会清空高度等信息
                }

                // ========== 其他事件处理 ==========
                // 添加新任务 - 清空地图并进入新建模式
                $("#addNewMissionBtn").on("click", function () {
                    // 1. 如果当前在编辑一个任务，先退出
                    const $editingLi = $('#floatingMissionList').find('li[data-editing="true"]');
                    if ($editingLi.length > 0) {
                        exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                    }

                    // 2. 清理地图和数据
                    missionLayer.viewer.entities.removeAll();
                    missionLayer.pointData = [];
                    missionLayer.editingMissionId = null;
                    missionLayer.selectedEntity = null;
                    missionLayer.lineEntity = null;

                    // 3. 设置UI为新建模式
                    missionLayer.isEditing = true;
                    $('.mission-item').removeClass('selected').removeClass('hovered');
                    $('#editDrawer').addClass('active').removeClass('point-is-selected');
                    $('#speed').val(DEFAULT_SPEED);
                    $('#height').val(20);
                    $('#latDisplay').text('--');
                    $('#lngDisplay').text('--');
                    $('#missionName').val("");
                    $('#upload').removeClass('active');
                    showMessage("已进入新建任务模式，请在地图上添加航点");
                });

                // ========== 任务推送处理 ==========
                function uploadMission(type) {
                    const selectedMission = getSelectedMission();
                    if (!selectedMission) return;

                    const missionId = selectedMission.data("id");

                    Utils.http.request({
                        url: `${CONFIG.API.BASE_URL}${CONFIG.API.ENDPOINTS.UPLOAD}`,
                        method: 'GET',
                        data: { id: missionId, type: type }
                    }).then(res => {
                        if (res.success) {
                            const message = type === 1 ? "已将任务推送并执行" : "已将任务推送";
                            Utils.ui.showMessage(message);
                        } else {
                            Utils.ui.showMessage(res.message || "推送失败", 'error');
                        }
                    }).catch(xhr => {
                        Utils.http.handleError(xhr, '推送任务');
                    });
                }

                // 获取选中的任务
                function getSelectedMission() {
                    const $selected = $("li.selected");
                    if (!$selected.length) {
                        Utils.ui.showMessage("请先选择一个任务", 'error');
                        return null;
                    }
                    return $selected;
                }

                $("#uploadMission").on("click", function () {
                    uploadMission(0);
                });

                $("#uploadMissionStart").on("click", function () {
                    uploadMission(1);
                });

                // ========== 事件处理函数集合 ==========
                // 统一处理速度/高度/航向设置的复选框逻辑
                function setupCheckboxHandler(selector, inputId, globalValueCallback, disabledText) {
                    $(selector).on('change', function() {
                        const isChecked = $(this).prop('checked');
                        const $input = $(inputId);
                        const globalValue = globalValueCallback();

                        if (isChecked) {
                            $input.prop('disabled', true).val(disabledText).addClass('disabled-input-hint');
                        } else {
                            $input.prop('disabled', false).val(globalValue).removeClass('disabled-input-hint');
                        }
                        updateSelectedPointData();
                    });
                }

                // 初始化复选框事件处理器
                function initializeCheckboxHandlers() {
                    DebugHelper.log('初始化复选框事件处理器');

                    // 初始化各设置项
                    setupCheckboxHandler('#isOther', '#speedOther',
                        () => parseFloat($('#speed').val()) || DEFAULT_SPEED, '使用全局速度');
                    setupCheckboxHandler('#isOther2', '#heightOther',
                        () => parseFloat($('#height').val()) || 20, '使用全局高度');
                    setupCheckboxHandler('#isOther3', '#headingOther',
                        () => 0, '使用默认航向');

                    // 输入值变化时更新航点数据
                    $('#speedOther, #heightOther, #headingOther').on('change', updateSelectedPointData);
                }

                // 初始化移动端适配
                function initializeMobileAdaptation() {
                    if (isMobileDevice()) {
                        DebugHelper.log('检测到移动设备，应用移动端适配');

                        // 修改地图操作说明为移动端
                        const scoped_symbol = getScoped($(".map-tips-box"));
                        $('.map-tips-box .tips-content').html(`
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">单指点击</span>
                                <span ${scoped_symbol} class="tip-value">选择航点</span>
                            </div>
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">长按</span>
                                <span ${scoped_symbol} class="tip-value">新增航点</span>
                            </div>
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">拖动航点</span>
                                <span ${scoped_symbol} class="tip-value">调整位置</span>
                            </div>
                            <div ${scoped_symbol} class="tip-item">
                                <span ${scoped_symbol} class="tip-key">双指缩放</span>
                                <span ${scoped_symbol} class="tip-value">地图缩放</span>
                            </div>
                        `);
                    }
                }

                // 初始化窗口事件
                function initializeWindowEvents() {
                    DebugHelper.log('初始化窗口事件');

                    // 窗口大小变化时自动全屏
                    $(window).on('resize', PerformanceHelper.throttle(function() {
                        try {
                            const index = parent.layer.getFrameIndex(window.name);
                            if (index) {
                                parent.layer.full(index);
                            }
                        } catch (e) {
                            DebugHelper.error("Layer full screen failed on resize:", e);
                        }
                    }, 250));
                }

                // 移动设备检测函数
                function isMobileDevice() {
                    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
                        || ('ontouchstart' in window && navigator.maxTouchPoints > 0);
                }

                // 2. 更新选中航点的数据函数
                function updateSelectedPointData() {
                    if (!missionLayer.selectedEntity) return;
                    
                    const index = missionLayer.pointData.findIndex(p => p.id === missionLayer.selectedEntity.id);
                    if (index === -1) return;
                    
                    // 处理速度设置
                    if ($('#isOther').prop('checked')) {
                        // 使用全局速度，删除特定速度
                        delete missionLayer.pointData[index].speed;
                    } else {
                        // 使用特定速度
                        const speed = parseFloat($('#speedOther').val());
                        if (!isNaN(speed)) {
                            missionLayer.pointData[index].speed = speed;
                        }
                    }
                    
                    // 处理高度设置
                    if ($('#isOther2').prop('checked')) {
                        // 使用全局高度，删除特定高度
                        delete missionLayer.pointData[index].height;
                    } else {
                        // 使用特定高度
                        const height = parseFloat($('#heightOther').val());
                        if (!isNaN(height)) {
                            missionLayer.pointData[index].height = height;
                        }
                    }
                    
                    // 处理航向设置
                    if ($('#isOther3').prop('checked')) {
                        // 使用默认航向，删除特定航向
                        delete missionLayer.pointData[index].heading;
                    } else {
                        // 使用特定航向
                        const heading = parseFloat($('#headingOther').val());
                        if (!isNaN(heading)) {
                            missionLayer.pointData[index].heading = heading;
                        }
                    }
                }

                // 删除航点 - 移除当前选中的航点
                $("#deletePoint").on("click", function () {
                    missionLayer.removePoint();
                });

                // 关闭窗口 - 退出任务规划页面
                $("#close").on("click", function () {
                    vui.confirm("确定要退出规划吗？", "退出确认", () => {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                });

                // 保存任务 - 保存当前编辑的任务数据
                $("#saveMission").on("click", function () {
                    const vehicleId = @Model.id;
                    let missionName = $('#missionName').val()?.trim();
                    const description = 'Auto-generated mission';
                    const speed = parseFloat($('#speed').val()) || DEFAULT_SPEED;
                    const height = parseFloat($('#height').val()) || 20;
                    
                    async function doSave(missionName) {
                        // 使用数据管理模块进行验证和保存
                        const missionData = {
                            missionName: missionName,
                            pointData: missionLayer.pointData
                        };

                        if (!MissionDataManager.validateMissionData(missionData)) {
                            return;
                        }

                        const missionId = missionLayer.editingMissionId;

                        try {
                            DebugHelper.time('保存任务');
                            const result = await MissionDataManager.saveMission(
                                missionName,
                                missionLayer.pointData,
                                missionId
                            );
                            DebugHelper.timeEnd('保存任务');

                            if (result.success) {
                                handleSaveSuccess(missionId);
                            }
                        } catch (error) {
                            DebugHelper.error('保存任务失败', error);
                        }
                    }

                    // 处理保存成功后的状态更新
                    function handleSaveSuccess(missionId) {
                        missionLayer.loadMissionList();

                        if (!missionId) {
                            // 成功添加新任务 - 退出新建模式
                            missionLayer.resetState();
                        } else {
                            // 成功更新现有任务 - 自动退出编辑模式
                            const $savedLi = $('#floatingMissionList').find(`li[data-id="${missionId}"]`);
                            if ($savedLi.length > 0) {
                                exitEditMode($savedLi, $savedLi.find('.edit-btn'), true);
                            } else {
                                missionLayer.resetState();
                            }
                        }
                    }

                    // 如果任务名称为空，弹出输入框
                    if (!missionName) {
                        vui.message("","请填写任务名",1,0,function(s){
                            doSave(s && s.trim());
                        },missionName);
                        return;
                    }
                    doSave(missionName);
                });

                // ========== 统一初始化管理器 ==========
                const InitializationManager = {
                    // 初始化状态
                    state: {
                        isInitializing: false,
                        isInitialized: false,
                        currentStep: null,
                        completedSteps: [],
                        failedSteps: []
                    },

                    // 初始化步骤配置
                    steps: [
                        { name: '模块管理器', fn: () => ModuleManager.initialize(), critical: true },
                        { name: '复选框事件', fn: initializeCheckboxHandlers, critical: false },
                        { name: '移动端适配', fn: initializeMobileAdaptation, critical: false },
                        { name: '窗口事件', fn: initializeWindowEvents, critical: false },
                        { name: '任务列表', fn: () => missionLayer.loadMissionList(), critical: true },
                        { name: '任务事件', fn: bindMissionEvents, critical: true }
                    ],

                    // 执行初始化
                    async initialize() {
                        if (this.state.isInitializing || this.state.isInitialized) {
                            DebugHelper.warn('初始化已在进行中或已完成');
                            return;
                        }

                        this.state.isInitializing = true;
                        DebugHelper.time('应用初始化');
                        DebugHelper.log('开始应用初始化流程');

                        try {
                            for (const step of this.steps) {
                                this.state.currentStep = step.name;
                                DebugHelper.log(`初始化步骤: ${step.name}`);

                                await this.executeStep(step);
                                this.state.completedSteps.push(step.name);
                            }

                            this.state.isInitialized = true;
                            this.state.currentStep = null;

                            DebugHelper.log('应用初始化完成');
                            Utils.ui.showMessage('系统初始化完成');

                        } catch (error) {
                            DebugHelper.error('应用初始化失败', error);

                            // 检查是否为关键步骤失败
                            const currentStep = this.steps.find(s => s.name === this.state.currentStep);
                            if (currentStep && currentStep.critical) {
                                Utils.ui.showMessage('系统关键组件初始化失败，请刷新页面重试', 'error');
                            } else {
                                Utils.ui.showMessage('部分功能初始化失败，但系统仍可使用', 'warning');
                            }
                        } finally {
                            this.state.isInitializing = false;
                            DebugHelper.timeEnd('应用初始化');
                        }
                    },

                    // 执行单个初始化步骤
                    async executeStep(step) {
                        const startTime = performance.now();

                        try {
                            const result = step.fn();
                            if (result instanceof Promise) {
                                await result;
                            }

                            const duration = performance.now() - startTime;
                            DebugHelper.log(`✓ ${step.name} 初始化完成 (${duration.toFixed(2)}ms)`);

                        } catch (error) {
                            const duration = performance.now() - startTime;
                            DebugHelper.error(`✗ ${step.name} 初始化失败 (${duration.toFixed(2)}ms)`, error);

                            this.state.failedSteps.push({
                                name: step.name,
                                error: error.message,
                                critical: step.critical
                            });

                            // 如果是关键步骤，抛出错误
                            if (step.critical) {
                                throw error;
                            } else {
                                // 非关键步骤失败，记录但继续执行
                                DebugHelper.warn(`非关键步骤 ${step.name} 失败，继续执行后续步骤`);
                            }
                        }
                    },

                    // 获取初始化状态
                    getStatus() {
                        return {
                            ...this.state,
                            progress: this.state.completedSteps.length / this.steps.length * 100
                        };
                    }
                };

                // ========== 全局初始化状态访问器 ==========
                // 提供给开发者调试使用的全局函数
                window.getInitializationStatus = function() {
                    return InitializationManager.getStatus();
                };

                window.reinitialize = function() {
                    if (confirm('确定要重新初始化系统吗？这将重置所有状态。')) {
                        location.reload();
                    }
                };

                // ========== 文档就绪时统一初始化 ==========
                $(document).ready(function () {
                    DebugHelper.log('DOM加载完成，开始系统初始化');
                    InitializationManager.initialize();
                });



                // 车辆面板相关变量（预留）
                var vehicle_panel = vui.Vehicle;
                var home_position = { lat: NaN, lng: NaN, alt: NaN };
                var home_position_raw = { lat: 0, lng: 0, alt: 0 };
                var home_valid = false;

            });
        });
    </script>
}




