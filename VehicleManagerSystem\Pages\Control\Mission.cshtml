@page "/Control/Mission/{id}/{uid?}/{nonce?}/{token?}"
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<LanguageResource> language
@model VehicleManagerSystem.Pages.Control.MissionModel
@{
    Layout = "_LayoutControl";
}

@section Styles {
    <link href="~/lib/cesium/Widgets/widgets.css" rel="stylesheet" />
}

<main class="main-view">
    <div id="map-view"></div>
    <div class="div-border"></div> <!-- 边框 -->
    <!-- 地图操作提示 -->
    <div class="map-tips-box">
        <div class="tips-header">
            <span>地图操作说明</span>
        </div>

        <div class="tips-content">
            <div class="tip-item">
                <span class="tip-key">左键单击</span>
                <span class="tip-value">选择航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键双击</span>
                <span class="tip-value">新增航点</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">左键拖动</span>
                <span class="tip-value">调整位置</span>
            </div>

            <div class="tip-item">
                <span class="tip-key">滚轮</span>
                <span class="tip-value">地图缩放</span>
            </div>

        </div>
    </div>
    <input type="text" id="missionName" style="display: none;" />
    <div id="editDrawer" class="drawer-panel">
        <div class="drawer-container">
            <div class="drawer-section">
                <div class="drawer-section-header">
                    <span>全局</span>
                </div>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label for="speed">速度(m/s)</label>
                        <input id="speed" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" />
                    </div>
                    <div class="form-group">
                        <label for="height">高度(m)</label>
                        <input id="height" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" />
                    </div>
                </div>
            </div>

            <div id="locationSection" class="drawer-section">
                <div class="drawer-section-header">
                    <span>位置</span>
                </div>
                <div class="drawer-section-body">
                    <div class="form-group">
                        <label>经度</label>
                        <span id="lngDisplay">--</span>
                    </div>
                    <div class="form-group">
                        <label>纬度</label>
                        <span id="latDisplay">--</span>
                    </div>
                </div>
            </div>
            <div id="locationSectionOther" class="drawer-section">
                <div class="drawer-section-header">
                    <span>其他设置</span>
                </div>
                <div class="drawer-section-bodyother">
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther" class="custom-radio" />
                            <label for="isOther" class="custom-radio-label">速度</label>
                        </div>
                        <input id="speedOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther2" class="custom-radio" />
                            <label for="isOther2" class="custom-radio-label">高度</label>
                        </div>
                        <input id="heightOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                    <div class="drawer-section-body-other">
                        <div class="drawer-section-body-other-labelrow">
                            <input type="checkbox" id="isOther3" class="custom-radio" />
                            <label for="isOther3" class="custom-radio-label">航向</label>
                        </div>
                        <input id="headingOther" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '')" disabled />
                    </div>
                </div>
            </div>

            <div class="drawer-section">
                <div class="drawer-section-header"><span>操作</span></div>
                <div class="drawer-section-body">
                    <button id="deletePoint" class="layui-btn layui-btn-danger layui-btn-sm">
                        删除选中点
                    </button>
                    <div class="form-group-button">
                        <button id="saveMission" class="full-width-btn">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="drawer-panel" id="upload">
        <div class="drawer-section" >
            <div class="drawer-section-header"><span>操作</span></div>
            <div class="drawer-section-body">
                <div class="form-group-button">
                    <button id="uploadMission" class="full-width-btn">
                        推送任务
                    </button>
                </div>
                <div class="form-group-button">
                    <button id="uploadMissionStart" class="full-width-btn">
                        推送并执行任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="floating-mission-list">
        <div class="mission-list-header">
            <div class="mission-list-title">航线列表</div>
        </div>
        <ul id="floatingMissionList"></ul>
        <div class="mission-list-footer">
            <div class="addNewMissionBtnDiv">
                <button id="addNewMissionBtn">添加航线</button>
            </div>
        </div>
        <div class="mission-list-border-left-top"></div>
        <div class="mission-list-border-right-bottom"></div>
        <div class="end"></div>
    </div>
</main>

<div id="close" class="close-button" title="退出当前规划">
    <i class="iconfont icon-fanhui1"></i>
</div>

<script src="~/lib/cesium/Cesium.js"></script>
<script src="~/js/signalr.min.js"></script>
<script src="~/js/vui.js"></script>
<script src="~/lib/layuiadmin/layui/layui.js"></script>
@section Scripts {
    <script type="text/javascript">
        layui.use(['layer', 'form', 'jquery'], function () {
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.jquery;

            vui.use(["panel", "aim"], function () {

                // ========== 配置常量模块 ==========
                const CONFIG = {
                    WAYPOINT: {
                        PREFIX: 'point',
                        TYPE: 'custom-point',
                        DEFAULT_SPEED: 10,
                        DEFAULT_HEIGHT: 20,
                        MAX_DRAG_DISTANCE: 50000,
                        COLORS: {
                            NORMAL: '#1976D2',
                            SELECTED: '#2E7D32',
                            OUTLINE: '#FF5722'
                        }
                    },
                    API: {
                        BASE: '/api/Mission',
                        TIMEOUT: 30000
                    },
                    UI: {
                        MESSAGE_DURATION: 1000,
                        LONG_PRESS_DURATION: 1000,
                        DEBOUNCE_DELAY: 300
                    },
                    MAP: {
                        TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1ODJhMjA5Yy02OTU2LTQ4ZmUtYmU2Yi1hMDRlMDNlZjljZmEiLCJpZCI6ODk4NjksImlhdCI6MTY2MjY5NTEwOX0.Z7GZMmZ9sXH740jtl059KaViFXC1Nn0GIjYWyWLKTrg'
                    }
                };

                // ========== 工具函数模块 ==========
                const Utils = {
                    // 获取航点索引
                    getPointIndex(id) {
                        return parseInt(id.replace(CONFIG.WAYPOINT.PREFIX, ''), 10);
                    },

                    // 显示消息提示
                    showMessage(message, type = 'info', duration = CONFIG.UI.MESSAGE_DURATION) {
                        vui.message(message, type === 'error' ? '错误' : '提示', 0, duration);
                    },

                    // 计算航线总距离
                    calculateDistance(points) {
                        if (!points || points.length < 2) return '0.00';
                        let totalDistance = 0;
                        for (let i = 1; i < points.length; i++) {
                            const p1 = points[i - 1];
                            const p2 = points[i];
                            const R = 6371e3; // 地球半径(米)
                            const φ1 = Cesium.Math.toRadians(p1.latitude);
                            const φ2 = Cesium.Math.toRadians(p2.latitude);
                            const Δφ = Cesium.Math.toRadians(p2.latitude - p1.latitude);
                            const Δλ = Cesium.Math.toRadians(p2.longitude - p1.longitude);
                            const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                                    Math.cos(φ1) * Math.cos(φ2) *
                                    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                            totalDistance += R * c;
                        }
                        return (totalDistance / 1000).toFixed(2); // 返回公里数
                    },

                    // 获取作用域标识
                    getScoped($element) {
                        const attrs = Array.from($element[0].getAttributeNames());
                        return attrs.find(attr => attr.startsWith('b-')) || null;
                    },

                    // 防抖函数
                    debounce(func, wait) {
                        let timeout;
                        return function executedFunction(...args) {
                            const later = () => {
                                clearTimeout(timeout);
                                func(...args);
                            };
                            clearTimeout(timeout);
                            timeout = setTimeout(later, wait);
                        };
                    },

                    // 计算航线总距离 - 与 Mission copy.cshtml 保持一致
                    calculateDistance(points) {
                        if (!points || points.length < 2) return '0.00';
                        let totalDistance = 0;
                        for (let i = 1; i < points.length; i++) {
                            const p1 = points[i - 1];
                            const p2 = points[i];
                            const R = 6371e3; // 地球半径(米)
                            const φ1 = Cesium.Math.toRadians(p1.latitude);
                            const φ2 = Cesium.Math.toRadians(p2.latitude);
                            const Δφ = Cesium.Math.toRadians(p2.latitude - p1.latitude);
                            const Δλ = Cesium.Math.toRadians(p2.longitude - p1.longitude);
                            const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                                    Math.cos(φ1) * Math.cos(φ2) *
                                    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                            totalDistance += R * c;
                        }
                        return (totalDistance / 1000).toFixed(2); // 返回公里数
                    },

                    // 获取作用域标识 - 与 Mission copy.cshtml 保持一致
                    getScoped($element) {
                        const attrs = Array.from($element[0].getAttributeNames());
                        return attrs.find(attr => attr.startsWith('b-')) || null;
                    },

                    // 节流函数
                    throttle(func, limit) {
                        let inThrottle;
                        return function() {
                            const args = arguments;
                            const context = this;
                            if (!inThrottle) {
                                func.apply(context, args);
                                inThrottle = true;
                                setTimeout(() => inThrottle = false, limit);
                            }
                        }
                    }
                };

                // ========== 认证管理模块 ==========
                const AuthManager = {
                    // 获取认证头信息
                    getAuthHeaders() {
                        const uid = localStorage.getItem('uid');
                        const token = localStorage.getItem('token');

                        if (!uid || !token) {
                            return null;
                        }

                        return {
                            'uid': uid,
                            'token': token
                        };
                    },

                    // 创建带认证的AJAX请求
                    createAuthenticatedAjax(options) {
                        const authHeaders = this.getAuthHeaders();
                        if (!authHeaders) {
                            Utils.showMessage('用户未登录，请重新登录', 'error');
                            return Promise.reject('Authentication failed');
                        }

                        // 合并认证头到现有headers
                        const headers = Object.assign({}, options.headers || {}, authHeaders);

                        // 创建新的options对象，包含认证头和超时设置
                        const authenticatedOptions = Object.assign({}, options, {
                            headers: headers,
                            timeout: CONFIG.API.TIMEOUT
                        });

                        return $.ajax(authenticatedOptions);
                    }
                };

                // ========== DOM管理模块 ==========
                const DOMManager = {
                    cache: new Map(),

                    // 获取DOM元素（带缓存）
                    get(selector) {
                        if (!this.cache.has(selector)) {
                            this.cache.set(selector, $(selector));
                        }
                        return this.cache.get(selector);
                    },

                    // 批量更新DOM
                    batchUpdate(updates) {
                        const fragment = document.createDocumentFragment();
                        updates.forEach(update => {
                            if (update.element && update.content) {
                                update.element.html(update.content);
                            }
                        });
                    },

                    // 预加载关键元素
                    preloadElements() {
                        const selectors = [
                            '#editDrawer', '#upload', '#floatingMissionList',
                            '#addNewMissionBtn', '#saveMission', '#deletePoint',
                            '#uploadMission', '#uploadMissionStart',
                            '#latDisplay', '#lngDisplay', '#speedOther',
                            '#heightOther', '#headingOther', '#missionName'
                        ];

                        selectors.forEach(selector => this.get(selector));
                    }
                };

                // ========== UI管理模块 ==========
                const UIManager = {
                    drawer: {
                        // 显示编辑抽屉
                        showEditDrawer() {
                            DOMManager.get('#editDrawer').addClass('active');
                            DOMManager.get('#upload').removeClass('active');
                        },

                        // 隐藏编辑抽屉
                        hideEditDrawer() {
                            DOMManager.get('#editDrawer').removeClass('active');
                            DOMManager.get('#upload').addClass('active');
                        },

                        // 显示上传抽屉
                        showUploadDrawer() {
                            DOMManager.get('#upload').addClass('active');
                            DOMManager.get('#editDrawer').removeClass('active');
                        },

                        // 更新位置显示
                        updateLocationDisplay(lat, lng) {
                            DOMManager.get('#latDisplay').text(lat.toFixed(7));
                            DOMManager.get('#lngDisplay').text(lng.toFixed(7));
                        }
                    },

                    missionList: {
                        // 渲染任务列表
                        render(missions) {
                            const $container = DOMManager.get('#floatingMissionList');
                            if (!missions || missions.length === 0) {
                                $container.html('<li class="no-missions">暂无任务</li>');
                                return;
                            }

                            // 获取作用域符号 - 与 Mission copy.cshtml 保持一致
                            const scoped_symbol = Utils.getScoped(DOMManager.get("#floatingMissionList")) || '';

                            const html = missions.map(mission => `<li ${scoped_symbol} data-id="${mission.id}" class="mission-item">
                                                 <div ${scoped_symbol} class="mission-header">
                                                    <span ${scoped_symbol} class="mission-name">${mission.missionName}</span>
                                                    <span ${scoped_symbol} class="point-count">航点数: ${mission.pointData.length}</span>
                                                </div>
                                                <div ${scoped_symbol} class="mission-footer">
                                                    <span ${scoped_symbol} class="distance">${Utils.calculateDistance(mission.pointData)} km</span>
                                                    <span ${scoped_symbol} class="create-time">${new Date(mission.createTime).toLocaleString()}</span>
                                                </div>
                                                <div ${scoped_symbol} class="drawer-buttons">
                                                    <button ${scoped_symbol} class="search-btn">查看</button>
                                                    <button ${scoped_symbol} class="edit-btn">编辑</button>
                                                    <button ${scoped_symbol} class="delete-btn">删除</button>
                                                </div>
                                            </li>`).join('');

                            $container.html(html);
                        },

                        // 选中任务项
                        selectMissionItem($item) {
                            DOMManager.get('.mission-item').removeClass('selected');
                            $item.addClass('selected');
                        },

                        // 清除所有编辑状态
                        clearAllEditingStates() {
                            DOMManager.get('.mission-item').removeAttr('data-editing');
                            DOMManager.get('.edit-btn').text('编辑');
                        }
                    },

                    form: {
                        // 重置任务表单
                        resetMissionForm() {
                            DOMManager.get('#missionName').val('');
                            DOMManager.get('#speed').val(CONFIG.WAYPOINT.DEFAULT_SPEED);
                            DOMManager.get('#height').val(CONFIG.WAYPOINT.DEFAULT_HEIGHT);
                            DOMManager.get('#speedOther').val('').prop('disabled', true);
                            DOMManager.get('#heightOther').val('').prop('disabled', true);
                            DOMManager.get('#headingOther').val('').prop('disabled', true);
                            DOMManager.get('#isOther, #isOther2, #isOther3').prop('checked', false);
                        },

                        // 更新航点表单
                        updateWaypointForm(waypoint) {
                            if (waypoint) {
                                DOMManager.get('#speedOther').val(waypoint.speed || '');
                                DOMManager.get('#heightOther').val(waypoint.height || '');
                                DOMManager.get('#headingOther').val(waypoint.heading || '');
                            }
                        }
                    }
                };

                // ========== 事件管理模块 ==========
                const EventManager = {
                    events: new Map(),

                    // 注册事件监听
                    on(eventName, callback) {
                        if (!this.events.has(eventName)) {
                            this.events.set(eventName, []);
                        }
                        this.events.get(eventName).push(callback);
                    },

                    // 移除事件监听
                    off(eventName, callback) {
                        if (this.events.has(eventName)) {
                            const callbacks = this.events.get(eventName);
                            const index = callbacks.indexOf(callback);
                            if (index > -1) {
                                callbacks.splice(index, 1);
                            }
                        }
                    },

                    // 触发事件
                    emit(eventName, data) {
                        if (this.events.has(eventName)) {
                            this.events.get(eventName).forEach(callback => {
                                try {
                                    callback(data);
                                } catch (error) {
                                    console.error(`Event callback error for ${eventName}:`, error);
                                }
                            });
                        }
                    },

                    // 防抖包装器
                    debounce: Utils.debounce,

                    // 节流包装器
                    throttle: Utils.throttle
                };

                // ========== 状态管理模块 ==========
                const StateManager = {
                    state: {
                        isEditing: false,
                        selectedWaypoint: null,
                        editingMissionId: null,
                        isDragging: false
                    },

                    // 设置状态
                    setState(key, value) {
                        const oldValue = this.state[key];
                        this.state[key] = value;

                        // 触发状态变化事件
                        EventManager.emit('stateChange', {
                            key,
                            oldValue,
                            newValue: value,
                            state: { ...this.state }
                        });
                    },

                    // 获取状态
                    getState(key) {
                        return key ? this.state[key] : { ...this.state };
                    },

                    // 重置状态
                    resetState() {
                        Object.keys(this.state).forEach(key => {
                            this.setState(key, false);
                        });
                    }
                };

                // ========== 地图管理模块 ==========
                const MapManager = {
                    // 初始化地图
                    async initialize(containerId) {
                        try {
                            Cesium.Ion.defaultAccessToken = CONFIG.MAP.TOKEN;

                            const viewer = new Cesium.Viewer(containerId, {
                                terrain: Cesium.Terrain.fromWorldTerrain(),
                                sceneMode: Cesium.SceneMode.SCENE2D,
                                animation: false,
                                timeline: false,
                                fullscreenButton: false,
                                infoBox: false,
                                geocoder: false,
                                homeButton: false,
                                sceneModePicker: false,
                                baseLayerPicker: false,
                                navigationHelpButton: false,
                                selectionIndicator: false,
                                shouldAnimate: true,
                                enableRotate: false,
                                enableTilt: false,
                                orderIndependentTranslucency: true,
                                contextOptions: { webgl: { alpha: true } }
                            });

                            // 隐藏版权信息
                            viewer.cesiumWidget.creditContainer.style.display = 'none';

                            // 设置相机heading重置
                            this.setupCameraHeadingReset(viewer);

                            // 启用抗锯齿
                            viewer.scene.postProcessStages.fxaa.enabled = true;

                            return viewer;
                        } catch (error) {
                            Utils.showMessage("地图初始化失败，请刷新页面重试。", 'error', 3000);
                            throw error;
                        }
                    },

                    // 设置相机heading重置
                    setupCameraHeadingReset(viewer) {
                        let headingResetTimeout;
                        viewer.scene.postRender.addEventListener(() => {
                            if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
                                const heading = viewer.camera.heading;
                                if (Math.abs(heading) > 0.001) {
                                    clearTimeout(headingResetTimeout);
                                    headingResetTimeout = setTimeout(() => {
                                        viewer.camera.setView({ orientation: { heading: 0 } });
                                    }, 100);
                                }
                            }
                        });
                    }
                };

                // ========== 任务图层主模块 ==========
                const missionLayer = {
                    viewer: null,
                    pointCounter: 0,
                    pointData: [],
                    lineEntity: null,
                    selectedEntity: null,
                    editingMissionId: null,
                    isEditing: false,
                    loaded: false,

                    // 渲染地图
                    async render(container) {
                        try {
                            this.viewer = await MapManager.initialize('map-view');
                            container.viewer = this.viewer;

                            // 初始化拖拽模块
                            this.dragModule.init(this.viewer, this);

                            this.loaded = true;
                            container.loaded = true;

                            EventManager.emit('mapLoaded', { viewer: this.viewer });
                        } catch (error) {
                            console.error('地图渲染失败:', error);
                            throw error;
                        }
                    },

                    // 更新航线
                    updateLine() {
                        if (this.lineEntity) {
                            this.viewer.entities.remove(this.lineEntity);
                        }

                        if (this.pointData.length > 1) {
                            const positions = this.pointData.map(point => point.position);
                            this.lineEntity = this.viewer.entities.add({
                                polyline: {
                                    positions: positions,
                                    width: 3,
                                    material: Cesium.Color.fromCssColorString('#FF5722'),
                                    clampToGround: true
                                }
                            });
                        }
                    },

                    // 显示航点抽屉
                    showPointDrawer(entity) {
                        if (!entity) return;

                        const cartographic = Cesium.Cartographic.fromCartesian(entity.position.getValue());
                        UIManager.drawer.updateLocationDisplay(
                            Cesium.Math.toDegrees(cartographic.latitude),
                            Cesium.Math.toDegrees(cartographic.longitude)
                        );

                        UIManager.drawer.showEditDrawer();
                        DOMManager.get('#editDrawer').addClass('point-is-selected');

                        // 更新表单数据
                        const pointIndex = this.pointData.findIndex(p => p.id === entity.id);
                        if (pointIndex !== -1) {
                            UIManager.form.updateWaypointForm(this.pointData[pointIndex]);
                        }

                        StateManager.setState('selectedWaypoint', entity);
                    },

                    // 隐藏航点抽屉
                    hidePointDrawer() {
                        DOMManager.get('#editDrawer').removeClass('point-is-selected');
                        UIManager.drawer.updateLocationDisplay(0, 0);
                        StateManager.setState('selectedWaypoint', null);
                    },

                    // 创建航点实体
                    createWaypointEntity(id, position, index) {
                        return this.viewer.entities.add({
                            id: id,
                            position: new Cesium.ConstantPositionProperty(position),
                            type: CONFIG.WAYPOINT.TYPE,
                            point: {
                                color: Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL),
                                pixelSize: 24,
                                outlineColor: Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.OUTLINE),
                                outlineWidth: 3
                            },
                            label: {
                                text: String(index),
                                font: '30px sans-serif',
                                scale: 0.5,
                                fillColor: Cesium.Color.WHITE,
                                style: Cesium.LabelStyle.FILL,
                                verticalOrigin: Cesium.VerticalOrigin.CENTER,
                                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                                disableDepthTestDistance: Number.POSITIVE_INFINITY
                            }
                        });
                    },

                    // 选择航点
                    selectWaypoint(entity) {
                        // 重置之前选中的航点
                        if (this.selectedEntity && this.selectedEntity !== entity) {
                            this.selectedEntity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.NORMAL);
                        }

                        // 设置新选中的航点
                        this.selectedEntity = entity;
                        if (entity) {
                            entity.point.color = Cesium.Color.fromCssColorString(CONFIG.WAYPOINT.COLORS.SELECTED);
                            this.showPointDrawer(entity);
                        } else {
                            this.hidePointDrawer();
                        }
                    },

                    // 清除所有航点
                    clearAllPoints() {
                        this.viewer.entities.removeAll();
                        this.pointData = [];
                        this.lineEntity = null;
                        this.selectedEntity = null;
                        this.pointCounter = 0;
                        this.hidePointDrawer();
                    },

                    // 拖拽模块
                    dragModule: {
                        viewer: null,
                        missionLayer: null,
                        handler: null,
                        isDragging: false,
                        draggingEntity: null,
                        originalDragPosition: null,

                        // 初始化拖拽模块
                        init(viewer, missionLayer) {
                            this.viewer = viewer;
                            this.missionLayer = missionLayer;
                            this.handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
                            this.setupEvents();
                            this.setupTouchEvents();
                        },

                        // 设置鼠标事件
                        setupEvents() {
                            const self = this;

                            // 点选功能 - 左键单击选择航点
                            this.handler.setInputAction((click) => {
                                if (!self.missionLayer.isEditing) return;
                                const pickedObject = self.viewer.scene.pick(click.position);

                                if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                                    const entity = pickedObject.id;
                                    self.missionLayer.selectWaypoint(entity);
                                } else {
                                    // 点击空白区域，取消选择
                                    self.missionLayer.selectWaypoint(null);
                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

                            // 新增点功能 - 左键双击添加航点
                            this.handler.setInputAction((click) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.loaded) return;
                                const cartesian = self.viewer.scene.pickPosition(click.position);
                                if (!Cesium.defined(cartesian)) return;

                                self.addWaypoint(cartesian);
                            }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

                            // 开始拖拽 - 左键按下时触发
                            this.handler.setInputAction((movement) => {
                                if (!self.missionLayer.isEditing || !self.missionLayer.selectedEntity) return;
                                const pickedObject = self.viewer.scene.pick(movement.position);
                                if (pickedObject && pickedObject.id === self.missionLayer.selectedEntity) {
                                    self.startDrag(self.missionLayer.selectedEntity);
                                }
                            }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

                            // 拖拽过程 - 鼠标移动
                            this.handler.setInputAction((movement) => {
                                if (!this.isDragging || !this.draggingEntity) return;

                                const cartesian = this.viewer.scene.pickPosition(movement.endPosition);
                                if (!Cesium.defined(cartesian)) return;

                                this.updateDragPosition(cartesian);
                            }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

                            // 结束拖拽 - 左键释放时触发
                            this.handler.setInputAction(() => {
                                this.endDrag();
                            }, Cesium.ScreenSpaceEventType.LEFT_UP);
                        },

                        // 设置触摸事件
                        setupTouchEvents() {
                            const self = this;
                            let touchTimer = null;
                            let lastTouchPos = null;
                            let draggingTouchId = null;
                            let draggingEntity = null;

                            // 长按新增航点
                            this.viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!self.missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    lastTouchPos = {
                                        x: e.touches[0].clientX,
                                        y: e.touches[0].clientY
                                    };
                                    touchTimer = setTimeout(function () {
                                        const rect = self.viewer.canvas.getBoundingClientRect();
                                        const pos = {
                                            x: lastTouchPos.x - rect.left,
                                            y: lastTouchPos.y - rect.top
                                        };
                                        const cartesian = self.viewer.scene.pickPosition(pos);
                                        if (Cesium.defined(cartesian)) {
                                            self.addWaypoint(cartesian);
                                        }
                                    }, CONFIG.UI.LONG_PRESS_DURATION);
                                }
                            });

                            this.viewer.canvas.addEventListener('touchend', function (e) {
                                if (touchTimer) {
                                    clearTimeout(touchTimer);
                                    touchTimer = null;
                                }
                            });

                            // 触摸拖动航点
                            this.viewer.canvas.addEventListener('touchstart', function (e) {
                                if (!self.missionLayer.isEditing) return;
                                if (e.touches.length === 1) {
                                    const touch = e.touches[0];
                                    const rect = self.viewer.canvas.getBoundingClientRect();
                                    const pickPos = {
                                        x: touch.clientX - rect.left,
                                        y: touch.clientY - rect.top
                                    };
                                    const pickedObject = self.viewer.scene.pick(pickPos);
                                    if (pickedObject && pickedObject.id && pickedObject.id.type === CONFIG.WAYPOINT.TYPE) {
                                        draggingTouchId = touch.identifier;
                                        draggingEntity = pickedObject.id;
                                        self.viewer.scene.screenSpaceCameraController.enableTranslate = false;
                                        self.viewer.scene.screenSpaceCameraController.enableTilt = false;
                                    }
                                }
                            });

                            this.viewer.canvas.addEventListener('touchmove', function (e) {
                                if (draggingTouchId !== null && draggingEntity) {
                                    for (let i = 0; i < e.touches.length; i++) {
                                        if (e.touches[i].identifier === draggingTouchId) {
                                            const touch = e.touches[i];
                                            const rect = self.viewer.canvas.getBoundingClientRect();
                                            const pos = {
                                                x: touch.clientX - rect.left,
                                                y: touch.clientY - rect.top
                                            };
                                            const cartesian = self.viewer.scene.pickPosition(pos);
                                            if (Cesium.defined(cartesian)) {
                                                self.updateEntityPosition(draggingEntity, cartesian);
                                            }
                                            break;
                                        }
                                    }
                                }
                            });

                            this.viewer.canvas.addEventListener('touchend', function (e) {
                                if (draggingTouchId !== null) {
                                    self.viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                    self.viewer.scene.screenSpaceCameraController.enableTilt = true;
                                    draggingTouchId = null;
                                    draggingEntity = null;
                                }
                            });
                        },

                        // 添加航点
                        addWaypoint(cartesian) {
                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                            const newPosition = Cesium.Cartesian3.fromRadians(
                                cartographic.longitude,
                                cartographic.latitude,
                                parseFloat(cartographic.height.toFixed(2))
                            );

                            const newId = CONFIG.WAYPOINT.PREFIX + (++this.missionLayer.pointCounter);
                            const newEntity = this.missionLayer.createWaypointEntity(
                                newId,
                                newPosition,
                                this.missionLayer.pointData.length + 1
                            );

                            // 添加到数据数组并更新连线 - 与 Mission copy.cshtml 保持一致的数据结构
                            this.missionLayer.pointData.push({
                                id: newId,
                                position: newPosition
                            });
                            this.missionLayer.updateLine();

                            // 自动选中新创建的航点
                            this.missionLayer.selectWaypoint(newEntity);
                        },

                        // 开始拖拽
                        startDrag(entity) {
                            if (!entity) return;
                            this.isDragging = true;
                            this.draggingEntity = entity;
                            this.originalDragPosition = entity.position.getValue(this.viewer.clock.currentTime);
                            // 禁用相机平移和倾斜，防止拖拽时意外移动地图
                            this.viewer.scene.screenSpaceCameraController.enableTranslate = false;
                            this.viewer.scene.screenSpaceCameraController.enableTilt = false;
                            entity.point.color = Cesium.Color.fromCssColorString('#7B1FA2');
                            StateManager.setState('isDragging', true);
                        },

                        // 更新拖拽位置
                        updateDragPosition(cartesian) {
                            const cartographic = Cesium.Cartographic.fromCartesian(cartesian);

                            // 验证坐标有效性
                            if (isNaN(cartographic.longitude) || isNaN(cartographic.latitude)) return;

                            this.missionLayer.selectedEntity.position.getValue();
                            const newPosition = Cesium.Cartesian3.fromRadians(
                                cartographic.longitude,
                                cartographic.latitude,
                                cartographic.height.toFixed(2)
                            );

                            // 检查拖拽距离限制
                            if (this.originalDragPosition) {
                                const distance = Cesium.Cartesian3.distance(this.originalDragPosition, newPosition);
                                if (distance > CONFIG.WAYPOINT.MAX_DRAG_DISTANCE) return;
                            }

                            this.updateEntityPosition(this.draggingEntity, newPosition);
                        },

                        // 更新实体位置
                        updateEntityPosition(entity, position) {
                            // 更新实体位置
                            entity.position = new Cesium.ConstantPositionProperty(position);

                            // 更新数据数组
                            const index = this.missionLayer.pointData.findIndex(p => p.id === entity.id);
                            if (index !== -1) {
                                this.missionLayer.pointData[index].position = position;
                            }

                            // 实时更新UI
                            const cartographic = Cesium.Cartographic.fromCartesian(position);
                            DOMManager.get('#latDisplay').text(Cesium.Math.toDegrees(cartographic.latitude).toFixed(7));
                            DOMManager.get('#lngDisplay').text(Cesium.Math.toDegrees(cartographic.longitude).toFixed(7));

                            this.missionLayer.updateLine();
                        },

                        // 结束拖拽
                        endDrag() {
                            if (this.isDragging && this.draggingEntity) {
                                // 恢复相机控制
                                this.viewer.scene.screenSpaceCameraController.enableTranslate = true;
                                this.viewer.scene.screenSpaceCameraController.enableTilt = true;
                                this.draggingEntity.point.color = Cesium.Color.fromCssColorString('#2E7D32');

                                // 简化：直接更新高度显示
                                const cartographic = Cesium.Cartographic.fromCartesian(this.draggingEntity.position.getValue());
                            }
                            this.isDragging = false;
                            this.draggingEntity = null;
                            this.originalDragPosition = null;
                            StateManager.setState('isDragging', false);
                        }
                    },

                    // ========== 任务管理功能 ==========

                    // 开始编辑模式
                    startEditing(missionId = null) {
                        this.isEditing = true;
                        this.editingMissionId = missionId;
                        StateManager.setState('isEditing', true);
                        StateManager.setState('editingMissionId', missionId);
                        UIManager.drawer.showEditDrawer();
                    },

                    // 停止编辑模式
                    stopEditing() {
                        this.isEditing = false;
                        this.editingMissionId = null;
                        StateManager.setState('isEditing', false);
                        StateManager.setState('editingMissionId', null);
                        UIManager.drawer.hideEditDrawer();
                        this.selectWaypoint(null);
                    },

                    // 保存任务
                    async saveMission() {
                        try {
                            if (this.pointData.length === 0) {
                                Utils.showMessage('请添加至少一个航点', 'error');
                                return false;
                            }

                            let missionName = DOMManager.get('#missionName').val()?.trim();

                            // 如果任务名称为空，弹出输入框
                            if (!missionName) {
                                return new Promise((resolve) => {
                                    vui.message("", "请填写任务名", 1, 0, (inputName) => {
                                        if (inputName && inputName.trim()) {
                                            this.doSaveMission(inputName.trim()).then(resolve);
                                        } else {
                                            Utils.showMessage("任务名称不能为空", 'error');
                                            resolve(false);
                                        }
                                    }, missionName);
                                });
                            }

                            return await this.doSaveMission(missionName);
                        } catch (error) {
                            console.error('保存任务失败:', error);
                            Utils.showMessage('保存任务失败，请重试', 'error');
                            return false;
                        }
                    },

                    // 执行保存任务的具体逻辑
                    async doSaveMission(missionName) {
                        try {
                            const globalSpeed = parseFloat(DOMManager.get('#speed').val()) || CONFIG.WAYPOINT.DEFAULT_SPEED;
                            const globalHeight = parseFloat(DOMManager.get('#height').val()) || CONFIG.WAYPOINT.DEFAULT_HEIGHT;

                            // 构建航点数据 - 与 Mission copy.cshtml 保持一致的格式
                            const points = this.pointData.map((p, index) => {
                                const cartographic = Cesium.Cartographic.fromCartesian(p.position);

                                return {
                                    id: Utils.getPointIndex(p.id),
                                    longitude: Cesium.Math.toDegrees(cartographic.longitude),
                                    latitude: Cesium.Math.toDegrees(cartographic.latitude),
                                    // 使用点特定的设置或全局设置
                                    altitude: p.height !== undefined ? p.height : globalHeight,
                                    speed: p.speed !== undefined ? p.speed : globalSpeed,
                                    heading: p.heading !== undefined ? p.heading : 0xFFFF
                                };
                            });

                            // 构建保存数据 - 与 Mission copy.cshtml 保持一致的格式
                            const payload = {
                                missionName,
                                vehicleId: @Model.id,
                                missionDescription: 'Auto-generated mission',
                                pointData: points,
                                speed: globalSpeed,
                                height: globalHeight
                            };

                            // 如果是更新操作，添加 Id 字段
                            const missionId = this.editingMissionId;
                            if (missionId) {
                                payload.Id = missionId;
                            }

                            // 根据是否有编辑ID决定使用 add 还是 update 端点和方法
                            const url = missionId ? `${CONFIG.API.BASE}/update` : `${CONFIG.API.BASE}/add`;
                            const method = missionId ? 'PUT' : 'POST';

                            const response = await AuthManager.createAuthenticatedAjax({
                                url: url,
                                method: method,
                                contentType: 'application/json',
                                data: JSON.stringify(payload)
                            });

                            if (response.success) {
                                Utils.showMessage('任务保存成功');
                                EventManager.emit('missionSaved', { missionId: response.data?.missionId });

                                // 刷新任务列表
                                await MissionListManager.refreshMissionList();

                                // 保存成功后的状态管理
                                if (!this.editingMissionId) {
                                    // 成功添加新任务 - 退出新建模式
                                    this.editingMissionId = null;
                                    this.isEditing = false;
                                    DOMManager.get('#editDrawer').removeClass('active');
                                } else {
                                    // 成功更新现有任务 - 自动退出编辑模式
                                    const $savedLi = DOMManager.get('#floatingMissionList').find(`li[data-id="${this.editingMissionId}"]`);
                                    if ($savedLi.length > 0) {
                                        // 调用exitEditMode函数，确保UI状态正确更新
                                        AppController.exitEditMode($savedLi, $savedLi.find('.edit-btn'), true);
                                    } else {
                                        // 如果列表项未找到，重置状态
                                        this.isEditing = false;
                                        DOMManager.get('#editDrawer').removeClass('active');
                                    }
                                }

                                return true;
                            } else {
                                Utils.showMessage(response.message || '保存失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('保存任务失败:', error);

                            // 详细的错误处理 - 与 Mission copy.cshtml 保持一致
                            if (error.status === 403) {
                                Utils.showMessage('权限不足，无法保存该任务', 'error');
                            } else if (error.status === 400) {
                                Utils.showMessage('任务数据格式错误', 'error');
                            } else if (error.status === 404) {
                                Utils.showMessage('API接口未找到', 'error');
                            } else if (error.status === 500) {
                                Utils.showMessage('服务器内部错误', 'error');
                            } else {
                                Utils.showMessage('网络错误，请重试', 'error');
                            }
                            return false;
                        }
                    },

                    // 定位显示 - 自动调整相机视角以显示所有航点 - 与 Mission copy.cshtml 保持一致
                    flyToPoints(viewer, pointData) {
                        if (!pointData || pointData.length === 0) return;

                        const lons = pointData.map(p => p.longitude);
                        const lats = pointData.map(p => p.latitude);
                        const lonRange = Math.max(...lons) - Math.min(...lons);
                        const latRange = Math.max(...lats) - Math.min(...lats);

                        // 根据航点分布范围调整视角参数
                        let padding, zoomAmount, pitch;
                        if (lonRange < 0.005 && latRange < 0.005) {
                            // 小范围 - 近距离显示
                            padding = 0.001;
                            zoomAmount = 100;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.1;
                        } else if (lonRange < 0.1 && latRange < 0.1) {
                            // 中等范围 - 中距离显示
                            padding = 0.01;
                            zoomAmount = 500;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.2;
                        } else {
                            // 大范围 - 远距离显示
                            padding = 0.05;
                            zoomAmount = 1000;
                            pitch = -Cesium.Math.PI_OVER_TWO + 0.3;
                        }

                        viewer.camera.flyTo({
                            destination: Cesium.Rectangle.fromDegrees(
                                Math.min(...lons) - padding,
                                Math.min(...lats) - padding,
                                Math.max(...lons) + padding,
                                Math.max(...lats) + padding
                            ),
                            zoom: { amount: zoomAmount },
                            orientation: { heading: 0, pitch: pitch, roll: 0 },
                            duration: 2
                        });
                    },

                    // 加载任务详情 - 从服务器获取任务数据 - 与 Mission copy.cshtml 保持一致
                    async loadMissionForEdit(missionId) {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/detail`,
                                method: 'GET',
                                data: { id: missionId }
                            });

                            if (response.success && response.data) {
                                this.loadMissionData(response.data);
                                this.startEditing(missionId);
                                return response.data;
                            } else {
                                Utils.showMessage(response.message || '加载任务失败', 'error');
                                return null;
                            }
                        } catch (error) {
                            console.error('加载任务失败:', error);
                            Utils.showMessage('加载任务失败，请重试', 'error');
                            return null;
                        }
                    },

                    // 加载任务数据到地图
                    loadMissionData(missionData) {
                        // 清除现有数据
                        this.clearAllPoints();

                        // 设置任务信息 - 与 Mission copy.cshtml 保持一致的数据结构
                        DOMManager.get('#missionName').val(missionData.missionName || '');
                        DOMManager.get('#speed').val(missionData.speed || CONFIG.WAYPOINT.DEFAULT_SPEED);
                        DOMManager.get('#height').val(missionData.height || CONFIG.WAYPOINT.DEFAULT_HEIGHT);

                        // 加载航点 - 使用 pointData 数组与 Mission copy.cshtml 保持一致
                        if (missionData.pointData && missionData.pointData.length > 0) {
                            missionData.pointData.forEach((waypoint, index) => {
                                const position = Cesium.Cartesian3.fromDegrees(
                                    waypoint.longitude,
                                    waypoint.latitude,
                                    waypoint.altitude || CONFIG.WAYPOINT.DEFAULT_HEIGHT
                                );

                                const waypointId = CONFIG.WAYPOINT.PREFIX + (++this.pointCounter);
                                const entity = this.createWaypointEntity(waypointId, position, index + 1);

                                // 创建点数据对象 - 与 Mission copy.cshtml 保持一致的数据结构
                                const pointData = { id: waypointId, position };

                                // 只保存与全局设置不同的特定值 - 与 Mission copy.cshtml 保持一致
                                const specificSettings = [
                                    { property: 'speed', pointValue: waypoint.speed, globalValue: missionData.speed },
                                    { property: 'height', pointValue: waypoint.altitude, globalValue: missionData.height },
                                    { property: 'heading', pointValue: waypoint.heading, globalValue: 0xFFFF }
                                ];

                                specificSettings.forEach(setting => {
                                    if (setting.pointValue !== undefined && setting.pointValue !== setting.globalValue) {
                                        pointData[setting.property] = setting.pointValue;
                                    }
                                });

                                this.pointData.push(pointData);
                            });

                            this.updateLine();

                            // 自动定位到航点区域 - 与 Mission copy.cshtml 保持一致
                            if (missionData.pointData.length > 0) {
                                this.flyToPoints(this.viewer, missionData.pointData);
                            }
                        }
                    },

                    // 删除任务
                    async deleteMission(missionId, missionName = '') {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/delete`,
                                method: 'DELETE',
                                contentType: 'application/json',
                                data: JSON.stringify(missionId) // 与 Mission copy.cshtml 保持一致，直接传递 missionId
                            });

                            if (response.success) {
                                const message = missionName ? `任务 "${missionName}" 删除成功` : '任务删除成功';
                                Utils.showMessage(message);
                                EventManager.emit('missionDeleted', { missionId });

                                // 如果删除的是当前编辑的任务，清除编辑状态
                                if (this.editingMissionId === missionId) {
                                    this.clearAllPoints();
                                    this.stopEditing();
                                }
                                return true;
                            } else {
                                Utils.showMessage(response.message || '删除失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('删除任务失败:', error);
                            Utils.showMessage('删除任务失败，请重试', 'error');
                            return false;
                        }
                    },

                    // 删除选中的航点
                    removeSelectedPoint() {
                        if (!this.selectedEntity) {
                            Utils.showMessage('请先选择要删除的航点', 'error');
                            return;
                        }

                        // 添加确认对话框，与 Mission copy.cshtml 保持一致
                        vui.confirm("确定要删除这个点吗？", "删除确认", () => {
                            const entityId = this.selectedEntity.id;

                            // 从地图中移除实体
                            this.viewer.entities.remove(this.selectedEntity);

                            // 从数据数组中移除
                            const index = this.pointData.findIndex(p => p.id === entityId);
                            if (index !== -1) {
                                this.pointData.splice(index, 1);
                            }

                            // 清除选择状态
                            this.selectedEntity = null;

                            // 更新连线
                            this.updateLine();

                            // 更新航点标签编号
                            this.renumberWaypoints();

                            Utils.showMessage('航点已删除');
                        });
                    },

                    // 重新编号航点 - 与 Mission copy.cshtml 保持一致的实现
                    renumberWaypoints() {
                        const entities = this.viewer.entities.values;
                        const points = entities.filter(entity => entity.id && entity.id.startsWith(CONFIG.WAYPOINT.PREFIX));
                        points.sort((a, b) => Utils.getPointIndex(a.id) - Utils.getPointIndex(b.id));
                        points.forEach((entity, index) => {
                            entity.label.text = String(index + 1);
                        });
                    },

                    // 上传任务
                    async uploadMission(executeImmediately = false) {
                        try {
                            // 检查是否有选中的任务
                            const $selected = DOMManager.get("li.selected");
                            if (!$selected.length) {
                                Utils.showMessage('请先选择一个任务', 'error');
                                return false;
                            }

                            const missionId = $selected.data("id");

                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/upload`,
                                method: 'GET',
                                data: {
                                    id: missionId,
                                    type: executeImmediately ? 1 : 0
                                }
                            });

                            if (response.success) {
                                const message = executeImmediately ? '已将任务推送并执行' : '已将任务推送';
                                Utils.showMessage(message);
                                return true;
                            } else {
                                Utils.showMessage(response.message || '推送失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('上传任务失败:', error);
                            Utils.showMessage('上传任务失败，请重试', 'error');
                            return false;
                        }
                    }
                };

                // ========== 任务列表管理 ==========
                const MissionListManager = {
                    missions: [],

                    // 加载任务列表
                    async loadMissionList() {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/list`,
                                method: 'GET',
                                data: {
                                    page: 1,
                                    limit: 50,
                                    'biz_context.VehicleId': @Model.id
                                }
                            });

                            if (response.success && response.data?.list?.length > 0) {
                                this.missions = response.data.list;
                                UIManager.missionList.render(this.missions);
                                return this.missions;
                            } else {
                                Utils.showMessage(response.message || '加载任务列表失败', 'error');
                                return [];
                            }
                        } catch (error) {
                            console.error('加载任务列表失败:', error);
                            Utils.showMessage('加载任务列表失败，请重试', 'error');
                            return [];
                        }
                    },

                    // 刷新任务列表
                    async refreshMissionList() {
                        return await this.loadMissionList();
                    },

                    // 获取任务详情
                    getMissionById(missionId) {
                        return this.missions.find(m => m.missionId === missionId);
                    }
                };

                // ========== 事件绑定和初始化 ==========
                const AppController = {
                    // 初始化应用
                    async init() {
                        try {
                            // 预加载DOM元素
                            DOMManager.preloadElements();

                            // 初始化地图
                            await missionLayer.render(this);

                            // 绑定事件
                            this.bindEvents();

                            // 加载任务列表
                            await MissionListManager.loadMissionList();

                            // 设置状态监听
                            this.setupStateListeners();

                            //Utils.showMessage('系统初始化完成');
                        } catch (error) {
                            console.error('应用初始化失败:', error);
                            Utils.showMessage('系统初始化失败，请刷新页面重试', 'error');
                        }
                    },

                    // 绑定事件
                    bindEvents() {
                        // 新建任务按钮
                        DOMManager.get('#addNewMissionBtn').on('click', () => {
                            // 1. 如果当前在编辑一个任务，先退出
                            const $editingLi = DOMManager.get('#floatingMissionList').find('li[data-editing="true"]');
                            if ($editingLi.length > 0) {
                                AppController.exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                            }

                            // 2. 清理地图和数据
                            missionLayer.viewer.entities.removeAll();
                            missionLayer.pointData = [];
                            missionLayer.editingMissionId = null;
                            missionLayer.selectedEntity = null;
                            missionLayer.lineEntity = null;

                            // 3. 设置UI为新建模式
                            missionLayer.isEditing = true;
                            DOMManager.get('.mission-item').removeClass('selected').removeClass('hovered');
                            DOMManager.get('#editDrawer').addClass('active').removeClass('point-is-selected');
                            DOMManager.get('#speed').val(CONFIG.WAYPOINT.DEFAULT_SPEED);
                            DOMManager.get('#height').val(CONFIG.WAYPOINT.DEFAULT_HEIGHT);
                            DOMManager.get('#latDisplay').text('--');
                            DOMManager.get('#lngDisplay').text('--');
                            DOMManager.get('#missionName').val("");
                            DOMManager.get('#upload').removeClass('active');
                            Utils.showMessage("已进入新建任务模式，请在地图上添加航点");
                        });

                        // 保存任务按钮
                        DOMManager.get('#saveMission').on('click', async () => {
                            const success = await missionLayer.saveMission();
                            if (success) {
                                await MissionListManager.refreshMissionList();
                            }
                        });

                        // 删除航点按钮
                        DOMManager.get('#deletePoint').on('click', () => {
                            missionLayer.removeSelectedPoint();
                        });

                        // 上传任务按钮
                        DOMManager.get('#uploadMission').on('click', async () => {
                            await missionLayer.uploadMission(false);
                        });

                        // 上传并执行任务按钮
                        DOMManager.get('#uploadMissionStart').on('click', async () => {
                            await missionLayer.uploadMission(true);
                        });

                        // 任务项悬停效果 - 与 Mission copy.cshtml 保持一致
                        DOMManager.get('#floatingMissionList').on('mouseenter', 'li', function () {
                            const $li = $(this);
                            if (!$li.hasClass('selected')) {
                                $li.addClass('hovered');
                            }
                        }).on('mouseleave', 'li', function () {
                            const $li = $(this);
                            if (!$li.hasClass('selected')) {
                                $li.removeClass('hovered');
                            }
                        });

                        // 查看按钮点击事件 - 查看任务详情（只读模式）- 与 Mission copy.cshtml 完全一致
                        DOMManager.get('#floatingMissionList').off('click', '.search-btn').on('click', '.search-btn', function (e) {
                            e.stopPropagation();
                            const $clickedLi = $(this).closest("li");
                            const missionId = $clickedLi.data('id');
                            const $listContainer = DOMManager.get('#floatingMissionList');
                            const $editingLi = $listContainer.find('li[data-editing="true"]');

                            // 检查是否有任务正在编辑
                            if ($editingLi.length > 0) {
                                // 如果点击的是正在编辑的任务，直接退出编辑模式切换到查看
                                if ($editingLi.is($clickedLi)) {
                                    AppController.exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                                    AppController.switchToViewMode($clickedLi);
                                    return;
                                }

                                // 如果点击的是其他任务，询问是否放弃当前编辑
                                vui.confirm("当前正在编辑其他任务，切换将放弃更改，确定要继续吗？", "放弃编辑确认", function() {
                                    AppController.exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                                    AppController.switchToViewMode($clickedLi);
                                });
                                return;
                            }

                            // 检查是否正在新建任务
                            if (missionLayer.isEditing && !missionLayer.editingMissionId) {
                                vui.confirm("当前正在新建任务，切换将放弃新建，确定吗？", "放弃新建确认", function() {
                                    AppController.switchToViewMode($clickedLi);
                                });
                                return;
                            }

                            // 正常切换到查看模式
                            AppController.switchToViewMode($clickedLi);
                        });

                        // 任务列表事件委托 - 编辑按钮
                        DOMManager.get('#floatingMissionList').on('click', '.edit-btn', async (e) => {
                            e.stopPropagation();
                            const $btn = $(e.target);
                            const $li = $btn.closest('.mission-item');
                            const missionId = $li.data('id');
                            const missionName = $li.find('.mission-name').text().trim();
                            const $listContainer = DOMManager.get('#floatingMissionList');
                            const $editingLi = $listContainer.find('li[data-editing="true"]');

                            // 更新选中状态
                            DOMManager.get('.mission-item').removeClass('selected').removeClass('hovered');
                            $li.addClass('selected').addClass('hovered');

                            // 如果当前任务正在编辑，则退出编辑模式
                            if ($li.attr('data-editing') === 'true') {
                                vui.confirm(`确定要退出任务 "${missionName}" 的编辑模式吗？未保存的更改将丢失。`, "退出编辑确认", function() {
                                    AppController.exitEditMode($li, $btn);
                                    Utils.showMessage("已退出编辑任务模式");
                                });
                                return;
                            }

                            // 检查是否有其他任务正在编辑
                            if ($editingLi.length > 0 && !$editingLi.is($li)) {
                                const editingMissionName = $editingLi.find('.mission-name').text().trim();
                                vui.confirm(`当前正在编辑任务 "${editingMissionName}"，切换将放弃更改。确定要编辑任务 "${missionName}" 吗？`, "切换编辑确认", function() {
                                    AppController.exitEditMode($editingLi, $editingLi.find('.edit-btn'), false);
                                    AppController.enterEditMode($li, $btn, missionId);
                                    Utils.showMessage(`已切换到编辑任务 "${missionName}"`);
                                });
                                return;
                            }

                            // 检查是否正在新建任务
                            if (missionLayer.isEditing && !missionLayer.editingMissionId) {
                                vui.confirm(`当前正在新建任务，切换将放弃新建。确定要编辑任务 "${missionName}" 吗？`, "放弃新建确认", function() {
                                    // 清理新建状态
                                    missionLayer.isEditing = false;
                                    missionLayer.viewer.entities.removeAll();
                                    missionLayer.pointData = [];
                                    missionLayer.selectedEntity = null;
                                    missionLayer.lineEntity = null;

                                    AppController.enterEditMode($li, $btn, missionId);
                                    Utils.showMessage(`已切换到编辑任务 "${missionName}"`);
                                });
                                return;
                            }

                            // 正常进入编辑模式
                            AppController.enterEditMode($li, $btn, missionId);
                            Utils.showMessage(`已进入编辑任务 "${missionName}" 模式`);
                        });

                        // 任务项点击事件 - 处理任务选择和编辑状态切换 - 与 Mission copy.cshtml 完全一致
                        DOMManager.get('#floatingMissionList').off('click', '.mission-item').on('click', '.mission-item', function (e) {
                            if (!$(e.target).closest('.drawer-buttons').length) {
                                const $clickedLi = $(this);
                                const $listContainer = DOMManager.get('#floatingMissionList');
                                const $editingLi = $listContainer.find('li[data-editing="true"]');

                                // 如果正在编辑一个已存在的任务
                                if ($editingLi.length > 0) {
                                    // 如果点击的是正在编辑的任务，则退出编辑（与copy逻辑一致：不显示上传面板）
                                    if ($editingLi.is($clickedLi)) {
                                        AppController.exitEditMode($editingLi, $editingLi.find('.edit-btn'));
                                        return; // 只退出编辑，不显示上传面板
                                    }

                                    // 如果点击的是其他任务，需要确认（与copy逻辑一致：不显示上传面板）
                                    vui.confirm("当前正在编辑任务，切换任务将放弃更改，确定要继续吗？", "退出编辑确认", function() {
                                        AppController.exitEditMode($editingLi, $editingLi.find('.edit-btn'), false); // 退出但不重载，因为马上要切换
                                        AppController.switchToMission($clickedLi); // 不显示上传面板
                                    });
                                    return;
                                }

                                // 如果正在新建一个任务（与copy逻辑一致：不显示上传面板）
                                if (missionLayer.isEditing) {
                                    vui.confirm("当前正在新建任务，切换将放弃新建，确定吗？", "确认切换", function() {
                                        missionLayer.isEditing = false;
                                        DOMManager.get('#editDrawer').removeClass('active');
                                        AppController.switchToMission($clickedLi); // 不显示上传面板
                                    });
                                    return;
                                }

                                // 正常切换任务（无编辑状态）
                                AppController.switchToMission($clickedLi);
                                DOMManager.get('#upload').addClass('active');
                            }
                        });

                        // 删除任务按钮
                        DOMManager.get('#floatingMissionList').on('click', '.delete-btn', async (e) => {
                            e.stopPropagation();

                            const $btn = $(e.target);
                            const $li = $btn.closest('.mission-item');
                            const missionId = $li.data('id');
                            const missionName = $li.find('.mission-name').text().trim();
                            const isCurrentlyEditing = $li.attr('data-editing') === 'true';
                            const $listContainer = DOMManager.get('#floatingMissionList');
                            const $editingLi = $listContainer.find('li[data-editing="true"]');

                            // 构建确认消息
                            let confirmMessage = `确定要删除任务 "${missionName}" 吗？`;
                            if (isCurrentlyEditing) {
                                confirmMessage = `任务 "${missionName}" 正在编辑中，删除将丢失所有未保存的更改。确定要删除吗？`;
                            } else if ($editingLi.length > 0 && !$editingLi.is($li)) {
                                confirmMessage = `当前有其他任务正在编辑，确定要删除任务 "${missionName}" 吗？`;
                            }

                            vui.confirm(confirmMessage, '删除确认', async () => {
                                // 执行删除操作
                                const success = await AppController.performDeleteMission(missionId, missionName, $li, isCurrentlyEditing);
                                if (success) {
                                    await MissionListManager.refreshMissionList();
                                }
                            });
                        });

                        // 表单变化事件 - 与 Mission copy.cshtml 保持一致的复选框逻辑
                        DOMManager.get('#isOther').on('change', function() {
                            const isChecked = $(this).prop('checked');
                            const $input = DOMManager.get('#speedOther');
                            const globalValue = parseFloat(DOMManager.get('#speed').val()) || CONFIG.WAYPOINT.DEFAULT_SPEED;

                            if (isChecked) {
                                // 使用全局设置
                                $input.prop('disabled', true);
                                $input.val('使用全局速度');
                                $input.addClass('disabled-input-hint');
                            } else {
                                // 使用特定设置
                                $input.prop('disabled', false);
                                $input.val(globalValue);
                                $input.removeClass('disabled-input-hint');
                            }

                            // 更新选中航点的数据
                            AppController.updateSelectedPointData();
                        });

                        DOMManager.get('#isOther2').on('change', function() {
                            const isChecked = $(this).prop('checked');
                            const $input = DOMManager.get('#heightOther');
                            const globalValue = parseFloat(DOMManager.get('#height').val()) || CONFIG.WAYPOINT.DEFAULT_HEIGHT;

                            if (isChecked) {
                                // 使用全局设置
                                $input.prop('disabled', true);
                                $input.val('使用全局高度');
                                $input.addClass('disabled-input-hint');
                            } else {
                                // 使用特定设置
                                $input.prop('disabled', false);
                                $input.val(globalValue);
                                $input.removeClass('disabled-input-hint');
                            }

                            // 更新选中航点的数据
                            AppController.updateSelectedPointData();
                        });

                        DOMManager.get('#isOther3').on('change', function() {
                            const isChecked = $(this).prop('checked');
                            const $input = DOMManager.get('#headingOther');
                            const globalValue = 0;

                            if (isChecked) {
                                // 使用默认设置
                                $input.prop('disabled', true);
                                $input.val('使用默认航向');
                                $input.addClass('disabled-input-hint');
                            } else {
                                // 使用特定设置
                                $input.prop('disabled', false);
                                $input.val(globalValue);
                                $input.removeClass('disabled-input-hint');
                            }

                            // 更新选中航点的数据
                            AppController.updateSelectedPointData();
                        });

                        // 航点参数更新事件（防抖处理） - 与 Mission copy.cshtml 保持一致
                        const updateWaypointParams = Utils.debounce(() => {
                            AppController.updateSelectedPointData();
                        }, CONFIG.UI.DEBOUNCE_DELAY);

                        DOMManager.get('#speedOther, #heightOther, #headingOther').on('input change', updateWaypointParams);

                        // 关闭窗口按钮
                        DOMManager.get('#close').on('click', () => {
                            vui.confirm("确定要退出规划吗？", "退出确认", () => {
                                const index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                            });
                        });
                    },

                    // 设置状态监听
                    setupStateListeners() {
                        EventManager.on('stateChange', (data) => {
                            console.log('State changed:', data);
                        });

                        EventManager.on('mapLoaded', (data) => {
                            console.log('Map loaded successfully');
                        });

                        EventManager.on('missionSaved', async (data) => {
                            await MissionListManager.refreshMissionList();
                        });

                        EventManager.on('missionDeleted', async (data) => {
                            await MissionListManager.refreshMissionList();
                        });
                    },

                    // 进入编辑模式 - 设置任务为可编辑状态
                    enterEditMode($li, $btn, missionId) {
                        const $listContainer = DOMManager.get('#floatingMissionList');
                        // 清除其他编辑状态 - 确保同时只有一个任务处于编辑状态
                        $listContainer.find('.mission-item').each(function() {
                            const $item = $(this);
                            if (!$item.is($li) && $item.attr('data-editing') === 'true') {
                                AppController.exitEditMode($item, $item.find('.edit-btn'), false);
                            }
                        });

                        // 设置当前任务编辑状态
                        $li.attr('data-editing', 'true');
                        $btn.text('退出编辑');

                        missionLayer.editingMissionId = missionId;
                        missionLayer.loadMissionForEdit(missionId);
                        missionLayer.isEditing = true;
                        DOMManager.get('#editDrawer').addClass('active').removeClass('point-is-selected');
                        DOMManager.get('#upload').removeClass('active');
                    },

                    // 退出编辑模式 - 恢复任务为只读状态
                    exitEditMode($li, $btn, isExit = true) {
                        if (!$li || !$btn) return;
                        $li.removeAttr('data-editing');
                        $btn.text('编辑');
                        missionLayer.isEditing = false;
                        DOMManager.get('#editDrawer').removeClass('active');
                        DOMManager.get('#upload').addClass('active');
                        const missionId = $li.data('id');
                        if (missionId && isExit) {
                            missionLayer.loadMissionForEdit(missionId);
                        }
                        missionLayer.hidePointDrawer(); // 该函数会清空高度等信息
                    },

                    // 切换任务函数 - 加载并显示指定任务
                    switchToMission($li) {
                        // 设置当前选中状态
                        DOMManager.get('.mission-item').removeClass('selected').removeClass('hovered');
                        $li.addClass('selected').addClass('hovered');

                        // 加载任务数据
                        const missionId = $li.data('id');
                        DOMManager.get('#missionName').val($li.find('.mission-name').text().trim());
                        missionLayer.loadMissionForEdit(missionId);
                    },

                    // 切换到查看模式的函数
                    switchToViewMode($li) {
                        // 确保退出编辑状态
                        missionLayer.isEditing = false;
                        missionLayer.editingMissionId = null;

                        // 隐藏编辑面板，显示推送面板
                        DOMManager.get('#editDrawer').removeClass('active');
                        DOMManager.get('#upload').addClass('active');

                        // 切换任务并设置为只读模式
                        this.switchToMission($li);
                    },

                    // 更新选中航点的数据 - 与 Mission copy.cshtml 保持一致
                    updateSelectedPointData() {
                        if (!missionLayer.selectedEntity) return;

                        const index = missionLayer.pointData.findIndex(p => p.id === missionLayer.selectedEntity.id);
                        if (index === -1) return;

                        // 处理速度设置
                        if (DOMManager.get('#isOther').prop('checked')) {
                            // 使用全局速度，删除特定速度
                            delete missionLayer.pointData[index].speed;
                        } else {
                            // 使用特定速度
                            const speed = parseFloat(DOMManager.get('#speedOther').val());
                            if (!isNaN(speed)) {
                                missionLayer.pointData[index].speed = speed;
                            }
                        }

                        // 处理高度设置
                        if (DOMManager.get('#isOther2').prop('checked')) {
                            // 使用全局高度，删除特定高度
                            delete missionLayer.pointData[index].height;
                        } else {
                            // 使用特定高度
                            const height = parseFloat(DOMManager.get('#heightOther').val());
                            if (!isNaN(height)) {
                                missionLayer.pointData[index].height = height;
                            }
                        }

                        // 处理航向设置
                        if (DOMManager.get('#isOther3').prop('checked')) {
                            // 使用默认航向，删除特定航向
                            delete missionLayer.pointData[index].heading;
                        } else {
                            // 使用特定航向
                            const heading = parseFloat(DOMManager.get('#headingOther').val());
                            if (!isNaN(heading)) {
                                missionLayer.pointData[index].heading = heading;
                            }
                        }
                    },

                    // 执行删除任务的函数
                    async performDeleteMission(missionId, missionName, $li, isCurrentlyEditing) {
                        try {
                            const response = await AuthManager.createAuthenticatedAjax({
                                url: `${CONFIG.API.BASE}/delete`,
                                method: 'DELETE',
                                contentType: 'application/json',
                                data: JSON.stringify(missionId) // 与 Mission copy.cshtml 保持一致，直接传递 missionId
                            });

                            if (response.success) {
                                Utils.showMessage(`任务 "${missionName}" 删除成功`);

                                // 如果删除的是正在编辑的任务，清理编辑状态
                                if (isCurrentlyEditing || missionLayer.editingMissionId === missionId) {
                                    missionLayer.isEditing = false;
                                    missionLayer.editingMissionId = null;
                                    missionLayer.viewer.entities.removeAll();
                                    missionLayer.pointData = [];
                                    missionLayer.selectedEntity = null;
                                    missionLayer.lineEntity = null;
                                    DOMManager.get('#editDrawer').removeClass('active');
                                    DOMManager.get('#upload').addClass('active');
                                }

                                return true;
                            } else {
                                Utils.showMessage(response.message || '删除失败', 'error');
                                return false;
                            }
                        } catch (error) {
                            console.error('删除任务失败:', error);
                            Utils.showMessage('删除任务失败，请重试', 'error');
                            return false;
                        }
                    }
                };

                // ========== 应用启动 ==========
                $(document).ready(async function() {
                    try {
                        await AppController.init();
                    } catch (error) {
                        console.error('应用启动失败:', error);
                        Utils.showMessage('应用启动失败，请刷新页面重试', 'error');
                    }
                });

                // 车辆面板相关变量（预留） - 与 Mission copy.cshtml 保持一致
                var vehicle_panel = vui.Vehicle;
                var home_position = { lat: NaN, lng: NaN, alt: NaN };
                var home_position_raw = { lat: 0, lng: 0, alt: 0 };
                var home_valid = false;

            });
        });
    </script>
}