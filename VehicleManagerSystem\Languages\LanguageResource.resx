﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>FlightManagerSystem</value>
  </data>
  <data name="ERROR_UNKNOW" xml:space="preserve">
    <value>ERROR_UNKNOW</value>
  </data>
  <data name="ERROR_LOGIN_DISABLED" xml:space="preserve">
    <value>ERROR_LOGIN_DISABLED</value>
  </data>
  <data name="ERROR_USERNAME_OR_PASSWORD" xml:space="preserve">
    <value>ERROR_USERNAME_OR_PASSWORD</value>
  </data>
  <data name="FORMAT_PASSWORD" xml:space="preserve">
    <value>FORMAT_PASSWORD</value>
  </data>
  <data name="BUTTON_ACTION_LOGIN" xml:space="preserve">
    <value>BUTTON_ACTION_LOGIN</value>
  </data>
  <data name="INPUT_TEXT_PASSWORD" xml:space="preserve">
    <value>INPUT_TEXT_PASSWORD</value>
  </data>
  <data name="INPUT_TEXT_USERNAME" xml:space="preserve">
    <value>INPUT_TEXT_USERNAME</value>
  </data>
  <data name="BOX_TITLE_LOGIN" xml:space="preserve">
    <value>BOX_TITLE_LOGIN</value>
  </data>
  <data name="FORMAT_USERNAME" xml:space="preserve">
    <value>FORMAT_USERNAME</value>
  </data>
  <data name="JS_ALERT_TITLE_WARNING" xml:space="preserve">
    <value>JS_ALERT_TITLE_WARNING</value>
  </data>
  <data name="JS_ALERT_TITLE_ERROR" xml:space="preserve">
    <value>JS_ALERT_TITLE_ERROR</value>
  </data>
  <data name="UI_LOADING" xml:space="preserve">
    <value>TITLE_PAGE_BRIDGE</value>
  </data>
  <data name="ERROR_PERMISSION_ERROR" xml:space="preserve">
    <value>ERROR_PERMISSION_ERROR</value>
  </data>
  <data name="ERROR_PAGE_NOT_FOUND" xml:space="preserve">
    <value>ERROR_PAGE_NOT_FOUND</value>
  </data>
  <data name="ERROR_SERVER_ERROR" xml:space="preserve">
    <value>ERROR_SERVER_ERROR</value>
  </data>
  <data name="PAGE_TITLE_LOGIN" xml:space="preserve">
    <value>PAGE_TITLE_LOGIN</value>
  </data>
  <data name="PAGE_TITLE_ADMIN_INDEX" xml:space="preserve">
    <value>PAGE_TITLE_ADMIN_INDEX</value>
  </data>
  <data name="INDEX_MENU_VEHICLE_MANAGER" xml:space="preserve">
    <value>INDEX_MENU_VEHICLE_MANAGER</value>
  </data>
  <data name="INDEX_MENU_HANGAR_MANAGER" xml:space="preserve">
    <value>INDEX_MENU_HANGAR_MANAGER</value>
  </data>
  <data name="INDEX_MENU_BASE_STATION_MANAGER" xml:space="preserve">
    <value>INDEX_MENU_BASE_STATION_MANAGER</value>
  </data>
  <data name="INDEX_MENU_USER_MANAGER" xml:space="preserve">
    <value>INDEX_MENU_USER_MANAGER</value>
  </data>
  <data name="INDEX_MENU_MISSION_MANAGER" xml:space="preserve">
    <value>INDEX_MENU_MISSION_MANAGER</value>
  </data>
  <data name="INDEX_MENU_FLIGHT_LOGGER" xml:space="preserve">
    <value>INDEX_MENU_FLIGHT_LOGGER</value>
  </data>
  <data name="LANGUAGE_NAME" xml:space="preserve">
    <value>LANGUAGE_NAME</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_SERIAL" xml:space="preserve">
    <value>INPUT_TEXT_NODE_SERIAL</value>
  </data>
  <data name="BUTTON_TEXT_SEARCH" xml:space="preserve">
    <value>BUTTON_TEXT_SEARCH</value>
  </data>
  <data name="INPUT_TEXT_SEARCH" xml:space="preserve">
    <value>INPUT_TEXT_SEARCH</value>
  </data>
  <data name="BUTTON_TEXT_ADD" xml:space="preserve">
    <value>BUTTON_TEXT_ADD</value>
  </data>
  <data name="BUTTON_CONTROL" xml:space="preserve">
    <value>BUTTON_CONTROL</value>
  </data>
  <data name="VEHICLE_STATUS_OFFLINE" xml:space="preserve">
    <value>VEHICLE_STATUS_OFFLINE</value>
  </data>
  <data name="VEHICLE_STATUS_ONLINE" xml:space="preserve">
    <value>VEHICLE_STATUS_ONLINE</value>
  </data>
  <data name="VEHICLE_STATUS_UNKNOW" xml:space="preserve">
    <value>VEHICLE_STATUS_UNKNOW</value>
  </data>
  <data name="GENERAL_ALL" xml:space="preserve">
    <value>VEHICLE_TYPE_ALL</value>
  </data>
  <data name="VEHICLE_TYPE_HELI" xml:space="preserve">
    <value>VEHICLE_TYPE_HELI</value>
  </data>
  <data name="VEHICLE_TYPE_QUAD" xml:space="preserve">
    <value>VEHICLE_TYPE_QUAD</value>
  </data>
  <data name="VEHICLE_TYPE_HEXA" xml:space="preserve">
    <value>VEHICLE_TYPE_HEXA</value>
  </data>
  <data name="VEHICLE_TYPE_QUAD_X2" xml:space="preserve">
    <value>VEHICLE_TYPE_QUAD_X2</value>
  </data>
  <data name="VEHICLE_TYPE_HEXA_X2" xml:space="preserve">
    <value>VEHICLE_TYPE_HEXA_X2</value>
  </data>
  <data name="VEHICLE_TYPE_OTHER" xml:space="preserve">
    <value>VEHICLE_TYPE_OTHER</value>
  </data>
  <data name="VEHICLE_TYPE_UNKNOW" xml:space="preserve">
    <value>VEHICLE_TYPE_UNKNOW</value>
  </data>
  <data name="LABEL_VEHICLE_SERIAL" xml:space="preserve">
    <value>LABEL_VEHICLE_SERIAL</value>
  </data>
  <data name="LABEL_VEHICLE_NAME" xml:space="preserve">
    <value>LABEL_VEHICLE_NAME</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_DISPLAY_NAME" xml:space="preserve">
    <value>INPUT_TEXT_VEHICLE_DISPLAY_NAME</value>
  </data>
  <data name="BUTTON_CONFIRM" xml:space="preserve">
    <value>BUTTON_CONFIRM</value>
  </data>
  <data name="LABEL_VEHICLE_TYPE" xml:space="preserve">
    <value>LABEL_VEHICLE_TYPE</value>
  </data>
  <data name="LABEL_VEHICLE_MODEL" xml:space="preserve">
    <value>LABEL_VEHICLE_MODEL</value>
  </data>
  <data name="LABEL_VEHICLE_MANUFACTURER" xml:space="preserve">
    <value>LABEL_VEHICLE_MANUFACTURER</value>
  </data>
  <data name="LABEL_VEHICLE_STATUS" xml:space="preserve">
    <value>LABEL_VEHICLE_STATUS</value>
  </data>
  <data name="LABEL_VEHICLE_BATTERY" xml:space="preserve">
    <value>LABEL_VEHICLE_BATTERY</value>
  </data>
  <data name="LABEL_VEHICLE_RSSI" xml:space="preserve">
    <value>LABEL_VEHICLE_RSSI</value>
  </data>
  <data name="LABEL_VEHICLE_LONGITUDE" xml:space="preserve">
    <value>LABEL_VEHICLE_LONGITUDE</value>
  </data>
  <data name="LABEL_VEHICLE_LATITUDE" xml:space="preserve">
    <value>LABEL_VEHICLE_LATITUDE</value>
  </data>
  <data name="LABEL_VEHICLE_ALTITUDE" xml:space="preserve">
    <value>LABEL_VEHICLE_ALTITUDE</value>
  </data>
  <data name="LABEL_ACTION" xml:space="preserve">
    <value>LABEL_VEHICLE_DELETE</value>
  </data>
  <data name="ERROR_RESULT_ADD_VEHICLE_FAILED_UNIQUE" xml:space="preserve">
    <value>ERROR_RESULT_ADD_VEHICLE_FAILED_UNIQUE</value>
  </data>
  <data name="ERROR_PARAMS" xml:space="preserve">
    <value>ERROR_PARAMS</value>
  </data>
  <data name="ERROR_RESULT_ADD_VEHICLE_FAILED_SERIAL" xml:space="preserve">
    <value>ERROR_RESULT_ADD_VEHICLE_FAILED_SERIAL</value>
  </data>
  <data name="UNIT_METER" xml:space="preserve">
    <value>UNIT_METER</value>
  </data>
  <data name="BUTTON_ACTION_DEL" xml:space="preserve">
    <value>BUTTON_ACTION_DEL</value>
  </data>
  <data name="INPUT_TEXT_VEHICLE_SECRET" xml:space="preserve">
    <value>INPUT_TEXT_VEHICLE_SECRET</value>
  </data>
  <data name="LABEL_VEHICLE_SECRET" xml:space="preserve">
    <value>LABEL_VEHICLE_SECRET</value>
  </data>
  <data name="BUTTON_ACTION_SECRET" xml:space="preserve">
    <value>BUTTON_ACTION_SECRET</value>
  </data>
  <data name="LABEL_USER_NAME" xml:space="preserve">
    <value>LABEL_USER_NAME</value>
  </data>
  <data name="LABEL_USER_NICK" xml:space="preserve">
    <value>LABEL_USER_NICK</value>
  </data>
  <data name="LABEL_USER_ROLE" xml:space="preserve">
    <value>LABEL_USER_ROLE</value>
  </data>
  <data name="LABEL_USER_STATUS" xml:space="preserve">
    <value>LABEL_USER_STATUS</value>
  </data>
  <data name="LABEL_PASSWORD" xml:space="preserve">
    <value>LABEL_PASSWORD</value>
  </data>
  <data name="INPUT_CREATE_TEXT_USERNAME" xml:space="preserve">
    <value>INPUT_CREATE_TEXT_USERNAME</value>
  </data>
  <data name="INPUT_CREATE_TEXT_PASSWORD" xml:space="preserve">
    <value>INPUT_CREATE_TEXT_PASSWORD</value>
  </data>
  <data name="INPUT_TEXT_DISPLAY_NAME" xml:space="preserve">
    <value>INPUT_TEXT_DISPLAY_NAME</value>
  </data>
  <data name="USER_ROLE_MONITOR" xml:space="preserve">
    <value>USER_ROLE_MONITOR</value>
  </data>
  <data name="USER_ROLE_CONTROL" xml:space="preserve">
    <value>USER_ROLE_CONTROL</value>
  </data>
  <data name="USER_ROLE_THIRD_PARTY_ADMIN" xml:space="preserve">
    <value>USER_ROLE_THIRD_PARTY_ADMIN</value>
  </data>
  <data name="USER_ROLE_SUPER_ADMIN" xml:space="preserve">
    <value>USER_ROLE_SUPER_ADMIN</value>
  </data>
  <data name="USER_STATUS_NORMAL" xml:space="preserve">
    <value>USER_STATUS_NORMAL</value>
  </data>
  <data name="USER_STATUS_DISABLED" xml:space="preserve">
    <value>USER_STATUS_DISABLED</value>
  </data>
  <data name="BUTTON_ACTION_PASSWORD" xml:space="preserve">
    <value>BUTTON_ACTION_PASSWORD</value>
  </data>
  <data name="BUTTON_ACTION_ENABLE" xml:space="preserve">
    <value>BUTTON_ACTION_ENABLE</value>
  </data>
  <data name="BUTTON_ACTION_DISABLE" xml:space="preserve">
    <value>BUTTON_ACTION_DISABLE</value>
  </data>
  <data name="PAGE_USER_BUTTON_ACTION_VEHICLE_LIST" xml:space="preserve">
    <value>BUTTON_ACTION_VEHICLE_LIST</value>
  </data>
  <data name="ERROR_USER_EXISTS" xml:space="preserve">
    <value>ERROR_USER_EXISTS</value>
  </data>
  <data name="ERROR_SIGNTURE" xml:space="preserve">
    <value>ERROR_SIGNTURE</value>
  </data>
  <data name="ERROR_MISS_LOGIN" xml:space="preserve">
    <value>ERROR_MISS_LOGIN</value>
  </data>
  <data name="ERROR_VEHICLE_NOT_EXISTS" xml:space="preserve">
    <value>ERROR_VEHICLE_NOT_EXISTS</value>
  </data>
  <data name="ERROR_MISS_VEHICLE_PERMISSION" xml:space="preserve">
    <value>ERROR_MISS_VEHICLE_PERMISSION</value>
  </data>
  <data name="BUTTON_ACTION_CONTROL" xml:space="preserve">
    <value>BUTTON_ACTION_CONTROL</value>
  </data>
</root>