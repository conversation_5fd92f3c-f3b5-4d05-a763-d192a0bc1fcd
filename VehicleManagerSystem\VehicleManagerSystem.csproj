<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MQTTnet" Version="5.0.1.1416" />
    <PackageReference Include="MQTTnet.AspNetCore" Version="5.0.1.1416" />
    <PackageReference Include="MQTTnet.Extensions.Rpc" Version="5.0.1.1416" />
    <PackageReference Include="MQTTnet.Extensions.TopicTemplate" Version="5.0.1.1416" />
    <PackageReference Include="MQTTnet.Extensions.WebSocket4Net" Version="4.3.7.1207" />
    <PackageReference Include="MQTTnet.Server" Version="5.0.1.1416" />
    <PackageReference Include="runtime.native.System.Net.Security" Version="4.3.1" />
    <PackageReference Include="SqlSugarCore" Version="*********" />
    <PackageReference Include="System.Net.Security" Version="4.3.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\lib\cesium\Core\" />
    <Folder Include="wwwroot\lib\cesium\DataSources\" />
    <Folder Include="wwwroot\lib\cesium\Renderer\" />
    <Folder Include="wwwroot\lib\cesium\Scene\GltfPipeline\" />
    <Folder Include="wwwroot\lib\cesium\Scene\Model\Extensions\Gpm\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Appearances\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Builtin\Constants\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Builtin\Functions\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Builtin\Structs\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Materials\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Model\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\PostProcessStages\" />
    <Folder Include="wwwroot\lib\cesium\Shaders\Voxels\" />
    <Folder Include="wwwroot\lib\cesium\Widgets\HomeButton\" />
    <Folder Include="wwwroot\lib\cesium\Widgets\ThirdParty\" />
    <Folder Include="wwwroot\lib\cesium\Widget\" />
    <Folder Include="wwwroot\lib\layuiadmin\json\" />
  </ItemGroup>

</Project>
