﻿// noinspection JSCheckFunctionSignatures


layui.config({
    base: '/layuiadmin/'
}).extend({
    dynamic: '/modules/dynamic',
    expansion: '/modules/expansion',
}).define(['jquery', 'form', 'table', 'expansion', 'handle'], function (exports) {
    
    const layer = layui.layer,
        form = layui.form,
        table = layui.table,
        expansion = layui.expansion,
        handle = layui.handle,
        $ = layui.jquery,
        module = {
            generate_uuid: function () {
                var d = new Date().getTime(); //Timestamp
                var d2 = (performance && performance.now && (performance.now() * 1000)) || 0; //Time in microseconds since page-load or 0 if unsupported
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                    var r = Math.random() * 16; //random number between 0 and 16
                    if (d > 0) { //Use timestamp until depleted
                        r = (d + r) % 16 | 0;
                        d = Math.floor(d / 16);
                    } else { //Use microseconds since page-load if supported
                        r = (d2 + r) % 16 | 0;
                        d2 = Math.floor(d2 / 16);
                    }
                    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
                });
            },
            update_google: function (step, code, mm) {
                $.ajax({
                    url: "/api/account/update-google/" + step + "?code=" + code,
                    type: "get",
                    dataType: "json",
                    loading: true,
                    success: function (res) {
                        if (res.success) {
                            if (res.body.step === "verify_google") {
                                layer.prompt({
                                    formType: 0,
                                    title: '请输入谷歌验证码',
                                    value: '',
                                    offset: ["165px"],
                                    success: (layero, index, that) => {
                                        layui.$(layero).find('input').attr('type', 'number');
                                        layui.form.render(layui.$(layero).find('input'));
                                    }
                                }, function (value, index, elem) {
                                    if (isNaN(value) || value.trim() === "") {
                                        layer.tips('请输入正确的验证码', elem);
                                        return;
                                    }
                                    module.update_google("verify", value);
                                    layer.close(index);
                                });
                            }  //扫码和验证
                            else if (res.body.step === "view_qr_google") {
                                layer.open({
                                    type: 1,
                                    area: '350px',
                                    offset: ["165px"],
                                    resize: false,
                                    shadeClose: false,
                                    title: '请用谷歌验证器扫描',
                                    offset: ["24px"],
                                    content: $("#google-bind-tpl").html(),
                                    success: function (layero, index, elm) {
                                        $(layero).find('[ui-name="qr-image"]').attr("src", res.body.qrcode)
                                        form.on('submit(req-google)', function (data) {
                                            module.update_google("bind_by_qr_google", data.field.code, index);
                                            return false; // 阻止默认 form 跳转
                                        });
                                    }
                                });
                            }
                            else if (res.body.step === "complete") {
                                layer.msg("操作成功", { icon: 1, time: 2000 }, function () { });
                                layer.close(mm)
                            }
                        }
                    }
                });
            },
            update_password: function (password, old_password, code) {
                $.ajax({
                    url: "/api/account/update-password",
                    type: "post",
                    dataType: "json",
                    loading: true,
                    contentType: "application/json",
                    data: JSON.stringify({
                        password: password,
                        old_password: old_password,
                        code: code
                    }),
                    success: function (res) {
                        if (res.success) {
                            if (res.body && res.body.google) {
                                layer.prompt({
                                    formType: 0,
                                    title: '请输入谷歌验证码',
                                    value: '',
                                    offset: ["165px"],
                                    success: (layero, index, that) => {
                                        layui.$(layero).find('input').attr('type', 'number');
                                        layui.form.render(layui.$(layero).find('input'));
                                    }
                                }, function (value, index, elem) {
                                    if (isNaN(value) || value.trim() === "") {
                                        layer.tips('请输入正确的验证码', this);
                                        return;
                                    }
                                    module.update_password(password, old_password, value);
                                    layer.close(index);
                                });
                            } else {
                                localStorage.clear();
                                top.location.href = "/login";
                                layer.close(index)
                            }

                        }
                    }
                });
            },
            update_secret: function (secret, code) {
                $.ajax({
                    url: "/api/account/update-secret",
                    type: "post",
                    data: JSON.stringify({
                        secret: secret,
                        code: Number(code)
                    }),
                    dataType: "json",
                    success: function (res) {
                        if (res.success && res.body && res.body.google) {
                            layer.prompt({
                                formType: 0,
                                title: '请输入谷歌验证码',
                                value: '',
                                offset: ["165px"],
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的验证码', this);
                                    return;
                                }
                                module.update_secret(secret, value);
                                layer.close(index);
                            });
                        } else if (res.success) {
                            $('[name="secret"]').attr("old", $('[name="secret"]').val());
                            module.render_secret();
                            layer.msg("保存成功", {
                                icon: 1,
                                time: 2000,
                            }, function () {
                            });
                        }
                    }
                });
            },
            render_secret: function () {
                if ($('[name="secret"]').val() == $('[name="secret"]').attr("old")) {
                    $('[name="secret-save"]').hide();
                } else {
                    $('[name="secret-save"]').show();
                }
            },
            add_whiteipaddress: function (address, code) {
                $.ajax({
                    url: "/api/account/add-white-ipaddress",
                    type: "post",
                    data: JSON.stringify({
                        address: address,
                        code: Number(code)
                    }),
                    dataType: "json",
                    success: function (res) {
                        if (res.success && res.body && res.body.google) {
                            layer.prompt({
                                formType: 0,
                                title: '请输入谷歌验证码',
                                value: '',
                                offset: ["165px"],
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的验证码', this);
                                    return;
                                }
                                module.add_whiteipaddress(secret, value);
                                layer.close(index);
                            });
                        } else {
                            $('.ip-list').append('<li><span class="ip">' + address + '</span><span name="ip-del" data-address="' + address + '" class="layui-btn layui-btn-xs layui-btn-danger">删除</span></li>');
                            layer.msg("操作成功", {
                                icon: 1,
                                time: 2000,
                            }, function () {
                            });
                        }
                    }
                })
            },
            del_whiteipaddress: function (address, code) {
                $.ajax({
                    url: "/api/account/del-white-ipaddress",
                    type: "post",
                    data: JSON.stringify({
                        address: address,
                        code: Number(code)
                    }),
                    dataType: "json",
                    success: function (res) {
                        if (res.success && res.body && res.body.google) {
                            layer.prompt({
                                formType: 0,
                                title: '请输入谷歌验证码',
                                value: '',
                                offset: ["165px"],
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的验证码', this);
                                    return;
                                }
                                module.add_whiteipaddress(secret, value);
                                layer.close(index);
                            });
                        } else {
                            $('.ip-list').find("[data-address='" + address + "']").parent().remove();
                            layer.msg("操作成功", {
                                icon: 1,
                                time: 2000,
                            }, function () {
                            });
                        }
                    }
                })
            },
            add_cert: function (remark, password,code) {
                $.ajax({
                    url: "/api/account/add-cert",
                    type: "post",
                    data: JSON.stringify({
                        remark: remark,
                        password: password,
                        code: Number(code)
                    }),
                    dataType: "json",
                    success: function (res) {
                        if (res.success && res.body && res.body.google) {
                            layer.prompt({
                                formType: 0,
                                title: '请输入谷歌验证码',
                                value: '',
                                offset: ["165px"],
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的验证码', this);
                                    return;
                                }
                                module.add_cert(remark, password, value);
                                layer.close(index);
                            });
                        } else {
                            var cert =  res.body.Cert;
                            handle.save_file(handle.base64ToBlob(cert, "application/x-pkcs12"), (new Date().valueOf()) + "_login.pfx");
                            $('.cert-list').append('<li><span class="cert">' + res.body.Remark + ' [' + res.body.Hash + ']</span><span name="cert-del" data-hash="' + res.body.Hash + '" class="layui-btn layui-btn-xs layui-btn-danger">删除</span></li>');
                            layer.msg("操作成功", {
                                icon: 1,
                                time: 2000,
                            }, function () {
                            });
                        }
                    }
                })
            },
            del_cert: function (hash,code) {
                $.ajax({
                    url: "/api/account/del-cert",
                    type: "post",
                    data: JSON.stringify({
                        hash: hash,
                        code: Number(code)
                    }),
                    dataType: "json",
                    success: function (res) {
                        if (res.success && res.body && res.body.google) {
                            layer.prompt({
                                formType: 0,
                                title: '请输入谷歌验证码',
                                value: '',
                                offset: ["165px"],
                                success: (layero, index, that) => {
                                    layui.$(layero).find('input').attr('type', 'number');
                                    layui.form.render(layui.$(layero).find('input'));
                                }
                            }, function (value, index, elem) {
                                if (isNaN(value) || value.trim() === "") {
                                    layer.tips('请输入正确的验证码', this);
                                    return;
                                }
                                module.del_cert(hash, value);
                                layer.close(index);
                            });
                        } else {
                            $('.cert-list').find("[data-hash='" + hash + "']").parent().remove();
                            layer.msg("操作成功", {
                                icon: 1,
                                time: 2000,
                            }, function () {
                            });
                        }
                    }
                })
            },
        render: function () {
            var load_data = function () {
                $.ajax({
                    url: "/api/account/get-security",
                    type: "get",
                    success: function (res) {
                        if (res.success) {
                            if (res.body.Secret) {
                                $('[name="secret-setting"]').html("<span class='layui-badge layui-bg-green' style='margin-left:10px'>已设置</span>");
                            } else {
                                $('[name="secret-setting"]').html("<span class='layui-badge'  style='margin-left:10px'>未设置</span>");
                            }
                            for (var n = 0; n < res.body.Iplist.length; n++) {
                                $('.ip-list').append('<li><span class="ip">' + res.body.Iplist[n] + '</span><span name="ip-del" data-address="' + res.body.Iplist[n] +'" class="layui-btn layui-btn-xs layui-btn-danger">删除</span></li>');
                            }
                            //for (var n = 0; n < res.body.Certlist.length; n++) {
                            //    $('.cert-list').append('<li><span class="cert">' + res.body.Certlist[n].Remark + ' [' + res.body.Certlist[n].Hash + ']</span><span name="cert-del" data-hash="' + res.body.Certlist[n].Hash + '" class="layui-btn layui-btn-xs layui-btn-danger">删除</span></li>');
                            //}
                        }
                    }
                });
                 

            };
            load_data();
            $(document).on("click", '[name="secret-save"]', function () { 
                var secret = $('[name="secret"]').val();
                if ((secret + "").length < 30) {
                    layer.msg("请至少输入30位", {
                        icon: 2,
                        time: 2000,
                      
                    }, function () {
                    }); 
                    return;
                }
                module.update_secret(secret,"0"); 
            });
            $(document).on("click", '[name="cert-add"]', function () {
                layer.prompt({
                    formType: 0,
                    title: '请输入证书名称',
                    value: '',
                    offset: ["24px"]
                }, function (value, index, elem) {
                    if (value.trim()=="") {
                        layer.msg('请输入正确证书名称', {
                            icon: 2,
                            time: 2000,

                        });
                        return;
                    }
                     
                    layer.prompt({
                        formType: 1,
                        title: '请设置证书安装密码',
                        value: '',
                        offset: ["24px"]
                    }, function (password, index, elem) {
                        if (password.trim() == "") {
                            layer.msg('请输入正确的证书密码', {
                                icon: 2,
                                time: 2000,
                            });
                            return;
                        } 
                        module.add_cert(value, password, '0');
                        layer.close(index);
                    });
                    layer.close(index);
                });

            });
            $(document).on("click", '[name="cert-del"]', function () {
                //检测是否需要验证谷歌
                var hash = $(this).attr("data-hash");
                module.del_cert(hash,'0');
            });
            $(document).on("click", '[name="ip-add"]', function () {
                //检测是否需要验证谷歌
                layer.prompt({
                    formType: 0,
                    title: '请输入IP地址',
                    value: '',
                    offset: ["24px"]
                }, function (value, index, elem) {
                    var rs = value.split(".");
                    if (rs.length != 4) {
                        layer.msg('请输入正确的IP地址', {
                            icon: 2,
                            time: 2000,

                        });
                        return;
                    }
                    if (Number(rs[0]) > 255 || Number(rs[1]) > 255 || Number(rs[2]) > 255 || Number(rs[3]) > 255) {
                        layer.msg('请输入正确的IP地址', {
                            icon: 2,
                            time: 2000,

                        });
                        return;
                    }
                    if (Number(rs[0]) < 0 || Number(rs[1]) < 0 || Number(rs[2]) < 0 || Number(rs[3]) < 0) {
                        layer.msg('请输入正确的IP地址', {
                            icon: 2,
                            time: 2000,

                        });
                        return;
                    }
                    module.add_whiteipaddress(value, "0");
                    layer.close(index);
                });
            });
            $(document).on("click", '[name="ip-del"]', function () {
                //检测是否需要验证谷歌
                var ip = $(this).attr("data-address"); 
                module.del_whiteipaddress(ip, "0");

            });
            $('#change_google').on("click", function () {
                module.update_google("first", 0);
            });
            $('#change_password').on("click", function () {
                layer.open({
                    type: 1,
                    area: '350px',
                    resize: false,
                    shadeClose: false,
                    title: '修改密码',
                    offset:["24px"],
                    content: $("#update-password-tpl").html(),
                    success: function (layero, index, elm) {
                        form.verify({
                            confirm_password: function (t, i) {
                                if (t.trim() !== $(layero).find('[name="new_password"]').val()) {
                                    return "两次密码不一致"
                                }
                            },
                        });
                        form.on('submit(req-update-password)', function (data) {
                            module.update_password(data.field.confirm_password, data.field.old_password, 0);
                            return false;
                        });
                    }
                });
            });
            $('[name="secret"]').on("input", function () {
                module.render_secret();
            });
            $(document).on("click", '[name="secret-build"]', function () {
                $('[name="secret"]').val(module.generate_uuid());
                module.render_secret();
            });
        },
            
    }; 
    exports('security', module);
});
