﻿page:before {
    width: 100%;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(
    /*上左内*/ 50% 80px,calc(50% - 180px) 80px,calc(50% - 200px) 60px,80px 60px,60px 80px,
    /*左内*/ 60px 180px,40px 200px,40px calc(100% - 200px),60px calc(100% - 180px),60px calc(100% - 80px),80px calc(100% - 60px),
    /*下内*/ 300px calc(100% - 60px),310px calc(100% - 70px),calc(100% - 310px) calc(100% - 70px),calc(100% - 300px) calc(100% - 60px),calc(100% - 80px) calc(100% - 60px),calc(100% - 60px) calc(100% - 80px),
    /*右内*/ calc(100% - 60px) calc(100% - 180px),calc(100% - 40px) calc(100% - 200px),calc(100% - 40px) 200px,calc(100% - 60px) 180px,calc(100% - 60px) 80px,calc(100% - 80px) 60px,
    /*上右内*/ calc(50% + 200px) 60px,calc(50% + 180px) 80px,50% 80px,
    /*上右边*/ 50% 79px,calc(50% + 179.5px) 79px,calc(50% + 199.5px) 59px,calc(100% - 79.5px) 59px,
    /*右边*/ calc(100% - 59px) 79.5px,calc(100% - 59px) 179.5px,calc(100% - 39px) 199.5px,calc(100% - 39px) calc(100% - 199.5px),calc(100% - 59px) calc(100% - 179.5px),calc(100% - 59px) calc(100% - 79.5px),calc(100% - 79.5px) calc(100% - 59px),
    /*下边*/ calc(100% - 300.5px) calc(100% - 59px),calc(100% - 310.5px) calc(100% - 69px),310.5px calc(100% - 69px),300.5px calc(100% - 59px),79.5px calc(100% - 59px),
    /*左边*/ 59px calc(100% - 79.5px),59px calc(100% - 179.5px),39px calc(100% - 199.5px),39px 199.5px,59px 179.5px,59px 79.5px,
    /*上左边*/ 79.5px 59px,calc(50% - 199.5px) 59px,calc(50% - 179.5px) 79px,50% 79px);
    z-index: 1;
}

page::after {
    width: 100%;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    position: absolute;
    background: rgba(2,214,251,0.1);
    /*  background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(2,214,251,0.9));*/
    clip-path: polygon( 50% 0,100% 0,100% 100%,0 100%,0 0,50% 0,
    /*上左内*/ 50% 80px,calc(50% - 180px) 80px,calc(50% - 200px) 60px,80px 60px,60px 80px,
    /*左内*/ 60px 180px,40px 200px,40px calc(100% - 200px),60px calc(100% - 180px),60px calc(100% - 80px),80px calc(100% - 60px),
    /*下内*/ 300px calc(100% - 60px),310px calc(100% - 70px),calc(100% - 310px) calc(100% - 70px),calc(100% - 300px) calc(100% - 60px),calc(100% - 80px) calc(100% - 60px),calc(100% - 60px) calc(100% - 80px),
    /*右内*/ calc(100% - 60px) calc(100% - 180px),calc(100% - 40px) calc(100% - 200px),calc(100% - 40px) 200px,calc(100% - 60px) 180px,calc(100% - 60px) 80px,calc(100% - 80px) 60px,
    /*上右内*/ calc(50% + 200px) 60px,calc(50% + 180px) 80px,50% 80px);
    z-index: 0;
}

page {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background:transparent;
    padding:60px 40px;
    box-sizing:border-box;

}
ui-frame {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: 100%;
    flex: 1;
    --center-width: 110px;
    clip-path: polygon(
    /*上左内*/ 50% 24px,calc(50% - 180px - 2px) 24px,calc(50% - 200px - 2px) 4px,42px 4px,24px 22px,
    /*左内*/ 24px 122px,4px 142px,4px calc(100% - 142px),24px calc(100% - 122px),24px calc(100% - 240px),24px calc(100% - 22px), 42px calc(100% - 4px),
    /*下内*/ 258px calc(100% - 4px),268px calc(100% - 14px),calc(100% - 268px) calc(100% - 14px),calc(100% - 258px) calc(100% - 4px),calc(100% - 42px) calc(100% - 4px),calc(100% - 24px) calc(100% - 22px),
    /*右内*/ calc(100% - 24px) calc(100% - 122px),calc(100% - 4px) calc(100% - 142px),calc(100% - 4px) 142px,calc(100% - 24px) 122px,calc(100% - 24px) 22px,calc(100% - 42px) 4px,
    /*上右内*/ calc(50% + 200px + 2px) 4px,calc(50% + 180px + 2px) 24px,50% 24px );
    /*clip-path: polygon(*/
    /*右下外*/ /*100% 50%, 100% calc(100% - 138px), calc(100% - 20px) calc(100% - 118px), calc(100% - 20px) calc(100% - 18px), calc(100% - 38px) 100%,*/
    /*下外*/ /*calc(100% - 254px) 100%, calc(100% - 264px) calc(100% - 10px), 264px calc(100% - 10px), 254px 100%, 40px 100%, 20px calc(100% - 20px),*/
    /*左外*/ /*20px calc(100% - 118px), 0px calc(100% - 138px), 0 138px, 20px 118px, 20px 37px,*/
    /*上外*/ /*744px 37px, 744px 23px, 766px 0, calc(100% - 38px) 0,*/
    /*右上外*/ /*calc(100% - 20px) 18px, calc(100% - 20px) 118px, 100% 138px);*/
    background: rgba(2, 64, 101, 0.9);
}
    ui-frame:before {
        z-index: 1;
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
         background: #02d6fb;
        --lr-margin-width: 138px;
        --t-center-width: 200px;
        --b-margin-width: 200px;
        clip-path: polygon(
        /*上左内*/ 50% 24px,calc(50% - 180px - 2px) 24px,calc(50% - 200px - 2px) 4px,42px 4px,24px 22px,
        /*左内*/   24px 122px,4px 142px,4px calc(100% - 142px),24px calc(100% - 122px),24px calc(100% - 240px),24px calc(100% - 22px),42px calc(100% - 4px),
        /*下内*/ 258px calc(100% - 4px),268px calc(100% - 14px),calc(100% - 268px) calc(100% - 14px),calc(100% - 258px) calc(100% - 4px),calc(100% - 42px) calc(100% - 4px),calc(100% - 24px) calc(100% - 22px),
        /*右内*/ calc(100% - 24px) calc(100% - 122px),calc(100% - 4px) calc(100% - 142px),calc(100% - 4px) 142px,calc(100% - 24px) 122px,calc(100% - 24px) 22px,calc(100% - 42px) 4px,
        /*上右内*/ calc(50% + 200px + 2px) 4px,calc(50% + 180px + 2px) 24px,50% 24px,
                   50% 25px,calc(50% + 180px + 2.5px) 25px,calc(50% + 200px + 2.5px) 5px,calc(100% - 42.5px) 5px,calc(100% - 25px) 22.5px,calc(100% - 25px) 122.5px,calc(100% - 5px) 142.5px,calc(100% - 5px) calc(100% - 142.5px),calc(100% - 25px) calc(100% - 122.5px),calc(100% - 25px) calc(100% - 22.5px),
                   calc(100% - 42.5px) calc(100% - 5px),calc(100% - 257.5px) calc(100% - 5px),calc(100% - 267.5px) calc(100% - 15px),267.5px calc(100% - 15px), 257.5px calc(100% - 5px),
                   42.5px calc(100% - 5px),25px calc(100% - 22.5px),25px calc(100% - 240.5px),25px calc(100% - 122.5px),5px calc(100% - 142.5px),5px 142.5px,25px 122.5px,
                   25px 22.5px,42.5px 5px,calc(50% - 200px - 2.5px) 5px,calc(50% - 180px - 2.5px) 25px, 50% 25px
        
        );

        /*clip-path: polygon(*/ /*右下外*/ /*100% 50%, 100% calc(100% - 138px), calc(100% - 20px) calc(100% - 118px), calc(100% - 20px) calc(100% - 18px), calc(100% - 38px) 100%,*/
        /*下外*/ /*calc(100% - 254px) 100%, calc(100% - 264px) calc(100% - 10px), 264px calc(100% - 10px), 254px 100%, 40px 100%, 20px calc(100% - 20px),*/
        /*左外*/ /*20px calc(100% - 118px), 0px calc(100% - 138px), 0 138px, 20px 118px, 20px 37px,*/
        /*上外*/ /*744px 37px, 744px 23px, 766px 0, calc(100% - 38px) 0,*/
        /*右上外*/ /*calc(100% - 20px) 18px, calc(100% - 20px) 118px, 100% 138px, 100% 50%,*/
        /*右上内*/ /*calc(100% - 1px) 50%, calc(100% - 1px) 138.5px, calc(100% - 21px) 118.5px, calc(100% - 21px) 18.5px, calc(100% - 38.5px) 1px,*/
        /*上内*/ /*766.5px 1px, 745px 23.5px, 745px 38px,*/
        /*左内*/ /*21px 38px, 21px 118.5px, 1px 138.5px, 1px calc(100% - 138.5px), 21px calc(100% - 118.5px), 21px calc(100% - 20.5px),*/
        /*下内*/ /*40.5px calc(100% - 1px), 253.5px calc(100% - 1px), 263.5px calc(100% - 11px), calc(100% - 263.5px) calc(100% - 11px), calc(100% - 253.5px) calc(100% - 1px), calc(100% - 38.5px) calc(100% - 1px), calc(100% - 21px) calc(100% - 18.5px),*/
        /*右下内*/ /*calc(100% - 21px) calc(100% - 118.5px), calc(100% - 1px) calc(100% - 138.5px), calc(100% - 1px) 50%);*/
    }
/*login:before {
    width: 100%;
    height: 100%;
    content: "";
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(50% 0,calc(100% - 10px) 0,100% 10px,
                       100% calc(100% - 10px),calc(100% - 10px) 100%,
                       10px 100%,0 calc(100% - 10px),0 10px,10px 0,
                       50% 0,50% 1px,10.5px 1px,1px 10.5px,1px calc(100% - 10.5px),
                       10.5px calc(100% - 1px),calc(100% - 10.5px) calc(100% - 1px),
                       calc(100% - 1px) calc(100% - 10.5px) ,calc(100% - 1px) 10.5px,
                       calc(100% - 10.5px) 1px,50% 1px
    
    );
    z-index: 0;
}
login:after {
    width: 100%;
    height: 100%;
    content: "";
    position: absolute;
    background: rgba(2,214,251,0.1);
    clip-path: polygon(50% 0,calc(100% - 10px) 0,100% 10px, 100% calc(100% - 10px),calc(100% - 10px) 100%, 10px 100%,0 calc(100% - 10px),0 10px,10px 0, 50% 0);
    z-index: 0;
}*/
login {
    width: 300px;
    height: 168px;
    left: calc(50vw - 150px);
    top: calc(50vh - 84px);
    display: flex;
    flex-direction: column;
    border: 1px solid #02d6fb;
    color: #02d6fb;
    margin-bottom: 20vh;
}

login>item{
    display:flex;
    height:40px;
    padding:4px 12px;
    align-items:center;
    z-index:1;
}
login > item > input {
    border: 1px solid #02d6fb;
    background: #333333aa;
    outline: none;
    flex: 1;
    height: 26px;
    z-index: 1;
    padding: 0px 8px;
    color: #02d6fb;
    font-family:weilai;
}
login > item.button-item{
    flex-direction:row-reverse;
}
login > item > button {
    border: 1px solid #02d6fb;
    background: #333333aa;
    outline: none;
    min-width: 100px;
    height: 30px;
    z-index: 1;
    background: #02d6fb88;
    color: #fff;
    cursor:pointer;
}
login > item > button:hover{
    opacity:0.8;
}
login > item > button:active {
    box-shadow: 0px 0px 2px 0px #02d6fb;
}