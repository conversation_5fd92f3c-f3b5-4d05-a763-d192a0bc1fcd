/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.122
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as y}from"./chunk-EU6MOGEX.js";import{d as j}from"./chunk-MEAQKBL7.js";import{b as d,i as L}from"./chunk-RKBLQ2M4.js";import{a as R}from"./chunk-K3VQHMI6.js";import{a as s,b as W,c as T,d as q}from"./chunk-UTRPTI5S.js";import{a as w}from"./chunk-TFNGIACM.js";import{a as P}from"./chunk-OW23VKVW.js";import{a as A,b as z}from"./chunk-LKAZ42NI.js";import{e as l}from"./chunk-MYHWD27O.js";function x(o,t){z.typeOf.object("ellipsoid",o),this._ellipsoid=o,this._cameraPosition=new s,this._cameraPositionInScaledSpace=new s,this._distanceToLimbInScaledSpaceSquared=0,l(t)&&(this.cameraPosition=t)}Object.defineProperties(x.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},cameraPosition:{get:function(){return this._cameraPosition},set:function(o){let e=this._ellipsoid.transformPositionToScaledSpace(o,this._cameraPositionInScaledSpace),a=s.magnitudeSquared(e)-1;s.clone(o,this._cameraPosition),this._cameraPositionInScaledSpace=e,this._distanceToLimbInScaledSpaceSquared=a}}});var U=new s;x.prototype.isPointVisible=function(o){let e=this._ellipsoid.transformPositionToScaledSpace(o,U);return F(e,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};x.prototype.isScaledSpacePointVisible=function(o){return F(o,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};var at=new s;x.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid=function(o,t){let e=this._ellipsoid,a,i;return l(t)&&t<0&&e.minimumRadius>-t?(i=at,i.x=this._cameraPosition.x/(e.radii.x+t),i.y=this._cameraPosition.y/(e.radii.y+t),i.z=this._cameraPosition.z/(e.radii.z+t),a=i.x*i.x+i.y*i.y+i.z*i.z-1):(i=this._cameraPositionInScaledSpace,a=this._distanceToLimbInScaledSpaceSquared),F(o,i,a)};x.prototype.computeHorizonCullingPoint=function(o,t,e){return Q(this._ellipsoid,o,t,e)};var v=q.clone(q.UNIT_SPHERE);x.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid=function(o,t,e,a){let i=Z(this._ellipsoid,e,v);return Q(i,o,t,a)};x.prototype.computeHorizonCullingPointFromVertices=function(o,t,e,a,i){return J(this._ellipsoid,o,t,e,a,i)};x.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid=function(o,t,e,a,i,c){let n=Z(this._ellipsoid,i,v);return J(n,o,t,e,a,c)};var nt=[];x.prototype.computeHorizonCullingPointFromRectangle=function(o,t,e){z.typeOf.object("rectangle",o);let a=L.subsample(o,t,0,nt),i=j.fromPoints(a);if(!(s.magnitude(i.center)<.1*t.minimumRadius))return this.computeHorizonCullingPoint(i.center,a,e)};var st=new s;function Z(o,t,e){if(l(t)&&t<0&&o.minimumRadius>-t){let a=s.fromElements(o.radii.x+t,o.radii.y+t,o.radii.z+t,st);o=q.fromCartesian3(a,e)}return o}function Q(o,t,e,a){z.typeOf.object("directionToPoint",t),z.defined("positions",e),l(a)||(a=new s);let i=Y(o,t),c=0;for(let n=0,r=e.length;n<r;++n){let m=e[n],h=K(o,m,i);if(h<0)return;c=Math.max(c,h)}return X(i,c,a)}var M=new s;function J(o,t,e,a,i,c){z.typeOf.object("directionToPoint",t),z.defined("vertices",e),z.typeOf.number("stride",a),l(c)||(c=new s),a=P(a,3),i=P(i,s.ZERO);let n=Y(o,t),r=0;for(let m=0,h=e.length;m<h;m+=a){M.x=e[m]+i.x,M.y=e[m+1]+i.y,M.z=e[m+2]+i.z;let u=K(o,M,n);if(u<0)return;r=Math.max(r,u)}return X(n,r,c)}function F(o,t,e){let a=t,i=e,c=s.subtract(o,a,U),n=-s.dot(c,a);return!(i<0?n>0:n>i&&n*n/s.magnitudeSquared(c)>i)}var ct=new s,rt=new s;function K(o,t,e){let a=o.transformPositionToScaledSpace(t,ct),i=s.magnitudeSquared(a),c=Math.sqrt(i),n=s.divideByScalar(a,c,rt);i=Math.max(1,i),c=Math.max(1,c);let r=s.dot(n,e),m=s.magnitude(s.cross(n,e,n)),h=1/c,u=Math.sqrt(i-1)*h;return 1/(r*h-m*u)}function X(o,t,e){if(!(t<=0||t===1/0||t!==t))return s.multiplyByScalar(o,t,e)}var D=new s;function Y(o,t){return s.equals(t,s.ZERO)?t:(o.transformPositionToScaledSpace(t,D),s.normalize(D,D))}var Pt=x;var O={};O.getHeight=function(o,t,e){if(!Number.isFinite(t))throw new A("scale must be a finite number.");if(!Number.isFinite(e))throw new A("relativeHeight must be a finite number.");return(o-e)*t+e};var mt=new W;O.getPosition=function(o,t,e,a,i){let c=t.cartesianToCartographic(o,mt);if(!l(c))return s.clone(o,i);let n=O.getHeight(c.height,e,a);return s.fromRadians(c.longitude,c.latitude,n,t,i)};var $=O;var dt={NONE:0,BITS12:1},S=Object.freeze(dt);var C=new s,lt=new s,f=new T,V=new d,ht=new d,pt=Math.pow(2,12);function p(o,t,e,a,i,c,n,r,m,h){let u=S.NONE,g,N;if(l(t)&&l(e)&&l(a)&&l(i)){let E=t.minimum,I=t.maximum,_=s.subtract(I,E,lt),tt=a-e;Math.max(s.maximumComponent(_),tt)<pt-1?u=S.BITS12:u=S.NONE,g=d.inverseTransformation(i,new d);let ot=s.negate(E,C);d.multiply(d.fromTranslation(ot,V),g,g);let b=C;b.x=1/_.x,b.y=1/_.y,b.z=1/_.z,d.multiply(d.fromScale(b,V),g,g),N=d.clone(i),d.setTranslation(N,s.ZERO,N),i=d.clone(i,new d);let et=d.fromTranslation(E,V),it=d.fromScale(_,ht),k=d.multiply(et,it,V);d.multiply(i,k,i),d.multiply(N,k,N)}this.quantization=u,this.minimumHeight=e,this.maximumHeight=a,this.center=s.clone(o),this.toScaledENU=g,this.fromScaledENU=i,this.matrix=N,this.hasVertexNormals=c,this.hasWebMercatorT=P(n,!1),this.hasGeodeticSurfaceNormals=P(r,!1),this.exaggeration=P(m,1),this.exaggerationRelativeHeight=P(h,0),this.stride=0,this._offsetGeodeticSurfaceNormal=0,this._offsetVertexNormal=0,this._calculateStrideAndOffsets()}p.prototype.encode=function(o,t,e,a,i,c,n,r){let m=a.x,h=a.y;if(this.quantization===S.BITS12){e=d.multiplyByPoint(this.toScaledENU,e,C),e.x=w.clamp(e.x,0,1),e.y=w.clamp(e.y,0,1),e.z=w.clamp(e.z,0,1);let u=this.maximumHeight-this.minimumHeight,g=w.clamp((i-this.minimumHeight)/u,0,1);T.fromElements(e.x,e.y,f);let N=y.compressTextureCoordinates(f);T.fromElements(e.z,g,f);let E=y.compressTextureCoordinates(f);T.fromElements(m,h,f);let I=y.compressTextureCoordinates(f);if(o[t++]=N,o[t++]=E,o[t++]=I,this.hasWebMercatorT){T.fromElements(n,0,f);let _=y.compressTextureCoordinates(f);o[t++]=_}}else s.subtract(e,this.center,C),o[t++]=C.x,o[t++]=C.y,o[t++]=C.z,o[t++]=i,o[t++]=m,o[t++]=h,this.hasWebMercatorT&&(o[t++]=n);return this.hasVertexNormals&&(o[t++]=y.octPackFloat(c)),this.hasGeodeticSurfaceNormals&&(o[t++]=r.x,o[t++]=r.y,o[t++]=r.z),t};var ut=new s,B=new s;p.prototype.addGeodeticSurfaceNormals=function(o,t,e){if(this.hasGeodeticSurfaceNormals)return;let a=this.stride,i=o.length/a;this.hasGeodeticSurfaceNormals=!0,this._calculateStrideAndOffsets();let c=this.stride;for(let n=0;n<i;n++){for(let u=0;u<a;u++){let g=n*a+u,N=n*c+u;t[N]=o[g]}let r=this.decodePosition(t,n,ut),m=e.geodeticSurfaceNormal(r,B),h=n*c+this._offsetGeodeticSurfaceNormal;t[h]=m.x,t[h+1]=m.y,t[h+2]=m.z}};p.prototype.removeGeodeticSurfaceNormals=function(o,t){if(!this.hasGeodeticSurfaceNormals)return;let e=this.stride,a=o.length/e;this.hasGeodeticSurfaceNormals=!1,this._calculateStrideAndOffsets();let i=this.stride;for(let c=0;c<a;c++)for(let n=0;n<i;n++){let r=c*e+n,m=c*i+n;t[m]=o[r]}};p.prototype.decodePosition=function(o,t,e){if(l(e)||(e=new s),t*=this.stride,this.quantization===S.BITS12){let a=y.decompressTextureCoordinates(o[t],f);e.x=a.x,e.y=a.y;let i=y.decompressTextureCoordinates(o[t+1],f);return e.z=i.x,d.multiplyByPoint(this.fromScaledENU,e,e)}return e.x=o[t],e.y=o[t+1],e.z=o[t+2],s.add(e,this.center,e)};p.prototype.getExaggeratedPosition=function(o,t,e){e=this.decodePosition(o,t,e);let a=this.exaggeration,i=this.exaggerationRelativeHeight;if(a!==1&&this.hasGeodeticSurfaceNormals){let n=this.decodeGeodeticSurfaceNormal(o,t,B),r=this.decodeHeight(o,t),m=$.getHeight(r,a,i)-r;e.x+=n.x*m,e.y+=n.y*m,e.z+=n.z*m}return e};p.prototype.decodeTextureCoordinates=function(o,t,e){return l(e)||(e=new T),t*=this.stride,this.quantization===S.BITS12?y.decompressTextureCoordinates(o[t+2],e):T.fromElements(o[t+4],o[t+5],e)};p.prototype.decodeHeight=function(o,t){return t*=this.stride,this.quantization===S.BITS12?y.decompressTextureCoordinates(o[t+1],f).y*(this.maximumHeight-this.minimumHeight)+this.minimumHeight:o[t+3]};p.prototype.decodeWebMercatorT=function(o,t){return t*=this.stride,this.quantization===S.BITS12?y.decompressTextureCoordinates(o[t+3],f).x:o[t+6]};p.prototype.getOctEncodedNormal=function(o,t,e){t=t*this.stride+this._offsetVertexNormal;let a=o[t]/256,i=Math.floor(a),c=(a-i)*256;return T.fromElements(i,c,e)};p.prototype.decodeGeodeticSurfaceNormal=function(o,t,e){return t=t*this.stride+this._offsetGeodeticSurfaceNormal,e.x=o[t],e.y=o[t+1],e.z=o[t+2],e};p.prototype._calculateStrideAndOffsets=function(){let o=0;switch(this.quantization){case S.BITS12:o+=3;break;default:o+=6}this.hasWebMercatorT&&(o+=1),this.hasVertexNormals&&(this._offsetVertexNormal=o,o+=1),this.hasGeodeticSurfaceNormals&&(this._offsetGeodeticSurfaceNormal=o,o+=3),this.stride=o};var G={position3DAndHeight:0,textureCoordAndEncodedNormals:1,geodeticSurfaceNormal:2},H={compressed0:0,compressed1:1,geodeticSurfaceNormal:2};p.prototype.getAttributes=function(o){let t=R.FLOAT,e=R.getSizeInBytes(t),a=this.stride*e,i=0,c=[];function n(r,m){c.push({index:r,vertexBuffer:o,componentDatatype:t,componentsPerAttribute:m,offsetInBytes:i,strideInBytes:a}),i+=m*e}if(this.quantization===S.NONE){n(G.position3DAndHeight,4);let r=2;r+=this.hasWebMercatorT?1:0,r+=this.hasVertexNormals?1:0,n(G.textureCoordAndEncodedNormals,r),this.hasGeodeticSurfaceNormals&&n(G.geodeticSurfaceNormal,3)}else{let r=this.hasWebMercatorT||this.hasVertexNormals,m=this.hasWebMercatorT&&this.hasVertexNormals;n(H.compressed0,r?4:3),m&&n(H.compressed1,1),this.hasGeodeticSurfaceNormals&&n(H.geodeticSurfaceNormal,3)}return c};p.prototype.getAttributeLocations=function(){return this.quantization===S.NONE?G:H};p.clone=function(o,t){if(l(o))return l(t)||(t=new p),t.quantization=o.quantization,t.minimumHeight=o.minimumHeight,t.maximumHeight=o.maximumHeight,t.center=s.clone(o.center),t.toScaledENU=d.clone(o.toScaledENU),t.fromScaledENU=d.clone(o.fromScaledENU),t.matrix=d.clone(o.matrix),t.hasVertexNormals=o.hasVertexNormals,t.hasWebMercatorT=o.hasWebMercatorT,t.hasGeodeticSurfaceNormals=o.hasGeodeticSurfaceNormals,t.exaggeration=o.exaggeration,t.exaggerationRelativeHeight=o.exaggerationRelativeHeight,t._calculateStrideAndOffsets(),t};var Wt=p;export{Pt as a,Wt as b};
