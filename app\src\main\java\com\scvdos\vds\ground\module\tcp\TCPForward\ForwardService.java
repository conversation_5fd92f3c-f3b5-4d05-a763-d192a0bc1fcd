package com.scvdos.vds.ground.module.tcp.TCPForward;

import android.util.Log;

import androidx.annotation.NonNull;

import com.scvdos.vds.ground.api.messages.vdoslink.commands.command_long_base;
import com.scvdos.vds.ground.api.messages.vdoslink.constants.MAV_FRAME;
import com.scvdos.vds.ground.api.messages.vdoslink.vdoslink_message_command_long;
import com.scvdos.vds.ground.module.groundapi.link.IVehicle;
import com.scvdos.vds.ground.module.tcp.Manager;

import java.io.IOException;
import java.net.ServerSocket;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

public class ForwardService implements Runnable {

    private static  final  String TAG="ForwardService";
    private final Manager m_service;

    public ForwardService(Manager service){
        m_service=service;
    }
    private final List<ForwardClient> m_clients=new ArrayList<>();
    private Thread m_thread=null;
    private boolean m_running=false;
    private final Object thread_mutex=new Object();
    private final Object client_thread_mutex=new Object();

    private Thread m_thread_queue=null;
    public void start(){
        Log.d(TAG, "start: -------tcp start");
        synchronized (thread_mutex) {
            if (m_thread != null) {
                return;
            }
            m_running=true;
            m_thread=new Thread(this);
            m_thread.start();
        }
        if(m_thread_queue!=null){
            return;
        }
        m_thread_queue=new Thread(m_received_process);
        m_thread_queue.start();
    }
    public void stop(){
        m_running=false;
        m_thread.interrupt();
        m_thread_queue.interrupt();
    }

    public void send_all_message(int msgid,byte[] message){
        List<ForwardClient> temp = new ArrayList<>(m_clients);
        synchronized (client_thread_mutex) {
            temp.addAll(m_clients);
        }
        for(int n=0;n<temp.size();n++){
            temp.get(n).send(msgid,message);
        }
    }
    public void remove(ForwardClient client){
        synchronized (client_thread_mutex) {
            m_clients.remove(client);
        }
    }
    @Override
    public void run() {
        ServerSocket m_socket_server=null;
        while (m_running && !Thread.currentThread().isInterrupted()){
            try {
                m_socket_server=new ServerSocket(10090);
                while (m_running && !Thread.currentThread().isInterrupted()) {
                    try {
                        var clientSocket = m_socket_server.accept();
                        ForwardClient client=new ForwardClient(this,clientSocket);
                        synchronized (client_thread_mutex) {
                            m_clients.add(client);
                        }
                        Log.d(TAG, "start: -------accept client");
                        client.loop();
                    }catch (Exception ignored){
                        Log.d(TAG, "start: -------accept err");
                        ignored.printStackTrace();
                        break;
                    }
                }
                m_socket_server.close();
            } catch (IOException ignored) {
            }
        }
        if(m_socket_server!=null) {
            try {
                m_socket_server.close();
            } catch (IOException ignored) {
            }
        }
        synchronized (thread_mutex) {
            m_thread = null;

        }
    }

    private final LinkedBlockingQueue<client_message> m_received_queue=new LinkedBlockingQueue<>();
    public void onMessage(int msgid,byte[] message,ForwardClient client){
        try {
            m_received_queue.put(new client_message(msgid,message,client));
        } catch (InterruptedException ignored) {

        }
    }
    public void onClientMessage(ForwardService.client_message message){
        Log.d(TAG, "onClientMessage: "+message.m_msgid);
        switch (message.m_msgid){
            case 0x01:{
                var command=message.m_message[0];
                var param=(message.m_message[1] &0xff)|((message.m_message[2]<<8) &0xff);
                switch (command){
                    case 0x01:{
                        if(!m_service.CommandTakeOff(param, (b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i,command}))){
                            message.response(message.m_msgid+0xF0,new byte[]{0 ,0,command});
                        }
                        break;
                    }
                    case 0x02:{
                        if(!m_service.CommandMissionStart((b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i,command}))){
                            message.response(message.m_msgid+0xF0,new byte[]{0 ,0,command});
                        }
                        //auto
                        break;
                    }
                    case 0x03:{
                        if(!m_service.CommandLand( (b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i,command}))){
                            message.response(message.m_msgid+0xF0,new byte[]{0 ,0,command});
                        }
                        break;
                    }
                    case 0x04:{
                        if(!m_service.CommandRTL((b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i,command}))) {
                            message.response(message.m_msgid+0xF0, new byte[]{0, 0, command});
                        }
                        break;
                    }
                    case 0x05:{
                        if(!m_service.CommandMissionPause((b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i,command}))){
                            message.response(message.m_msgid+0xF0,new byte[]{0 ,0,command});
                        }
                        //pause
                        break;
                    }case 0x06:{
                        if(!m_service.CommandMissionResume((b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i,command}))){
                            message.response(message.m_msgid+0xF0,new byte[]{0 ,0,command});
                        }
                        //resume
                        break;
                    }
                }
                break;
            }
            case 0x02:{
                if(!m_service.CommandAutoMission(message.m_message, (b, i) -> message.response(message.m_msgid+0xF0,new byte[]{b ,i}))){
                    message.response(message.m_msgid+0xF0,new byte[]{0,0});
                }
                break;
            }
            case 0x03:{
                ByteBuffer msg=ByteBuffer.wrap(message.m_message);
                msg.order(ByteOrder.LITTLE_ENDIAN);
                var command = buildPayloadCommand(msg);
                if(!m_service.CommandCommandLong(command,(b,i)->message.response(message.m_msgid+0xF0,new byte[]{b}))){
                    message.response(message.m_msgid+0xF0,new byte[]{0});
                }
                break;
            }
            case 0x04:{
                ByteBuffer msg=ByteBuffer.wrap(message.m_message);
                msg.order(ByteOrder.LITTLE_ENDIAN);
                var x=msg.getFloat();
                var y=msg.getFloat();
                var z=msg.getFloat();
                var vx=msg.getFloat();
                var vy=msg.getFloat();
                var vz=msg.getFloat();
                var yaw=msg.getFloat();
                var yaw_rate=msg.getFloat();
                if(!m_service.CommandGuidedControl(MAV_FRAME.MAV_FRAME_BODY_OFFSET_NED, x, y, z, vx, vy, vz, yaw, yaw_rate, (b, i) ->message.response(message.m_msgid+0xF0,new byte[]{b ,i}))){
                    message.response(message.m_msgid+0xF0,new byte[]{0});
                }
            }
        }

    }

    @NonNull
    public static vdoslink_message_command_long buildPayloadCommand(ByteBuffer msg) {
        var p1= msg.getFloat();
        var p2= msg.getFloat();
        var p3= msg.getFloat();
        var p4= msg.getFloat();
        var p5= msg.getFloat();
        var p6= msg.getFloat();
        var p7= msg.getFloat();
        return new vdoslink_message_command_long(0, new command_long_base() {
           @Override
           public int get_command() {
               return 31010;
           }
           @Override
           public  float get_param1() { return p1; }

           @Override
           public    float get_param2() {
               return p2;
           }

           @Override
           public  float get_param3() {
               return p3;
           }

           @Override
           public   float get_param4() {
               return p4;
           }

           @Override
           public  float get_param5() {
               return p5;
           }

           @Override
           public  float get_param6() {
               return p6;
           }

           @Override
           public  float get_param7() {
               return p7;
           }
        });
    }
    private final Runnable m_received_process= () -> {
        while (m_running && !Thread.currentThread().isInterrupted()){
            try {
                var message= m_received_queue.take();
                onClientMessage(message);
            } catch (InterruptedException e) {
              Thread.currentThread().interrupt();
            }
        }
    };
    public static class client_message{
       private final ForwardClient m_client;
       public final int m_msgid;
       public final byte[] m_message;
       public  client_message(int msgid,byte[] message,ForwardClient client){
           m_msgid=msgid;
           m_message=message;
           m_client=client;
       }
       public void response(int msgid,byte[] message){
           m_client.send(msgid,message);
       }
    }
}
