﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Runtime.ConstrainedExecution;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.Extensions.Localization;

namespace VehicleManagerSystem.Extension
{
    [HtmlTargetElement("layui-select")]
    public class LayuiSelect([FromServices] IStringLocalizer<LanguageResource> language) : TagHelper
    {
       

        public Enum? target { get; set; }
        public object min { get; set; } = null!;
        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "vui-select";
            if (target != null)
            {
                var tf = target.GetType();
                bool is_beginraw = min == null!;
              
                int index = 0;
                foreach (var cur in Enum.GetValues(tf))
                {
                   
                    if (cur.Equals(min))
                    {
                        is_beginraw = true;

                    }
                    var key = tf.GetField(cur.ToString() ?? "")?.GetCustomAttribute<DisplayAttribute>()?.Name;
                    var display   =key==null?"": language[key];
                    if (index == 0)
                    {
                          output.Content.AppendHtml($"<vui-selected value='"+cur+"' title='"+display+"'></vui-selected>");
                          output.Content.AppendHtml($"<vui-options>");
                    }
                    if (is_beginraw)
                    {
                     
                        output.Content.AppendHtml($"<vui-option value='{cur}'>{display}</vui-option>");
                    }
                    index++;
                }
                if (index > 0)
                {
                    output.Content.AppendHtml($"</vui-options>");
                }
            }
        }

    }
}  