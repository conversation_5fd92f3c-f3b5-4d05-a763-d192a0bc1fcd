﻿using System.Text;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using VehicleManagerSystem.Services;
using VehicleManagerSystem.Tables;
using VehicleManagerSystem.Utils;

namespace VehicleManagerSystem.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VehicleController(MQTTService mqtt,DataBaseService database, IStringLocalizer<LanguageResource> language) : ControllerBase
    {
        public class VehicleAddInfo
        {
            public required string serial { get; set; }
            public required string name { get; set; }
        }
        [HttpPost("add")]
        public async Task<object> Add(VehicleAddInfo info)
        {
              if (info == null)
              {
                 return ApiResult.Failed(language["ERROR_PARAMS"]);
              }
              if (!VehicleSeralBuilder.Verify(info.serial))
              {
                   return ApiResult.Failed(language["ERROR_RESULT_ADD_VEHICLE_FAILED_SERIAL"]);
              }
              return await database.SqlExecuteAsync(async sql =>
              {
                  try
                  {
                      //VDB00ZY2500TW50000AF9000001
                      var vehicle = await sql.Insertable(new TableVehicle(info.serial, info.name)).ExecuteReturnEntityAsync();
                      if (vehicle != null)
                      {
                          mqtt.Subscribe(vehicle);
                      }
                      return ApiResult.Success(vehicle);
                  }
                  catch(Exception ex) 
                  {
                      if (ex.Message.ToUpper().Contains("UNIQUE"))
                      {
                          return ApiResult.Failed(language["ERROR_RESULT_ADD_VEHICLE_FAILED_UNIQUE"]);
                      }
                      else
                      {
                          return ApiResult.Failed(language["ERROR_UNKNOW"]);
                      }
                  }
              });
        }
        public class VehicleListBizContext
        {
            public VehicleType type { get; set; } = VehicleType.None;
            public VehicleStatus status{  get; set; }=VehicleStatus.None;
            public string? keyword{  get; set; }=null;

        }
        [HttpGet("list")]
        public async Task<object> List(int page,int limit,[FromQuery]VehicleListBizContext  biz_context)
        {
            return await database.SqlExecuteAsync(async sql =>
            {
                var query = sql.Queryable<TableVehicle>();
                if (biz_context != null)
                {
                    if (biz_context.type != VehicleType.None)
                    {
                        query.Where(it => it.VehicleType == biz_context.type);
                    }
                    if (biz_context.status != VehicleStatus.None)
                    {
                        query.Where(it => it.VehicleStatus == biz_context.status);
                    }
                    if (!string.IsNullOrWhiteSpace(biz_context?.keyword)){
                          query.Where(it => it.VehicleName.Contains(biz_context.keyword) ||
                                        it.VehicleManufacturer.Contains(biz_context.keyword) ||
                                        it.VehicleSerial == biz_context.keyword ||
                                        it.VehicleModel ==biz_context.keyword);
                    }
                }
               
                return ApiResult.Success( new {
                      count = await query.Clone().CountAsync(),
                      list  = await query.ToPageListAsync( page,limit),
                      code = 0
                });
            });
        }
    }
}
