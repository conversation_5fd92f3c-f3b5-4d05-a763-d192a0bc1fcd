﻿(function (vui) {

	vui.Aim = function () {
		this.root = $('<div class="vui-aim aim-show">');
		this.arc = $('<div class="vui-aim-arc"></div>').appendTo(this.root);
		$('<div class="vui-aim-circular"><div class="vui-aim-circular-in"></div></div>').appendTo(this.root);
		$('<div class="vui-aim-dashed"><div class="vui-aim-dashed-in"></div></div>').appendTo(this.root);
		$('<div class="vui-aim-circular-min"><div class="vui-aim-circular-min-in"></div></div>').appendTo(this.root);
		//$('<div class="vui-aim-point"><div class="vui-aim-point-in"></div></div>').appendTo(this.root);
		$('<div class="vui-aim-cross"><div class="vui-aim-cross-v"></div><div class="vui-aim-cross-h"></div></div>').appendTo(this.root);
		this.root.on("animationend", function (event) {
			  
			if ($(this).hasClass("aim-show")) {
				$(this).removeClass("aim-show");
			} else if ($(this).hasClass("hideani")) {
				$(this).remove();
			}
			
		});
		this.appendTo = function (elem) {
			this.root.appendTo(elem);
			return this;
		}
		this.remove = function () {
			this.root.addClass("hideani");
		}
	};
	vui.loadcss("aim");
	vui.loaded("aim");
})(vui);
