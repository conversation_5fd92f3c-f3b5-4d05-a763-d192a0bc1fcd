﻿using System.Text;
using VehicleManagerSystem.Tables;

namespace VehicleManagerSystem.Utils
{
    public class VehicleSeralBuilder
    {
        private const int SERIAL_CRC_POS = 19;
        private const int SERIAL_CRC_LEN = 2;

        public static bool Verify(string serial)
        {
            if (serial == null || serial.Length != 29)
            { 
                return false;
            }
            serial=serial.ToUpper();
            var crc = serial.Substring(SERIAL_CRC_POS, SERIAL_CRC_LEN);
            var origin = string.Concat(serial.AsSpan(0, SERIAL_CRC_POS), serial.AsSpan(SERIAL_CRC_POS+SERIAL_CRC_LEN));
            var raw_bytes = Encoding.ASCII.GetBytes(origin).Reverse().ToArray();
            var crc_calc = 0xdb;
            for (int i = 0; i < raw_bytes.Length; i++) { 
                var byt = raw_bytes[i];
                crc_calc ^= byt;
            }
            return crc_calc.ToString("X2") == crc;
        }
        /// <summary>
        /// 生成飞机序列号
        /// </summary>
        /// <param name="year">年份:2025</param>
        /// <param name="anchor">飞控版本:ZY00</param>
        /// <param name="model">飞机型号:TW50</param>
        /// <param name="vendorId">产地编号:000A</param>
        /// <param name="sequence">产地编号:飞机序号:1</param>
        /// <returns></returns>
        public static string? Build(int year,string anchor,string model,string vendorId,VehicleType frame,int sequence)
        {
            if (model.Length > 6) return null;
            if(vendorId.Length>4)return null;
            string serial_origin = $"VDB{anchor.PadLeft(4, '0')}{year - 2000:00}{model.PadLeft(6,'0')}{vendorId.PadLeft(4, '0')}{(short)frame:x2}{sequence:000000}";
            var raw_bytes = Encoding.ASCII.GetBytes(serial_origin).Reverse().ToArray();
            var crc_calc = 0xdb;
            for (int i = 0; i < raw_bytes.Length; i++) { 
                var byt = raw_bytes[i];
                crc_calc ^= byt;
            }
            var crc = crc_calc.ToString("X2");
            return serial_origin.Insert(SERIAL_CRC_POS, crc);
        }
    }
}
