﻿

layui.define(function (e) {
    var module = {};
    layui.use(["form"], function () {
        var $ = layui.$;
        var form = layui.form;
        module.import = function (config) {
            //templat:{
            //    templat_url:"获取模板地址",
            //    title:"标题",
            //    btn:"提交按钮",
            //    import_url:"提交地址",
            //    data:{},数据
            //    primary:"",包含在数据中的请求数据
            //}
            //templat_url, title, btn, import_url
            //
            $.ajax({
                url: config.templat_url,
                type: "get",
                loading: true,
                success: function (res) {
                    if (res.success) {
                        layer.open({
                            title: config.title,
                            type: 1,
                            fixed: true,
                            maxmin: true,
                            shadeClose: false,
                            resize: true,
                            offset: ["35px"],
                            content: res.biz_content,
                            btn: [config.btn, '取消'],
                            success: function (layero, index, options) {
                                layero.find("form").attr("lay-filter", "imprt-form-templat");
                                layero.find(".layui-layer-content").css("padding", "10px");
                                console.log(layero.find("[before]"));
                                layero.find("[before]").on("click", function () {

                                   
                                    var self = $(this);
                                    var next = self.attr("next");

                                    if (next != "" && next != null) {
                                        return;
                                    }
                                    var url = $(this).attr("url");
                                    var params = $(this).attr("param");
                                   
                                    if (params) {
                                        params = JSON.parse(params);
                                    }
                                    var data = {};
                                    for (var x in params) {
                                        let el = layero.find('[name="' + params[x] + '"]');
                                        if (el.length > 0) {
                                            data[params[x]] = el.val();
                                            if (data[params[x]] == "" || data[params[x]] == null) {
                                                layer.msg(el.attr('placeholder'), { icon: 2, time: 2000 }, function () { });
                                                return;
                                            }
                                        }
                                    }
                                    $.ajax({
                                        url: url,
                                        data: JSON.stringify(data),
                                        type:"post",
                                        success: function (s) {
                                            if (s.success) {
                                                layer.msg(s.message, { icon: 1, time: 2000 }, function () { });
                                            }
                                        }
                                    });

                                    var inv_number = 0;
                                    var inv= setInterval(function () {
                                        var ln = self.attr("interval");
                                        ln = Number(ln);
                                        inv_number++;
                                        if (ln == inv_number) {
                                            clearInterval(inv);
                                            self.attr("next", "");
                                            return;
                                        }
                                        self.attr("next","("+ (ln - inv_number)+")");
                                    },1000);
                                });
                                if (config.data && Object.keys(config.data).length > 0) {
                                    var form_elm = layero.find("form");
                                    for (var n in config.data) {
                                        form_elm.find("[name='" + n + "']").val(config.data[n]);
                                    }
                                    form_elm.find('[not-editable]').addClass("layui-disabled").attr('disabled', "true").attr("name", "");
                                }
                                layero.find("select[dynamic]").each(function () {
                                    var self = $(this);
                                    var url = $(this).attr("dynamic");
                                    $.ajax({
                                        url: url,
                                        type: "get",
                                        success: function (r) {
                                            if (r.success) {
                                                var bdy = JSON.parse(r.biz_content);
                                                for (var n = 0; n < bdy.length; n++) {
                                                    self.append('<option value="' + bdy[n].value + '">' + bdy[n].name + '</option>')
                                                }
                                                form.render();
                                             
                                            }
                                        }
                                    });
                                });
                                var linkage = function (self, url,val, param) {

                                    if (val == "" || val == null) {
                                        self.html("<option value=''>无数据</option>");
                                        form.render();
                                        return;
                                    }
                                    var data = {};
                                    data[param] = val;
                                    $.ajax({
                                        url: url,
                                        type: "post",
                                        data: JSON.stringify(data),
                                        success: function (rr) {
                                            if (rr.success) {
                                                self.html("<option value=''>请选择</option>");
                                                console.log(self);
                                                var bdy = JSON.parse(rr.biz_content);
                                               
                                                for (var n = 0; n < bdy.length; n++) {
                                                    self.append('<option value="' + bdy[n].value + '">' + bdy[n].name + '</option>');
                                                }
                                                form.render();

                                            }
                                        }

                                    });
                            
                                }
                                form.on("select(linkage)", function (e) {
                                    e.elem = $(e.elem);
                                    var next_elem = $(e.elem.attr("linkage"));
                                    var next_url = e.elem.attr("linkage-request");
                                    linkage(next_elem, next_url, e.value, e.elem.attr("name"));
                                  
                                });
                                //layero.find("select[linkage]").each(function () {
                                //    var target = layero.find($(this).attr("linkage"));
                                //    var lnk = $(this).attr('linkage-request');
                                //    var pm = target.attr("name");
                                //    target.on("click", function () {
                                //        console.log("changed");
                                //    })
                                //    target.on('change', new linkage($(this), $(this).attr('linkage-request'), target.attr("name")).change);
                                //});

                            },
                            yes: function (index, layero, options) {
                                var fields = form.val('imprt-form-templat');
                                if (config.data && Object.keys(config.data).length > 0) {
                                    fields[config.primary] = config.data[config.primary];
                                    if (Object.keys(fields).length == 1) {
                                        layer.msg("此通道订单禁止编辑", { icon: 2, time: 2000 }, function () { });
                                        return;
                                    }
                                }
                                $.ajax({
                                    url: config.import_url,
                                    type: "post",
                                    dataType: "json",
                                    loading: true,
                                    data: JSON.stringify(fields),
                                    success: function (res) {
                                        if (config && config.success && config.success(res)) {
                                            layer.close(index);
                                        }
                                    }
                                });
                            }
                        });
                    }
                    form.render();
                }, error: function () {

                    form.render();
                }
            });


        };
        module.select = function (title, data, selected) {
            var templat = $('<div class="layui-form" style="margin: 16px;"></div>');
            var elm_form = $('<div class="demo-login-container layui-form"  style="margin: 16px;"></div>');
            var elm_select_item = $('<div class="layui-form-item"></div>');
            var elm_select = $('<select name="select" class="layui-input-wrap"></select>');
            elm_select.append($('<option value="">请选择通道</option>'))
            for (let n = 0; n < data.length; n++) {
                elm_select.append($('<option value="' + data[n].value + '">' + data[n].name + '</option>'))
            }
            elm_form.append(elm_select_item.append(elm_select));
            elm_form.append($('<div class="layui-form-item"><button class="layui-btn layui-btn-fluid" lay-submit lay-filter="select-confirm">确定</button></div>'));
            templat.append(elm_form);
            layer.open({
                type: 1,
                offset: ['35px'],
                area: '350px',
                resize: false,
                shadeClose: false,
                title: title,
                content: templat.html(),
                success: function (layero, index) {
                    layero.find(".layui-layer-content").css("overflow", "unset");
                    form.render();
                    form.on('submit(select-confirm)', function (data) {
                        var field = data.field;
                        if (field.select && field.select != "") {
                            selected && selected(field.select);
                            layer.close(index);
                        }
                        return false;
                    });
                }
            });
        }
        module.render_operation = function () {
            if ($('head>#css-operation').length == 0) {
                $('head').append("<style id='css-operatio'>[data-field='operations'] > .layui-table-cell{width: auto;}</style>")
            }
            var operations_cell = $('[data-field="operations"] > .layui-table-cell');
            var cell_width = 0;
            operations_cell.css("width", "auto");
            operations_cell.each(function () {
                if (cell_width < $(this)[0].clientWidth) {
                    cell_width = $(this)[0].clientWidth;
                }
            });
            operations_cell.each(function () {
                $(this).css("width", (cell_width + 2) + "px");
            });
        }
         
    }), e("dynamic", module)
     
});