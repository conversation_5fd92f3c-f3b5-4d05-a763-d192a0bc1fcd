﻿.vui-loader {
    width: 40px;
    height: 40px;
    box-sizing: border-box;
    position: relative;
    --front-color: #333;
    --bkg-color: #eee;
}

.vui-loader-speed-1:before {
    animation-duration: 1s;
}

.vui-loader-speed-2:before {
    animation-duration: 2s;
}

.vui-loader-speed-3:before {
    animation-duration: 3s;
}

.vui-loader-speed-4:before {
    animation-duration: 4s;
}

@keyframes vui-loader-border {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.vui-loader:after {
    content: '';
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    border: 4px solid var(--bkg-color);
    left: 0;
    top: 0;
    box-sizing: border-box;
    z-index: 0;
}

.vui-loader:before {
    content: '';
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    border: 4px solid var(--front-color);
    clip-path: polygon(0% 0,50% 0,50% 50%,100% 50%,100% 100%,50% 100%,50% 50%,0 50%);
    animation-name: vui-loader-border;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    left: 0;
    top: 0;
    box-sizing: border-box;
    z-index: 1;
}

.vui-loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    z-index:99;
}

.vui-loader-no-root-node {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
}