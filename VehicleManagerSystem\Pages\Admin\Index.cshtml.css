﻿page:before {
    width: 100%;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(
    /*上左内*/ 50% 60px,80px 60px,60px 80px,
    /*左内*/ 60px 180px,40px 200px,40px calc(100% - 200px),60px calc(100% - 180px),60px calc(100% - 80px),80px calc(100% - 60px),
    /*下内*/ 300px calc(100% - 60px),310px calc(100% - 70px),calc(100% - 310px) calc(100% - 70px),calc(100% - 300px) calc(100% - 60px),calc(100% - 80px) calc(100% - 60px),calc(100% - 60px) calc(100% - 80px),
    /*右内*/ calc(100% - 60px) calc(100% - 180px),calc(100% - 40px) calc(100% - 200px),calc(100% - 40px) 200px,calc(100% - 60px) 180px,calc(100% - 60px) 80px,calc(100% - 80px) 60px,
    /*上右内*/ calc(50% + 200px) 60px,50% 60px,
    /*上右边*/ 50% 59px,calc(100% - 79.5px) 59px,
    /*右边*/ calc(100% - 59px) 79.5px,calc(100% - 59px) 179.5px,calc(100% - 39px) 199.5px,calc(100% - 39px) calc(100% - 199.5px),calc(100% - 59px) calc(100% - 179.5px),calc(100% - 59px) calc(100% - 79.5px),calc(100% - 79.5px) calc(100% - 59px),
    /*下边*/ calc(100% - 300.5px) calc(100% - 59px),calc(100% - 310.5px) calc(100% - 69px),310.5px calc(100% - 69px),300.5px calc(100% - 59px),79.5px calc(100% - 59px),
    /*左边*/ 59px calc(100% - 79.5px),59px calc(100% - 179.5px),39px calc(100% - 199.5px),39px 199.5px,59px 179.5px,59px 79.5px,
    /*上左边*/ 79.5px 59px,calc(50% - 199.5px) 59px,50% 59px);
    z-index: 1;
}

page::after {
    width: 100%;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    position: absolute;
    background: rgba(2,214,251,0.1);
    clip-path: polygon(50% 0, 100% 0, 100% 100%, 0 100%, 0 0, 50% 0, 
    /*上左内*/ 50% 60px, 80px 60px, 60px 80px, 
    /*左内*/ 60px 180px, 40px 200px, 40px calc(100% - 200px), 60px calc(100% - 180px), 60px calc(100% - 80px), 80px calc(100% - 60px), 
    /*下内*/ 300px calc(100% - 60px), 310px calc(100% - 70px), calc(100% - 310px) calc(100% - 70px), calc(100% - 300px) calc(100% - 60px), calc(100% - 80px) calc(100% - 60px), calc(100% - 60px) calc(100% - 80px), 
    /*右内*/ calc(100% - 60px) calc(100% - 180px), calc(100% - 40px) calc(100% - 200px), calc(100% - 40px) 200px, calc(100% - 60px) 180px, calc(100% - 60px) 80px, calc(100% - 80px) 60px, 
    /*上右内*/ calc(50% + 200px) 60px, 50% 60px);
    z-index: 0;
}

page {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
/*login:before {
    width: 100%;
    height: 100%;
    content: "";
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(50% 0,calc(100% - 10px) 0,100% 10px,
                       100% calc(100% - 10px),calc(100% - 10px) 100%,
                       10px 100%,0 calc(100% - 10px),0 10px,10px 0,
                       50% 0,50% 1px,10.5px 1px,1px 10.5px,1px calc(100% - 10.5px),
                       10.5px calc(100% - 1px),calc(100% - 10.5px) calc(100% - 1px),
                       calc(100% - 1px) calc(100% - 10.5px) ,calc(100% - 1px) 10.5px,
                       calc(100% - 10.5px) 1px,50% 1px
    
    );
    z-index: 0;
}
login:after {
    width: 100%;
    height: 100%;
    content: "";
    position: absolute;
    background: rgba(2,214,251,0.1);
    clip-path: polygon(50% 0,calc(100% - 10px) 0,100% 10px, 100% calc(100% - 10px),calc(100% - 10px) 100%, 10px 100%,0 calc(100% - 10px),0 10px,10px 0, 50% 0);
    z-index: 0;
}*/
 
login {
    width: 300px;
    height: 168px;
     
    left: calc(50vw - 150px);
    top: calc(50vh - 84px);
    display: flex;
    flex-direction: column;
    border: 1px solid #02d6fb;
    color: #02d6fb;
    margin-bottom:20vh;
}

login>item{
    display:flex;
    height:40px;
    padding:4px 12px;
    align-items:center;
    z-index:1;
}
login > item > input {
    border: 1px solid #02d6fb;
    background: #333333aa;
    outline: none;
    flex: 1;
    height: 26px;
    z-index: 1;
    padding: 0px 8px;
    color: #02d6fb;
    font-family:weilai;
}
login > item.button-item{
    flex-direction:row-reverse;
}
login > item > button {
    border: 1px solid #02d6fb;
    background: #333333aa;
    outline: none;
    min-width: 100px;
    height: 30px;
    z-index: 1;
    background: #02d6fb88;
    color: #fff;
    cursor:pointer;
}
login > item > button:hover{
    opacity:0.8;
}
login > item > button:active {
    box-shadow: 0px 0px 2px 0px #02d6fb;
}



.vui-main-menu {
    color: #EEE;
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 创建三列 */
    gap: 4px; /* 设置间距 */
    user-select: none;
}

body.content-body .vui-main-menu {
    display: block;
    margin-left: 32px;
}

.vui-main-menu > .vui-main-menu-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}

.vui-main-menu > .vui-main-menu-item > i {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.vui-main-menu > .vui-main-menu-item > i {
    font-size: 40px;
    flex: 1;
}

.vui-main-menu-item {
    height: 60px;
    border: 1px solid #02d6fb;
    padding: 8px 26px;
}

.vui-main-menu-item.selected {
        background: #02d6fb88;
        color: #EEE;
    }

body.content-body .vui-main-menu > .vui-main-menu-item {
    height: auto;
    flex-direction: row;
    gap: 4px;
    border: none;
    position: relative;
    float: left;
    margin-left: -25px;
}

body.content-body .vui-main-menu > .vui-main-menu-item {
    clip-path: polygon(50% 0,100% 0, calc(100% - 20px) 20px, calc(100% - 20px) 100%, 13px 100%,13px 20px,33px 0,50% 0);
}

body.content-body .vui-main-menu > .vui-main-menu-item:before {
    content: "";
    position: absolute;
    background: #02d6fb;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    clip-path: polygon(50% 0,100% 0, calc(100% - 20px) 20px, calc(100% - 20px) 100%, 13px 100%,13px 20px,33px 0,50% 0,
    /*内框*/ 50% 1px, 33.5px 1px,14px 20.5px,14px calc(100% - 1px), calc(100% - 21px) calc(100% - 1px),calc(100% - 21px) 19.5px, calc(100% - 2.5px) 1px,50% 1px );
}

.vui-main-menu-item:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

body.content-body .vui-main-menu > .vui-main-menu-item:after {
    clip-path: polygon(50% 0,100% 0, calc(100% - 20px) 20px, calc(100% - 20px) 100%, 13px 100%,13px 20px,33px 0,50% 0);
}
input:active {
    border-color: #02d6fb !important;
}
body.content-body .vui-main-menu > .vui-main-menu-item > i {
    font-size: 1.2em;
}
body .content > .menu-row {
    display: inline-block;
    position: absolute;
    z-index:1;
    
}
body.content-body .content > .menu-row {
   
    top: 0;
    left: 0;
}
.vui-main-menu-item.g1 {
    grid-column: 1 / 2;
    grid-row: 0 / 2;
}

.vui-main-menu-item.g2 {
    grid-column: 2 / 4;
    grid-row: 0 / 2;
}

.vui-main-menu-item.g3 {
    grid-column: 1 / 2;
    grid-row: 2 / 3;
}

.vui-main-menu-item.g4 {
    grid-column: 2 / 3;
    grid-row: 2 / 3;
}

.vui-main-menu-item.g5 {
    grid-column: 3 / 4;
    grid-row: 2 / 3;
}

.vui-main-menu-item.g6 {
    grid-column: 1 / 4;
    grid-row: 3 / 3;
}

.vui-main-menu-item.g7 {
    grid-column: 3 / 4;
    grid-row: 3 / 3;
}

.vui-main-menu-item:active {
    scale: 0.9;
}

.vui-main-menu-item:hover:after {
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(2,214,251,0.4));
}

body.content-body {
    justify-content: start;
    align-items: start;
    flex-direction: column;
}

body .content {
    margin: 64px 44px;
    height: calc(100% - 128px);
    width: calc(100% - 88px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

body {
    background: rgba(2,64,101,0.4);
}


body.content-body .content {
    background: transparent;
   
}
body.content-body .content {
    display: block;
    position:relative;
    
}

.frame {
    position: relative;
    height: 100%;
    flex: 1;
    --left-menu-width: calc(100% - 100px);
    clip-path: polygon( /*右下外*/ 100% 50%,100% calc(100% - 138px),calc(100% - 20px) calc(100% - 118px),calc(100% - 20px) calc(100% - 18px),calc(100% - 38px) 100%,
    /*下外*/ calc(100% - 254px) 100%,calc(100% - 264px) calc(100% - 10px),264px calc(100% - 10px),254px 100%,40px 100%,20px calc(100% - 20px),
    /* 左外*/ 20px calc(100% - 118px), 0px calc(100% - 138px),0 138px,20px 118px,20px 18px,
    /*上外*/ 38px 0px,calc(100% - 38px) 0,
    /*右上外*/ calc(100% - 20px) 18px,calc(100% - 20px) 118px,100% 138px);
    background: rgba(2,64,101,0.9);
   
}
body.content-body .frame {
    clip-path: polygon( /*右下外*/ 100% 50%,100% calc(100% - 138px),calc(100% - 20px) calc(100% - 118px),calc(100% - 20px) calc(100% - 18px),calc(100% - 38px) 100%,
    /*下外*/ calc(100% - 254px) 100%,calc(100% - 264px) calc(100% - 10px),264px calc(100% - 10px),254px 100%,40px 100%,20px calc(100% - 20px),
    /*左外*/ 20px calc(100% - 118px), 0px calc(100% - 138px),0 138px,20px 118px,20px 38px,
    /* 上外*/ var(--left-menu-width) 38px,var(--left-menu-width) 20px,calc(var(--left-menu-width) + 20px) 0,calc(100% - 38px) 0,
    /*右上外*/ calc(100% - 20px) 18px,calc(100% - 20px) 118px,100% 138px);
    z-index: 2;
}


.frame:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #02d6fb;
    clip-path: polygon(
    /*右下外*/ 100% 50%,100% calc(100% - 138px),calc(100% - 20px) calc(100% - 118px),calc(100% - 20px) calc(100% - 18px),calc(100% - 38px) 100%,
    /*下外*/ calc(100% - 254px) 100%,calc(100% - 264px) calc(100% - 10px),264px calc(100% - 10px),254px 100%,40px 100%,20px calc(100% - 20px),
    /*左外*/ 20px calc(100% - 118px), -1px calc(100% - 138px),-1px 138px,19px 118px,
    /*上外*/ 19px 18px,37.5px -1px,
    /*右上外*/ calc(100% - 37.5px) -1px,calc(100% - 19px) 19px,calc(100% - 19px) 118px,calc(100% + 1px) 138px,calc(100% + 1px) 50%,
    /*右上内*/ calc(100% - 1px) 50%,calc(100% - 1px) 138.5px,calc(100% - 21px ) 118.5px,calc(100% - 21px ) 18.5px, calc(100% - 38.5px) 1px,38.5px 1px,
    /*左内*/ 21px 18.5px,21px 118.5px,1px 138.5px,1px calc(100% - 138.5px),21px calc(100% - 118.5px),21px calc(100% - 20.5px),
    /*下内*/ 40.5px calc(100% - 1px),253.5px calc(100% - 1px),263.5px calc(100% - 11px),calc(100% - 263.5px) calc(100% - 11px),calc(100% - 253.5px) calc(100% - 1px),calc(100% - 38.5px) calc(100% - 1px),calc(100% - 21px) calc(100% - 18.5px),
    /*右下内*/ calc(100% - 21px) calc(100% - 118.5px),calc(100% - 1px) calc(100% - 138.5px),calc(100% - 1px) 50% );
    z-index: 1;
}
body.content-body .frame:before {
    clip-path: polygon(
    /*右下外*/ 100% 50%,100% calc(100% - 138px),calc(100% - 20px) calc(100% - 118px),calc(100% - 20px) calc(100% - 18px),calc(100% - 38px) 100%,
    /*下外*/ calc(100% - 254px) 100%,calc(100% - 264px) calc(100% - 10px),264px calc(100% - 10px),254px 100%,40px 100%,20px calc(100% - 20px),
    /*左外*/ 20px calc(100% - 118px), 0px calc(100% - 138px),0 138px,20px 118px,20px 38px,
    /*上外*/ var(--left-menu-width) 38px,var(--left-menu-width) 20px,calc(var(--left-menu-width) + 20px) 0,calc(100% - 38px) 0,
    /*右上外*/ calc(100% - 20px) 18px,calc(100% - 20px) 118px,100% 138px,100% 50%,
    /*右上内*/ calc(100% - 1px) 50%,calc(100% - 1px) 138.5px,calc(100% - 21px ) 118.5px,calc(100% - 21px ) 18.5px,calc(100% - 38.5px) 1px,
    /*上内*/ calc(var(--left-menu-width) + 20.5px) 1px,calc(var(--left-menu-width) + 1px) 20.5px,calc(var(--left-menu-width) + 1px) 39.2px,
    /*左内*/ 21px 39.2px,21px 118.5px,1px 138.5px,1px calc(100% - 138.5px),21px calc(100% - 118.5px),21px calc(100% - 20.5px),
    /*下内*/ 40.5px calc(100% - 1px),253.5px calc(100% - 1px),263.5px calc(100% - 11px),calc(100% - 263.5px) calc(100% - 11px),calc(100% - 253.5px) calc(100% - 1px),calc(100% - 38.5px) calc(100% - 1px),calc(100% - 21px) calc(100% - 18.5px),
    /*右下内*/ calc(100% - 21px) calc(100% - 118.5px),calc(100% - 1px) calc(100% - 138.5px),calc(100% - 1px) 50% );
}
.frame > iframe {
    display: none;
}

.frame > iframe.this {
    display: block;
}
.top-bar {
    color: #fff;
    position: absolute;
    right: 60px;
    top: 10px;
    display: flex;
    flex-direction: row;
    z-index: 98;
}
    .top-bar > .control {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 28px;
        padding: 4px 22px;
        border: 1px solid transparent;
        outline: none;
        text-decoration: none;
        color:#fff;
    }
.language {
    box-sizing: content-box;
    cursor: pointer;
}
.language > .current {
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 22px;
    border: 1px solid transparent;
}

.language.open > .current {
    background: rgba(2,64,101,0.9);
    border: 1px solid #02d6fb;
    margin-bottom: 4px;
}
.top-bar>*:hover{
    color:#02d6fb;
    cursor:pointer;
}
 
.language > .list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(2,64,101,0.9);
    border: 1px solid #02d6fb;
    display: none;
}
.language.open>.list{
    display:block;
}
.language > .list > li {
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 2px 12px;
}
.language > .list > li:hover {
    background: #02d6fb;
    color:#fff;
}