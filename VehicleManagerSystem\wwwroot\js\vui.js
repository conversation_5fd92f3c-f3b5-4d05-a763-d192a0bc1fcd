﻿$(function () {
    $(document).on("click", "vui-select>vui-selected", function (e) {

        if ($(this).parent().hasClass("open")) {
            $("vui-select.open").removeClass("open");
        } else {
            $("vui-select.open").removeClass("open");
            $(this).parent().toggleClass("open");
        }
        e.stopPropagation();
        e.preventDefault();
    });
    $(document).on("click", "vui-select>vui-options>vui-option", function () {
        var value = $(this).attr("value");
        var title = $(this).text();
        var select = $(this).parent().parent().find("vui-selected");
        select.attr("value", value);
        select.attr("title", title);
    });
    $(document).on("click", function (e) {
        $("vui-select.open").removeClass("open");
    });

    $(document).on("focus", "[readonly-fill]", function () {
        console.log("xx")
        var self = $(this)[0];
        setTimeout(function () {
            self.removeAttribute("readonly");
        },80);
    });
    $(document).on("click", "[readonly-fill]", function (e) {
        console.log("2")
        e.stopPropagation();
        e.preventDefault();
    });
    $(document).on("blur", "[readonly-fill]", function () {
        $(this).attr("readonly","true");
    });
}());