/* 基础布局 */
.main-view {
    height: 100%;
    width: 100%;
}

#map-view {
    height: 100%;
    width: 100%;
    background-color: #111; 
}

.div-border {
    content: "";
    background: rgba(2,64,101,0.9);
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    clip-path: polygon(50% 11px, calc(100% - 21px) 11px, calc(100% - 11px) 21px, calc(100% - 11px) calc(100% - 21px), calc(100% - 21px) calc(100% - 11px), 21px calc(100% - 11px), 11px calc(100% - 21px), 11px 21px, 21px 11px, 50% 11px,50% 0,0 0,0 100%,100% 100%,100% 0,50% 0 );
}

.div-border:before {
    content: "";
    background: #02d6fb;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    clip-path: polygon(50% 11px, calc(100% - 21px) 11px, calc(100% - 11px) 21px, calc(100% - 11px) calc(100% - 21px), calc(100% - 21px) calc(100% - 11px), 21px calc(100% - 11px), 11px calc(100% - 21px), 11px 21px, 21px 11px, 50% 11px,50% 10px,20.5px 10px,10px 20.5px, 10px calc(100% - 20.5px), 20.5px calc(100% - 10px), calc(100% - 20.5px) calc(100% - 10px), calc(100% - 10px) calc(100% - 20.5px), calc(100% - 10px) 20.5px, calc(100% - 20.5px) 10px, 50% 10px );
}
/* 任务列表 */
.floating-mission-list {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    width: 230px;
    height: 50vh;
    display: flex;
    flex-direction: column;
    color: #eee;
}

.mission-list-header {
    padding-left:4px;
    padding-top:4px;
    background: #02d6fb;
    color: #02d6fb;
    text-align: center;
    font-size: 16px;
    width: 100%;
    height: 40px;
    flex: 0 0 40px;
    clip-path: polygon(calc(10% + 4px) 4px, 100% 4px, 100% 100%,4px 100%, 4px calc(50% + 3px));
}

.mission-list-title {
    color: azure;
    background-color: #00000075;
    position: relative;
    display: flex;
    align-items: center;
    align-content: space-around;
    justify-content: space-around;
    width: 100%;
    height: 100%;
    text-align: center;
    clip-path: polygon(0.5px 50%, 
    0.5px calc(100% - 1px), 
    90% calc(100% - 1px), 
    calc(100% - 1px) calc(100% - 1px), 
    calc(100% - 1px) 1px, 
    10% 1px, 1px calc(50% + 1px));
    overflow: hidden;
}

.floating-mission-list > ul {
    list-style: none;
    max-height: 100%;
    padding: 8px;
    flex: 1 1 auto;
    overflow-y: auto;
    margin: 0px;
    flex-grow: 1;
    scrollbar-width: thin;
    scrollbar-color: rgba(2, 214, 251, 0.5) transparent;
    -webkit-overflow-scrolling: touch;
}

.floating-mission-list ul::-webkit-scrollbar {
    width: 5px;
}

.floating-mission-list ul::-webkit-scrollbar-thumb {
    background: rgba(2, 214, 251, 0.5);
    border-radius: 3px;
}

/* 任务项 */
.mission-item {
    background-color: #02d6fb2e;
    color: #eee;
    padding: 10px 12px;
    margin-bottom: 6px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease;
    border: 1px solid rgba(2, 214, 251, 0.3);
}

.mission-item:hover,
.mission-item.hovered {
    background-color: rgba(2, 214, 251, 0.4);
}

.mission-item.selected,
.mission-item.selected:hover {
    background-color: rgba(2, 214, 251, 0.5);
    border-color: rgba(2, 214, 251, 0.5);
}

.mission-item.hovered .drawer-buttons {
    display: flex;
}

.mission-header {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 6px;
}

.mission-footer {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #ececec;
}

/* 操作按钮 */
.drawer-buttons {
    position: absolute;
    top: 20%;
    width: 88%;
    display: none;
    flex-direction: row;
    gap: 0px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 0px;
    border: 1px solid #02d6fb

}

    .drawer-buttons button, #uploadMission, #uploadMissionStart {
        font-size: 12px;
        padding: 5px 0px;
        height: 33px;
        border: 0px solid rgba(2, 214, 251, 0.8);
        color: #eee;
        cursor: pointer;
        flex: 1;
        font-weight: 500;
        transition: background-color 0.2s ease;
    }
    .drawer-buttons:hover {
        display:flex;
    }
#uploadMission, #uploadMissionStart {
    background-color: rgba(2, 214, 251, 0.4);
}
    .drawer-buttons button:hover, #uploadMission:hover, #uploadMissionStart:hover {
        background-color: #02d6fba1;
    }
.search-btn {
    background: #2ac8d99e;
}
.search-btn:hover{
    background: #2ac8d9
}
.edit-btn {
    background-color: rgba(2, 214, 251, 0.4);
}
.drawer-buttons .delete-btn {
    background-color: rgba(200, 50, 50, 0.4);
    border-color: rgba(200, 50, 50, 0.7);
}

.drawer-buttons .delete-btn:hover {
    background-color: rgba(200, 50, 50, 0.7);
}

.mission-list-footer {
    padding: 2px;
    border-top: 1px solid rgba(2, 214, 251, 0.5);
}

.addNewMissionBtnDiv {
    background: #02d6fb;
    margin: 1px;
    flex: 0 0 44px;
    clip-path: polygon(calc(100% - 4px) 0, calc(100% - 4px) 100%, 10% 100%, 0 0);
}

#addNewMissionBtn {
    width: 100%;
    padding: 10px;
    background-color: #0134419e;
    color: #02d6fb;
    border: 1px solid rgba(2, 214, 251, 0.5);
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    font-weight: 600;
    clip-path: polygon(  calc(100% - 5px) 1px, calc(100% - 5px) calc(100% - 0.5px), calc(10% + 0.5px) calc(100% - 0.5px), 1.5px 1px )
}

#addNewMissionBtn:hover {
    background-color: rgba(2, 214, 251, 0.3);
    color: #fff;
}

.mission-list-border-left-top {
    content: "";
    background: #02d6fb;
    width: 100%;
    height: 100% ;
    position: absolute;
    clip-path: polygon(1px 23.5px, 
    24px 1px,
    70% 1px, 
    70% 0,
    24px 0, 
    0 23px, 
    0px calc(100% - 40px), 24px 100%,100% 100%,calc(100% - 1px) 100%,calc(100% - 1px) calc(100% - 1px),24.5px calc(100% - 1px),
    1px calc(100% - 40.5px),
    1px 70%);
}

.mission-list-border-right-bottom {
    display:none;
    content: "";
    background: #02d6fb;
    width: calc(100% + 3px);
    height: 100%;
    position: absolute;
    clip-path: polygon(calc(100% - 1px) 30%, calc(100% - 1px) calc(100% - 37px), calc(100% - 18px) calc(100% - 21px), calc(100% - 1px) calc(100% - 7px), calc(100% - 1px) calc(100% - 1px), 24px calc(100% - 1px),0.5px calc(100% - 21px),0.5px calc(100% - 44px),0px calc(100% - 44px), 0px calc(100% - 20.5px),24px 100%, 100% 100%, 100% 30% );
}

.end {
    width: 19px;
    height: 88%;
    top: 46px;
    position: absolute;
    background: #02d6fb;
    clip-path: polygon(100% 50%, 100% calc(100% - 36px), 10px calc(100% - 45px), 10px calc(100% - 36px), 100% calc(100% - 27px), 100% calc(100% - 18px), 10px calc(100% - 27px), 10px calc(100% - 18px), 100% calc(100% - 9px), 100% 100%, 10px calc(100% - 9px), 10px 0px, 11px 0, 100% 0px);
    left: calc(100% - 15px);
}
/* 抽屉面板 */
.drawer-panel {
    position: fixed;
    bottom: -100%;
    left: calc(50% - 50px);
    transform: translateX(-50%);
    width: 100%;
    max-width: 800px;
    background: transparent;
    transition: bottom 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    justify-content: center;
}

.drawer-panel.active {
    bottom: 20px;
}

.drawer-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: stretch;
    gap: 0;
    padding: 0;
    backdrop-filter: blur(8px);
    overflow: hidden;
}

.drawer-section {
    flex: 1 1 160px;
    background: #02d6fb2e;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    min-width: 160px;
    max-width: 180px;
    margin: 2px;
    border: 1px solid #02d6fb;
    /*clip-path:polygon(0 0, 70% 0,80% 10%,90% 0,100% 0,100% 100%,0 100%, 0 0 )*/
}

.drawer-section-header {
    color: #fff;
    text-align: center;
    padding: 5px 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(2, 214, 251, 0.5);
    margin-bottom: 10px;
}


.drawer-section-body {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 0 10px 10px 10px;
}

.drawer-section-body-other {
    display: flex;
    align-items: center;
    font-size: 13px;
    border: 1px solid rgba(2, 214, 251, 0.3);
    background: rgba(0, 0, 0, 0.3);
    overflow: hidden;
    height: 24px;
    margin-bottom: 5px;
}
.drawer-section-bodyother {
    display: flex;
    flex-wrap: wrap;
    margin-top: -6px;
    padding-left: 10px;
    padding-right: 10px;
}

.drawer-section-body-other-labelrow {
    display:flex;
}
.custom-radio-label {
    width:30px;
    padding:0px;
    margin:4px 0;
    color:#aaa;
    font-size:13px;
}
.custom-radio {
    width:15px;
    height:15px;
}

.drawer-section-body-other input[type=text] {
    flex-grow: 1;
    width: 100%;
    text-align: right;
    padding: 6px 8px;
    border: none;
    border-left: 1px solid rgba(2, 214, 251, 0.3);
    border-radius: 0;
    background: transparent;
    color: #eee;
    font-weight: 500;
}
.disabled-input-hint {
        color: #888;
        font-style: italic;
        background-color: rgba(200, 200, 200, 0.2);
        text-align: center;
    }

/* 表单样式 */
.form-group {
    display: flex;
    align-items: center;
    font-size: 13px;
    border: 1px solid rgba(2, 214, 251, 0.3);
    background: rgba(0, 0, 0, 0.3);
    padding-left: 10px;
    overflow: hidden;
}

.form-group label {
    white-space: nowrap;
    margin-right: 10px;
    font-weight: 400;
    color: #aaa;
    text-align: right;
    padding: 6px 0;
}

.form-group input,
.form-group span {
    flex-grow: 1;
    width: 100%;
    text-align: right;
    padding: 6px 8px;
    border: none;
    border-left: 1px solid rgba(2, 214, 251, 0.3);
    border-radius: 0;
    background: transparent;
    color: #eee;
    font-weight: 500;
}

.form-group input:focus {
    outline: none;
    background: rgba(2, 214, 251, 0.1);
}

.form-group .full-width-btn {
    width: 100%;
    background: transparent;
    border: none;
    color: #eee;
    padding: 8px 0;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s ease;
}

.form-group .full-width-btn:hover {
    background-color: rgba(2, 214, 251, 0.1);
}

.drawer-section-body > button#deletePoint {
    width: 100%;
    padding: 5px 0;
    font-size: 13px;
    font-weight: 600;
    color: #eee;
    border: 1px solid rgba(200, 50, 50, 0.7);
    transition: all 0.2s ease;
    cursor: pointer;
    background-color: rgba(200, 50, 50, 0.3);
}

.drawer-section-body > button#deletePoint:hover {
    background-color: rgba(200, 50, 50, 0.5);
    border-color: rgba(200, 50, 50, 0.8);
}

#saveMission {
    display: none;
}

#saveMission {
    display: block;
    width: 100%;
    padding: 5px 0;
    font-size: 13px;
    font-weight: 600;
    color: #eee;
    background: #00f1ff45;
    border: 1px solid rgba(2, 214, 251, 0.5);
    transition: background-color 0.2s ease;
}

#saveMission:hover {
    background-color: rgba(2, 214, 251, 0.5);
}

/* 抽屉状态控制 */
#editDrawer:not(.point-is-selected) #locationSection {
    display: none;
}

#editDrawer:not(.point-is-selected) #locationSectionOther {
    display: none;
}

#editDrawer:not(.point-is-selected) #deletePoint {
    display: none;
}

.form-group-button {
    display: flex;
    align-items: center;
    font-size: 10px;
    border: 1px solid rgba(2, 214, 251, 0.3);
    background: rgba(0, 0, 0, 0.3);
    overflow: hidden;
}
/* 地图提示框 */
.map-tips-box {
    position: absolute;
    top: 10%;
    left: 3%;
    width: 130px;
    transition: all 0.3s ease;
    color: #eee;
    pointer-events: none;
}

.tips-header {
    padding: 10px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.tips-header span {
    font-size: 13px;
}

.tips-content {
    font-size: 12px;
    transition: max-height 0.3s ease, padding 0.3s ease;
}


.tip-item {
    display: flex;
    justify-content: center;
    margin-bottom: 5px;
}

.tip-key {
    color: #aaa;
    margin-right: 10px;
}

.tip-value {
    color: #eee;
    font-weight: 500;
}

.tips-footer {
    border-top: 1px solid rgba(2, 214, 251, 0.5);
}

.btn-close-tips {
    width: 100%;
    padding: 8px;
    background: transparent;
    color: #eee;
    border: none;
    cursor: pointer;
    font-size: 13px;
}

.btn-close-tips:hover {
    background: rgba(2, 214, 251, 0.1);
}
/*
div:hover{
    background: #02d6fbEE;
    color: #fff;
    border-color: #fff;
}

div{
    border: 2px solid #02d6fbEE;
    color: #02d6fbEE;
    padding: 6px 8px 4px 8px;
    cursor: pointer;
    flex: 1;
    text-align: center;
}
    */

.mission-item[data-editing="true"],
.point-selected,
.point-dragging,
.error-message {
    animation: none;
}

/* Close button style */
.close-button {
    position: fixed;
    top: 5%;
    left: 3%;
    width: 32px;
    height: 32px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(2, 214, 251, 0.5);
    color: rgba(2, 214, 251, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-button:hover {
    background-color: rgba(2, 214, 251, 0.2);
    color: #fff;
    transform: scale(1.1);
}

.floating-mission-list,
.drawer-panel {
    will-change: transform, opacity;
}

button:focus,
input:focus {
    outline: 2px solid #02d6fb;
    outline-offset: 1px;
}

/* 面板缩放 */

/* 超小屏幕缩放 - 882*344, 740*360 */
@media (max-width: 900px) and (max-height: 370px) {
    .floating-mission-list {
        zoom: 0.50;
    }

    .drawer-panel {
        zoom: 0.50;
    }

    .map-tips-box {
        zoom: 0.50;
    }

    .close-button {
        zoom: 0.50;
    }
}

/* 小屏幕横屏缩放 - 896*414, 844*390 */
@media (max-width: 900px) and (min-height: 371px) and (max-height: 450px) {
    .floating-mission-list {
        zoom: 0.55;
    }

    .drawer-panel {
        zoom: 0.55;
    }

    .map-tips-box {
        zoom: 0.55;
    }

    .close-button {
        zoom: 0.55;
    }
}

/* 手机竖屏缩放 - 667*375 */
@media (max-width: 700px) and (max-height: 450px) {
    .floating-mission-list {
        zoom: 0.5;
    }

    .drawer-panel {
        zoom: 0.5;
    }

    .map-tips-box {
        zoom: 0.5;
    }

    .close-button {
        zoom: 0.5;
    }
}

/* 中等屏幕缩放 - 720*540, 1024*600 */
@media (min-width: 701px) and (max-width: 1050px) and (max-height: 650px) {
    .floating-mission-list {
        zoom: 0.75;
    }

    .drawer-panel {
        zoom: 0.75;
    }

    .map-tips-box {
        zoom: 0.75;
    }

    .close-button {
        zoom: 0.75;
    }
}

/* 大屏幕 - 1024*768, 1180*820 保持原始大小 */
@media (min-width: 1025px) {
    .floating-mission-list {
        zoom: 1;
    }

    .drawer-panel {
        zoom: 1;
    }

    .map-tips-box {
        zoom: 1;
    }

    .close-button {
        zoom: 1;
    }
}
